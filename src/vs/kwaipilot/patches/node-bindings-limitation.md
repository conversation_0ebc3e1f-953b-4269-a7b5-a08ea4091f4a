# 修复 bindings 包与 node internal 不兼容问题

问题链接: https://koncall.corp.kuaishou.com/ticket/95773

## 问题描述

通过 ssh remote 连接开发机,插件不能正常工作

## 问题判断

在 initSqlite 时, 报错

```
Could not find module root given file: "node:internal/process/task_queues". Do you have a `package.json` file? 
```

bindings 包在查找sqlite3.node 时, 没有兼容 node:internal 类型的模块,导致查找失败

https://github.com/TooTallNate/node-bindings/blob/master/bindings.js


## 修复方案

问题引入: 开启 splitting 后, 会导致 class#property 编译异常. 因此在`tsup.config.js` 中配置了`splitting: false`,

这导致打包后, bindings 执行时的调用栈改变, 无法正确定位到 sqlite3.node 模块.

但 tsup 官方明确了 cjs 模块不支持 splitting. 因此配置不能改回去

考虑patch bindings 包,来正确获取 sqlite3.node 模块

错误的情况的复现

```js
/**
 * Module dependencies.
 */

function getFileName(calling_file) {
    var origPST = Error.prepareStackTrace,
        origSTL = Error.stackTraceLimit,
        dummy = {},
        fileName;

    Error.stackTraceLimit = 10;

    Error.prepareStackTrace = function (e, st) {
        console.log(e.stack);
        for (var i = 0, l = st.length; i < l; i++) {
            fileName = st[i].getFileName();
            if (fileName !== __filename) {
                if (calling_file) {
                    if (fileName !== calling_file) {
                        return;
                    }
                } else {
                    return;
                }
            }
        }
    };

    // run the 'prepareStackTrace' function above
    Error.captureStackTrace(dummy);
    dummy.stack;

    // cleanup
    Error.prepareStackTrace = origPST;
    Error.stackTraceLimit = origSTL;
    return fileName;
}

function task() {
    console.log('PAth:', getFileName());
}

task();

```

错误打印出了`node:internal/modules/cjs/loader`

正确的打印应当是当前 js 文件的路径