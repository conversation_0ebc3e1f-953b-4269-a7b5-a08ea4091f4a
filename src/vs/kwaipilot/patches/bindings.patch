diff --git a/bindings.js b/bindings.js
index 727413a19969f773d01a2d18f8f42d892cf8fabe..ac24898ba513a8eac037af01a7d4866acc2f9c44 100644
--- a/bindings.js
+++ b/bindings.js
@@ -152,6 +152,10 @@ exports.getFileName = function getFileName(calling_file) {
 
   Error.prepareStackTrace = function(e, st) {
     for (var i = 0, l = st.length; i < l; i++) {
+      /* 见node-bindings-limitation.md */
+      if (st[i].getFileName()?.startsWith('node:internal')) {
+        continue;
+      }
       fileName = st[i].getFileName();
       if (fileName !== __filename) {
         if (calling_file) {
