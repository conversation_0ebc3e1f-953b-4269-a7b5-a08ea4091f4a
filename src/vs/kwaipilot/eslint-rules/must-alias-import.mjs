export default {
  meta: {
    type: "problem",
    docs: {
      description: "强制使用 @bridge 和 @webview 方式引入模块",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [], // 不需要额外的配置选项
  },

  create(context) {
    return {
      ImportDeclaration(node) {
        const importPath = node.source.value;

        // 检查是否包含 bridge 或 webview 关键字
        if (importPath.includes("bridge") || importPath.includes("webview")) {
          // 检查是否使用了相对路径
          if (importPath.startsWith(".")) {
            context.report({
              node,
              message: "必须使用 @bridge 或 @webview 方式引入模块，不允许使用相对路径",
              fix(fixer) {
                // 获取原始导入路径
                const originalPath = node.source.value;

                // 确定应该使用的别名
                const alias = originalPath.includes("bridge") ? "@bridge" : "@webview";

                // 提取模块的最后一部分作为新路径
                const parts = originalPath.split("/");
                const moduleName = parts[parts.length - 1];

                // 构建新的导入路径
                const newPath = `${alias}/${moduleName}`;

                // 修复导入语句
                return fixer.replaceText(node.source, `"${newPath}"`);
              }
            });
          }
        }
      }
    };
  }
};
