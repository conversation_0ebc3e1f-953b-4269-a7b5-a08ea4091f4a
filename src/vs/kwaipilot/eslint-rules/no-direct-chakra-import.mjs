export default {
  meta: {
    type: "problem",
    docs: {
      description:
				"【编码提示】需要考虑ide宿主下的渲染，请必须从 Uninon/chakra-ui 进行引入，防止直接对 ide的body操作，导致的ide崩溃",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [],
    messages: {
      noDirectChakraImport:
				"【编码提示】需要考虑ide宿主下的渲染，请必须从 Uninon/chakra-ui 进行引入，防止直接对 ide的body操作，导致的ide崩溃",
    },
  },
  create(context) {
    // 需要检查的组件列表
    const targetComponents = [
      "Tooltip",
      "Popover",
      "Menu",
      "Modal",
      "Portal",
      "AlertDialog",
      "Drwawer",
    ];

    return {
      ImportDeclaration(node) {
        // 只检查 webview-ui 下的文件，排除 Union 文件夹
        const filePath = context.getFilename();
        if (!filePath.includes("webview-ui") || filePath.includes("Union")) {
          return;
        }

        // 检查是否从 @chakra-ui/react 导入
        if (node.source.value === "@chakra-ui/react") {
          // 检查导入的组件
          node.specifiers.forEach((specifier) => {
            if (
              specifier.type === "ImportSpecifier"
              && targetComponents.includes(specifier.imported.name)
            ) {
              context.report({
                node: specifier,
                messageId: "noDirectChakraImport",
                fix(fixer) {
                  // 获取所有导入的组件
                  const imports = node.specifiers
                    .filter(s => s.type === "ImportSpecifier")
                    .map(s => s.imported.name);

                  // 分离需要修改的组件和保持原样的组件
                  const unionComponents = imports.filter(name =>
                    targetComponents.includes(name),
                  );
                  const otherComponents = imports.filter(
                    name => !targetComponents.includes(name),
                  );

                  const fixes = [];

                  // 如果有需要从 Union 导入的组件，添加新的导入语句
                  if (unionComponents.length > 0) {
                    fixes.push(
                      fixer.insertTextBefore(
                        node,
                        `import { ${unionComponents.join(", ")} } from "@/components/Union/chakra-ui";
`,
                      ),
                    );
                  }

                  // 如果还有其他组件，保留原来的导入语句，但移除已经改过的组件
                  if (otherComponents.length > 0) {
                    fixes.push(
                      fixer.replaceText(
                        node,
                        `import { ${otherComponents.join(", ")} } from "@chakra-ui/react";
`,
                      ),
                    );
                  }
                  else {
                    // 如果没有其他组件，删除整个导入语句
                    fixes.push(fixer.remove(node));
                  }

                  return fixes;
                },
              });
            }
          });
        }
      },
    };
  },
};
