export default {
  meta: {
    type: "problem",
    docs: {
      description: "【编码提示】禁止使用 dangerouslySetInnerHTML，因为它可能导致 XSS 攻击风险",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [],
    messages: {
      noDangerousHTML: "【编码提示】不要直接使用 dangerouslySetInnerHTML，请使用 useTrustedHTML hook 来安全地处理 HTML 内容",
    },
  },
  create(context) {
    return {
      JSXAttribute(node) {
        if (node.name.name === "dangerouslySetInnerHTML") {
          context.report({
            node,
            messageId: "noDangerousHTML",
            //           fix(fixer) {
            //             // 获取原始的 html 值
            //             const htmlValue = node.value.expression.properties.find(
            //               prop => prop.key.name === "__html",
            //             ).value;

            //             // 获取父级 JSX 元素的属性
            //             const parentJSXElement = node.parent;
            //             const existingProps = parentJSXElement.attributes
            //               .filter(attr => attr !== node)
            //               .map(attr => context.getSourceCode().getText(attr))
            //               .join(" ");

            //             // 构建新的代码
            //             const elementName = parentJSXElement.name.name;
            //             return fixer.replaceText(
            //               parentJSXElement,
            //               `const elementRef = useTrustedHTML(${context.getSourceCode().getText(htmlValue)});

            // return (
            //   <${elementName} ${existingProps} ref={elementRef} />
            // )`,
            //             );
            //           },
          });
        }
      },
    };
  },
};
