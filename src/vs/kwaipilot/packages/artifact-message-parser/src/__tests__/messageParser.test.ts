import { describe, expect, it } from "vitest";
import { ActionCallbackData, ParserCallbacks, StreamingMessageParser } from "../messageParser";

function parseAndRetrieveCallbackData<T extends keyof ParserCallbacks>(callbackName: T, id: string, message: string): ParserCallbacks[T] {
  let result: ParserCallbacks[T] = undefined;
  const messageParser = new StreamingMessageParser({
    callbacks: {
      [callbackName](data) {
        result = data;
      },
    },
  });
  messageParser.parse("1", message);
  return result;
}

describe("parse Artifact Tag", () => {
  it("shoule parse <KwaipilotArtifact> and its attrs", async () => {
    expect(parseAndRetrieveCallbackData(
      "onArtifactOpen",
      "1",
      `<KwaipilotArtifact id="form-component" title="Form Component"></KwaipilotArtifact>`,
    )).toEqual({
      messageId: "1",
      id: "form-component",
      title: "Form Component",
      attr: {
        id: "form-component",
        title: "Form Component",
      },
    });
  });
  it("should parse <KwaipilotArtifact> and its any attrs", () => {
    expect(parseAndRetrieveCallbackData(
      "onArtifactOpen",
      "1",
      `<KwaipilotArtifact id="form-component" title="Form Component" any-attr="value"></KwaipilotArtifact>`))
      .toEqual({
        messageId: "1",
        id: "form-component",
        title: "Form Component",
        attr: {
          "id": "form-component",
          "title": "Form Component",
          "any-attr": "value",
        },
      });
  });
});

describe("parse <ArtifactAction>", () => {
  it("should parse <ArtifactAction> and its attrs", () => {
    expect(parseAndRetrieveCallbackData("onActionOpen", "1", `
<KwaipilotArtifact id="form-component" title="Form Component" any-attr="value">
 <KwaipilotAction type="file" filePath="FormComponent.vue" resultType="file" any-attr="any-value">
 Content
 </KwaipilotAction>
</KwaipilotArtifact>
`)).toEqual({
      messageId: "1",
      actionId: "0",
      artifactId: "form-component",
      action: {
        content: "Content",
        filePath: "FormComponent.vue",
        resultType: "file",
        type: "file",
        attr: {
          "filePath": "FormComponent.vue",
          "resultType": "file",
          "type": "file",
          "any-attr": "any-value",
        },

      },
    } satisfies ActionCallbackData);
  });
});

describe("wrong cases", () => {
  it("parse wrong tag <kwaipilotArtifact>", () => {
    let executed = false;
    const messageParser = new StreamingMessageParser({
      callbacks: {
        onArtifactOpen() {
          executed = true;
        },
      },
    });
    messageParser.parse("1",
      `<kwaipilotArtifact id="form-component" title="Form Component"></kwaipilotArtifact>`,
    );
    expect(executed).toBe(false);
  });
});
