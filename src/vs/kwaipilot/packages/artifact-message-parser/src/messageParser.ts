import {
  type ActionType,
  type KwaipilotAction,
  type KwaipilotActionData,
  type FileAction,
  ResultType,
} from "./actions";
import { type KwaipilotArtifactData } from "./artifact";

export function unreachable(message: string): never {
  throw new Error(`Unreachable: ${message}`);
}

const ARTIFACT_TAG_OPEN = "<KwaipilotArtifact";
const ARTIFACT_TAG_CLOSE = "</KwaipilotArtifact>";
const ARTIFACT_ACTION_TAG_OPEN = "<KwaipilotAction";
const ARTIFACT_ACTION_TAG_CLOSE = "</KwaipilotAction>";

export interface ArtifactCallbackData extends KwaipilotArtifactData {
  messageId: string;
}

export interface ActionCallbackData {
  artifactId: string;
  messageId: string;
  actionId: string;
  action: KwaipilotAction;
}

export interface FileCallbackDataState {
  path: string;
  content: string;
  status: "start" | "stream" | "end";
}

export interface FileCallbackData {
  action: KwaipilotAction;
  data: FileCallbackDataState;
  artifactId: string;
  messageId: string;
  actionId: string;
}

export type ArtifactCallback = (data: ArtifactCallbackData) => void;
export type ActionCallback = (data: ActionCallbackData) => void;
export type FileCallback = (data: FileCallbackData) => void;

export interface ParserCallbacks {
  onArtifactOpen?: ArtifactCallback;
  onArtifactClose?: ArtifactCallback;
  onActionOpen?: ActionCallback;
  onActionClose?: ActionCallback;
  onFileStream?: FileCallback;
}

interface ElementFactoryProps {
  messageId: string;
}

type ElementFactory = (props: ElementFactoryProps) => string;

export interface StreamingMessageParserOptions {
  callbacks?: ParserCallbacks;
  artifactElement?: ElementFactory;
}

interface MessageState {
  position: number;
  insideArtifact: boolean;
  insideAction: boolean;
  currentArtifact?: KwaipilotArtifactData;
  currentAction: KwaipilotActionData;
  actionId: number;
  streamingFile: boolean;
}

export class StreamingMessageParser {
  private messages = new Map<string, MessageState>();

  constructor(private _options: StreamingMessageParserOptions = {}) {}

  parse(messageId: string, input: string) {
    let state = this.messages.get(messageId);

    if (!state) {
      state = {
        position: 0,
        insideAction: false,
        insideArtifact: false,
        currentAction: { content: "", attr: {} },
        actionId: 0,
        streamingFile: false,
      };

      this.messages.set(messageId, state);
    }

    let output = "";
    let i = state.position;
    let earlyBreak = false;

    while (i < input.length) {
      if (state.insideArtifact) {
        const currentArtifact = state.currentArtifact;

        if (currentArtifact === undefined) {
          unreachable("Artifact not initialized");
        }

        if (state.insideAction) {
          const closeIndex = input.indexOf(ARTIFACT_ACTION_TAG_CLOSE, i);

          const currentAction = state.currentAction;

          if (closeIndex !== -1) {
            currentAction.content += input.slice(i, closeIndex);

            const content = currentAction.content.trim();

            if ("type" in currentAction && currentAction.type === "file") {
              // 触发文件结束
              this._options.callbacks?.onFileStream?.({
                action: currentAction,
                data: {
                  path: currentAction.filePath,
                  content: content,
                  status: "end",
                },
                artifactId: currentArtifact.id,
                messageId,

                /**
               * We decrement the id because it's been incremented already
               * when `onActionOpen` was emitted to make sure the ids are
               * the same.
               */
                actionId: String(state.actionId - 1),
              });
            }

            currentAction.content = content;

            this._options.callbacks?.onActionClose?.({
              artifactId: currentArtifact.id,
              messageId,

              /**
               * We decrement the id because it's been incremented already
               * when `onActionOpen` was emitted to make sure the ids are
               * the same.
               */
              actionId: String(state.actionId - 1),

              action: currentAction as KwaipilotAction,
            });

            state.insideAction = false;
            state.currentAction = { content: "", attr: {} };

            i = closeIndex + ARTIFACT_ACTION_TAG_CLOSE.length;
          }
          else {
            if (
              currentAction
              && "type" in currentAction
              && currentAction.type === "file"
            ) {
              const alreadyStreaming = state.streamingFile;
              if (!alreadyStreaming) {
                state.streamingFile = true;
              }
              const filepath = currentAction.filePath;
              this._options.callbacks?.onFileStream?.({
                action: currentAction,
                data: {
                  path: filepath,
                  content: input
                    .slice(i, -ARTIFACT_ACTION_TAG_CLOSE.length)
                    .trim(),
                  status: alreadyStreaming ? "stream" : "start",
                },
                artifactId: currentArtifact.id,
                messageId,

                /**
               * We decrement the id because it's been incremented already
               * when `onActionOpen` was emitted to make sure the ids are
               * the same.
               */
                actionId: String(state.actionId - 1),
              });
            }
            break;
          }
        }
        else {
          const actionOpenIndex = input.indexOf(ARTIFACT_ACTION_TAG_OPEN, i);
          const artifactCloseIndex = input.indexOf(ARTIFACT_TAG_CLOSE, i);

          if (
            actionOpenIndex !== -1
            && (artifactCloseIndex === -1 || actionOpenIndex < artifactCloseIndex)
          ) {
            const actionEndIndex = input.indexOf(">", actionOpenIndex);

            if (actionEndIndex !== -1) {
              state.insideAction = true;

              state.currentAction = this.parseActionTag(
                input,
                actionOpenIndex,
                actionEndIndex,
              );

              this._options.callbacks?.onActionOpen?.({
                artifactId: currentArtifact.id,
                messageId,
                actionId: String(state.actionId++),
                action: state.currentAction as KwaipilotAction,
              });

              i = actionEndIndex + 1;
            }
            else {
              break;
            }
          }
          else if (artifactCloseIndex !== -1) {
            this._options.callbacks?.onArtifactClose?.({
              messageId,
              ...currentArtifact,
            });

            state.insideArtifact = false;
            state.currentArtifact = undefined;

            i = artifactCloseIndex + ARTIFACT_TAG_CLOSE.length;
          }
          else {
            break;
          }
        }
      }
      else if (input[i] === "<" && input[i + 1] !== "/") {
        let j = i;
        let potentialTag = "";

        while (
          j < input.length
          && potentialTag.length < ARTIFACT_TAG_OPEN.length
        ) {
          potentialTag += input[j];

          if (potentialTag === ARTIFACT_TAG_OPEN) {
            const nextChar = input[j + 1];

            if (nextChar && nextChar !== ">" && nextChar !== " ") {
              output += input.slice(i, j + 1);
              i = j + 1;
              break;
            }

            const openTagEnd = input.indexOf(">", j);

            if (openTagEnd !== -1) {
              const artifactTag = input.slice(i, openTagEnd + 1);
              const attr = this.extractAllAttributes(artifactTag);

              const artifactTitle = this.extractAttribute(
                artifactTag,
                "title",
              ) as string;
              const artifactId = this.extractAttribute(
                artifactTag,
                "id",
              ) as string;

              if (!artifactTitle) {
                console.warn("Artifact title missing");
              }

              if (!artifactId) {
                console.warn("Artifact id missing");
              }

              state.insideArtifact = true;

              const currentArtifact = {
                id: artifactId,
                title: artifactTitle,
                attr,
              } satisfies KwaipilotArtifactData;

              state.currentArtifact = currentArtifact;

              this._options.callbacks?.onArtifactOpen?.({
                messageId,
                ...currentArtifact,
              });

              const artifactFactory
                = this._options.artifactElement ?? createArtifactElement;

              output += artifactFactory({ messageId });

              i = openTagEnd + 1;
            }
            else {
              earlyBreak = true;
            }

            break;
          }
          else if (!ARTIFACT_TAG_OPEN.startsWith(potentialTag)) {
            output += input.slice(i, j + 1);
            i = j + 1;
            break;
          }

          j++;
        }

        if (j === input.length && ARTIFACT_TAG_OPEN.startsWith(potentialTag)) {
          break;
        }
      }
      else {
        output += input[i];
        i++;
      }

      if (earlyBreak) {
        break;
      }
    }

    state.position = i;

    return output;
  }

  reset() {
    this.messages.clear();
  }

  private parseActionTag(
    input: string,
    actionOpenIndex: number,
    actionEndIndex: number,
  ) {
    const actionTag = input.slice(actionOpenIndex, actionEndIndex + 1);

    const actionType = this.extractAttribute(actionTag, "type") as ActionType;

    const resultType = this.extractAttribute(actionTag, "resultType") as ResultType;

    const attr = this.extractAllAttributes(actionTag);

    const actionAttributes = {
      type: actionType,
      content: "",
      resultType,
      attr,
    };

    if (actionType === "file") {
      if (!("filePath" in attr)) {
        console.debug("File path not specified");
      }

      (actionAttributes as FileAction).filePath = attr.filePath;
    }
    else if (actionType === "result") {
      const resultType = this.extractAttribute(actionTag, "resultType") as ResultType;
      const filePath = this.extractAttribute(actionTag, "filePath") as string;
      (actionAttributes as FileAction).resultType = resultType;
      (actionAttributes as FileAction).filePath = filePath;
    }
    else if (actionType !== "shell") {
      console.warn(`Unknown action type '${actionType}'`);
    }

    return actionAttributes as FileAction;
  }

  private extractAttribute(tag: string, attributeName: string): string | undefined {
    const match = tag.match(new RegExp(`${attributeName}="([^"]*)"`, "i"));
    return match ? match[1] : undefined;
  }

  private extractAllAttributes(tag: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    const regex = /(\w+-?\w*)="(.*?)"/g;
    let match;

    while ((match = regex.exec(tag)) !== null) {
      const attrName = match[1]; // 属性名
      const attrValue = match[2]; // 属性值
      attributes[attrName] = attrValue; // 存储到对象中
    }
    return attributes;
  }
}

const createArtifactElement: ElementFactory = (props) => {
  const elementProps = [
    "class=\"__KwaipilotArtifact__\"",
    ...Object.entries(props).map(([key, value]) => {
      return `data-${camelToDashCase(key)}=${JSON.stringify(value)}`;
    }),
  ];

  return `<div ${elementProps.join(" ")}></div>`;
};

function camelToDashCase(input: string) {
  return input.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
}
