export interface Doc {
  cosmoId: string;
  id: number;
  intersection: boolean;
  name: string;
  urls: string[];
}

export type IChatModelType =
  | "kwaipilot_turbo_128k"
  | "kwaipilot_pro_64k"
  | "kwaipilot_pro_32k"
  | "GPT4"
  | "CLAUDE_3" | "GPT4O";

export interface Model {
  desc: string;
  disabled: boolean;
  disabledIcon: string;
  icon: string;
  maxLength: string;
  modelType: IChatModelType;
  name: string;
  vip: boolean;
  tooltip?: string;
  vipIcon?: string;
}
