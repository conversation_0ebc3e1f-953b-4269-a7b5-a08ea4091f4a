import { Observable } from "rxjs";
import { RangeData } from "../CustomVariable/cody-shared/range";
import { McpServerChangeEventDetail } from "../mcp/types";
import { SettingPage } from "../customSettingPanel";
import { UserInfo } from "../misc";
import { StateReturnType } from "../state-manager/types";

export type RequestMessage =
  | {
    /**
           * If defined, this request is expecting an AsyncIterator (multiple emitted values) in response.
           * The streamId is a unique and opaque identifier that all responses will be associated with so
           * that the caller can associate them with this request.
           *
           * If `undefined`, the request is expecting a Promise (single emitted value), and no stream is
           * needed.
           */
    streamId?: string;

    /**
           * The name of the method to invoke.
           */
    method: string;

    /**
           * The method arguments.
           */
    args: unknown[];
  }
  | {
    /** The streamId to abort.* */
    streamIdToAbort: string;
  };

export interface ResponseMessage {
  /**
        * If defined, this response is an emitted value (or error/completion event) from a request that
        * expects an AsyncIterator (multiple emitted values). All responses to that request use the same
        * `streamId` as the request so they can be associated with it.
        *
        * If `undefined`, this response is a single value (like a Promise).
        */
  streamId?: string;

  streamEvent?: "next" | "error" | "complete";

  /**
        * For non-stream responses or for `next`/`error` stream events, the data.
        */
  data?: unknown;
}

export interface SelectionOrFileContext {
  uri: string;
  relativePath: string;
  content: string;
  range?: RangeData;
}

export interface LatestCopiedContent {
  documentUri: string;
  relativePath: string;
  ranges: readonly RangeData[];
  plainText: string;
}
export type IndexState = {
  indexing: boolean;
  indexingProgress: number;
  indexed: boolean;
  indexingMessage: string;
  lastBuildTime: string;
  pauseIndexManual: boolean;
  status: "paused" | "indexing" | "indexed" | "error";
};

export interface ObservableAPI {
  currentFileAndSelection: () => Observable<SelectionOrFileContext | null>;
  visibility: () => Observable<boolean>;
  currentTheme: () => Observable<"light" | "dark">;
  isDeveloperMode: () => Observable<boolean>;
  latestCopiedContent: () => Observable<LatestCopiedContent | null>;
  indexState: () => Observable<IndexState>;
  mcpServers: () => Observable<McpServerChangeEventDetail>;
  customPanelPage: () => Observable<SettingPage>;
  rulesList: () => Observable<string[]>;
  userInfo: () => Observable<UserInfo | undefined>;
  settingUpdate: <K extends keyof StateReturnType["config"]>() => Observable<{
    key: K;
    value: StateReturnType["config"][K];
  }>;

}
