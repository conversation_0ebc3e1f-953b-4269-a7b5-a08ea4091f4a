/* eslint-disable no-prototype-builtins */
/* ---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *-------------------------------------------------------------------------------------------- */

import { IMessagePassingProtocol } from "./ipc.js";
import { CanceledLazyPromise, LazyPromise } from "./lazyPromise.js";
import { getStringIdentifierForProxy, IRPCProtocol, Proxied, ProxyIdentifier } from "./proxyIdentifier.js";

/**
 * shared 会被 webview 引用，所以不能直接依赖 vscode 的 Disposable
 */
interface IDisposable {
  dispose(): void;
}

const DollarSign = 36;

export interface JSONStringifyReplacer {
  (key: string, value: any): any;
}

export const enum RequestInitiator {
  LocalSide = 0,
  OtherSide = 1,
}

export const enum ResponsiveState {
  Responsive = 0,
  Unresponsive = 1,
}

export interface IRPCProtocolLogger {
  logIncoming(req: number, initiator: RequestInitiator, str: string, data?: any): void;
  logOutgoing(req: number, initiator: RequestInitiator, str: string, data?: any): void;
}

const noop = () => { };

const _RPCProtocolSymbol = Symbol.for("rpcProtocol");
const _RPCProxySymbol = Symbol.for("rpcProxy");

export class RPCProtocol implements IRPCProtocol, IDisposable {
  [_RPCProtocolSymbol] = true;

  private static readonly UNRESPONSIVE_TIME = 3 * 1000; // 3s

  private readonly _protocol: IMessagePassingProtocol;
  private readonly _logger: IRPCProtocolLogger | null;
  private _isDisposed: boolean;
  private readonly _locals: any[];
  private readonly _proxies: any[];
  private _lastMessageId: number;
  private readonly _cancelInvokedHandlers: { [req: string]: () => void };
  private readonly _pendingRPCReplies: { [msgId: string]: PendingRPCReply };

  disposables: IDisposable[] = [];

  constructor(protocol: IMessagePassingProtocol, logger: IRPCProtocolLogger | null = null) {
    this._protocol = protocol;
    this._logger = logger;
    this._isDisposed = false;
    this._locals = [];
    this._proxies = [];
    for (let i = 0, len = ProxyIdentifier.count; i < len; i++) {
      this._locals[i] = null;
      this._proxies[i] = null;
    }
    this._lastMessageId = 0;
    this._cancelInvokedHandlers = Object.create(null);
    this._pendingRPCReplies = {};
    this.disposables.push({
      dispose: this._protocol.onMessage(msg => this._receiveOneMessage(msg)),
    });
  }

  public dispose(): void {
    this._isDisposed = true;

    // Release all outstanding promises with a canceled error
    Object.keys(this._pendingRPCReplies).forEach((msgId) => {
      const pending = this._pendingRPCReplies[msgId];
      delete this._pendingRPCReplies[msgId];
      pending.resolveErr(new Error("RPC Canceled"));
    });
  }

  public drain(): Promise<void> {
    return Promise.resolve();
  }

  private _onWillSendRequest(_req: number): void {
  }

  private _onDidReceiveAcknowledge(_req: number): void {
  }

  public getProxy<T>(identifier: ProxyIdentifier<T>): Proxied<T> {
    const { nid: rpcId, sid } = identifier;
    if (!this._proxies[rpcId]) {
      this._proxies[rpcId] = this._createProxy(rpcId, sid);
    }
    return this._proxies[rpcId];
  }

  private _createProxy<T>(rpcId: number, debugName: string): T {
    const handler = {
      get: (target: any, name: PropertyKey) => {
        if (typeof name === "string" && !target[name] && name.charCodeAt(0) === DollarSign) {
          target[name] = (...myArgs: any[]) => {
            return this._remoteCall(rpcId, name, myArgs);
          };
        }
        if (name === _RPCProxySymbol) {
          return debugName;
        }
        return target[name];
      },
    };
    return new Proxy(Object.create(null), handler);
  }

  public set<T, R extends T>(identifier: ProxyIdentifier<T>, value: R): R {
    this._locals[identifier.nid] = value;
    return value;
  }

  public assertRegistered(identifiers: ProxyIdentifier<any>[]): void {
    for (let i = 0, len = identifiers.length; i < len; i++) {
      const identifier = identifiers[i];
      if (!this._locals[identifier.nid]) {
        throw new Error(`Missing proxy instance ${identifier.sid}`);
      }
    }
  }

  private _receiveOneMessage(rawmsg: MessageJSONStructure): void {
    if (this._isDisposed) {
      return;
    }

    const messageType = rawmsg.type;
    const req = rawmsg.req;

    switch (messageType) {
      case MessageType.RequestJSONArgs:
      case MessageType.RequestJSONArgsWithCancellation: {
        // eslint-disable-next-line prefer-const
        let { rpcId, method, args } = rawmsg;
        this._receiveRequest(req, rpcId, method, args, (messageType === MessageType.RequestJSONArgsWithCancellation));
        break;
      }
      case MessageType.Acknowledged: {
        this._logger?.logIncoming(req, RequestInitiator.LocalSide, `ack`);
        this._onDidReceiveAcknowledge(req);
        break;
      }
      case MessageType.Cancel: {
        this._receiveCancel(req);
        break;
      }
      case MessageType.ReplyOKEmpty: {
        this._receiveReply(req, undefined);
        break;
      }
      case MessageType.ReplyOKJSON: {
        this._receiveReply(req, rawmsg.res);
        break;
      }
      case MessageType.ReplyErrError: {
        this._receiveReplyErr(req, rawmsg.err);
        break;
      }
      case MessageType.ReplyErrEmpty: {
        this._receiveReplyErr(req, undefined);
        break;
      }
      default:
        console.error(`received unexpected message`);
        console.error(rawmsg);
    }
  }

  private _receiveRequest(req: number, rpcId: number, method: string, args: any[], usesCancellationToken: boolean): void {
    this._logger?.logIncoming(req, RequestInitiator.OtherSide, `receiveRequest ${getStringIdentifierForProxy(rpcId)}.${method}(`, args);
    const callId = String(req);

    let promise: Promise<any>;
    let cancel: () => void;
    if (usesCancellationToken) {
      throw new Error("Cancellation is not supported");
    }
    else {
      // cannot be cancelled
      promise = this._invokeHandler(rpcId, method, args);
      cancel = noop;
    }

    this._cancelInvokedHandlers[callId] = cancel;

    // Acknowledge the request
    const msg = MessageIO.serializeAcknowledged(req);
    this._logger?.logOutgoing(req, RequestInitiator.OtherSide, `ack`);
    this._protocol.send(msg);

    promise.then((r) => {
      delete this._cancelInvokedHandlers[callId];
      const msg = MessageIO.serializeReplyOK(req, r);
      this._logger?.logOutgoing(req, RequestInitiator.OtherSide, `reply:`, r);
      this._protocol.send(msg);
    }, (err) => {
      delete this._cancelInvokedHandlers[callId];
      const msg = MessageIO.serializeReplyErr(req, err);
      this._logger?.logOutgoing(req, RequestInitiator.OtherSide, `replyErr:`, err);
      this._protocol.send(msg);
    });
  }

  private _receiveCancel(req: number): void {
    this._logger?.logIncoming(req, RequestInitiator.OtherSide, `receiveCancel`);
    const callId = String(req);
    this._cancelInvokedHandlers[callId]?.();
  }

  private _receiveReply(req: number, value: any): void {
    this._logger?.logIncoming(req, RequestInitiator.LocalSide, `receiveReply:`, value);
    const callId = String(req);
    if (!this._pendingRPCReplies.hasOwnProperty(callId)) {
      return;
    }

    const pendingReply = this._pendingRPCReplies[callId];
    delete this._pendingRPCReplies[callId];

    pendingReply.resolveOk(value);
  }

  private _receiveReplyErr(req: number, value: any): void {
    this._logger?.logIncoming(req, RequestInitiator.LocalSide, `receiveReplyErr:`, value);

    const callId = String(req);
    if (!this._pendingRPCReplies.hasOwnProperty(callId)) {
      return;
    }

    const pendingReply = this._pendingRPCReplies[callId];
    delete this._pendingRPCReplies[callId];

    let err: any = undefined;
    if (value) {
      if (value.$isError) {
        err = new Error();
        err.name = value.name;
        err.message = value.message;
        err.stack = value.stack;
      }
      else {
        err = value;
      }
    }
    pendingReply.resolveErr(err);
  }

  private _invokeHandler(rpcId: number, methodName: string, args: any[]): Promise<any> {
    try {
      return Promise.resolve(this._doInvokeHandler(rpcId, methodName, args));
    }
    catch (err) {
      return Promise.reject(err);
    }
  }

  private _doInvokeHandler(rpcId: number, methodName: string, args: any[]): any {
    const actor = this._locals[rpcId];
    if (!actor) {
      throw new Error("Unknown actor " + getStringIdentifierForProxy(rpcId));
    }
    const method = actor[methodName];
    if (typeof method !== "function") {
      throw new Error("Unknown method " + methodName + " on actor " + getStringIdentifierForProxy(rpcId));
    }
    return method.apply(actor, args);
  }

  private _remoteCall(rpcId: number, methodName: string, args: any[]): Promise<any> {
    if (this._isDisposed) {
      return new CanceledLazyPromise();
    }

    const req = ++this._lastMessageId;
    const callId = String(req);
    const result = new LazyPromise();

    const disposables: IDisposable[] = [];

    this._pendingRPCReplies[callId] = new PendingRPCReply(result, disposables);
    this._onWillSendRequest(req);
    const msg = MessageIO.serializeRequest(req, rpcId, methodName, args, false);
    this._logger?.logOutgoing(req, RequestInitiator.LocalSide, `request: ${getStringIdentifierForProxy(rpcId)}.${methodName}(`, args);
    this._protocol.send(msg);
    return result;
  }
}

class PendingRPCReply {
  constructor(
    private readonly _promise: LazyPromise,
    private readonly _disposables: IDisposable[],
  ) { }

  public resolveOk(value: any): void {
    this._promise.resolveOk(value);
    this._disposables.forEach(d => d.dispose());
  }

  public resolveErr(err: any): void {
    this._promise.resolveErr(err);
    this._disposables.forEach(d => d.dispose());
  }
}

class MessageIO {
  public static serializeRequest(req: number, rpcId: number, method: string, args: any[], usesCancellationToken: boolean): MessageJSONStructure_RequestArg {
    return {
      type: usesCancellationToken ? MessageType.RequestJSONArgsWithCancellation : MessageType.RequestJSONArgs,
      req,
      rpcId,
      method,
      args,
    };
  }

  public static deserializeRequestMixedArgs(buff: MessageJSONStructure): { rpcId: number; method: string; args: any[] } {
    const rpcId = (buff as MessageJSONStructure_RequestArg).rpcId;
    const method = (buff as MessageJSONStructure_RequestArg).method;
    const rawargs = (buff as MessageJSONStructure_RequestArg).args;
    const args: any[] = new Array(rawargs.length);
    for (let i = 0, len = rawargs.length; i < len; i++) {
      const rawarg = rawargs[i];
      if (typeof rawarg === "string") {
        args[i] = JSON.parse(rawarg);
      }
      else {
        args[i] = rawarg;
      }
    }
    return {
      rpcId: rpcId,
      method: method,
      args: args,
    };
  }

  public static serializeAcknowledged(req: number): MessageJSONStructure {
    return {
      type: MessageType.Acknowledged,
      req,
    };
  }

  public static serializeCancel(req: number): MessageJSONStructure {
    return {
      type: MessageType.Cancel,
      req,
    };
  }

  public static serializeReplyOK(req: number, res: any): MessageJsonStructure_ReplyOKJSON | MessageJsonStructure_ReplyOKEmpty {
    if (typeof res === "undefined") {
      return this._serializeReplyOKEmpty(req);
    }
    else {
      return this._serializeReplyOKJSON(req, res);
    }
  }

  private static _serializeReplyOKEmpty(req: number): MessageJsonStructure_ReplyOKEmpty {
    return {
      type: MessageType.ReplyOKEmpty,
      req,
    };
  }

  private static _serializeReplyOKJSON(req: number, res: string): MessageJsonStructure_ReplyOKJSON {
    return {
      type: MessageType.ReplyOKJSON,
      req,
      res,
    };
  }

  public static serializeReplyErr(req: number, err: any): MessageJsonStructure_ReplyErrError | MessageJsonStructure_ReplyErrEmpty {
    // TODO: transformErrorForSerialization
    const errStr: string | undefined = String(err);
    if (!errStr) {
      return this._serializeReplyErrEmpty(req);
    }
    return {
      type: MessageType.ReplyErrError,
      err: errStr,
      req,
    };
  }

  private static _serializeReplyErrEmpty(req: number): MessageJsonStructure_ReplyErrEmpty {
    return {
      type: MessageType.ReplyErrEmpty,
      req,
    };
  }
}

const enum MessageType {
  RequestJSONArgs = 1,
  RequestJSONArgsWithCancellation = 2,
  Acknowledged = 5,
  Cancel = 6,
  ReplyOKEmpty = 7,
  ReplyOKJSON = 9,
  ReplyOKJSONWithBuffers = 10,
  ReplyErrError = 11,
  ReplyErrEmpty = 12,
}

export interface MessageJSONStructure_RequestArg {
  type: MessageType.RequestJSONArgs
    | MessageType.RequestJSONArgsWithCancellation;
  req: number;
  rpcId: number;
  method: string;
  args: any[];
}

export interface MessageJsonStructure_ReplyOKJSON {
  type: MessageType.ReplyOKJSON;
  req: number;
  res: any;
}

export interface MessageJsonStructure_ReplyOKEmpty {
  type: MessageType.ReplyOKEmpty;
  req: number;
}

export interface MessageJsonStructure_ReplyErrError {
  type: MessageType.ReplyErrError;
  req: number;
  err: any;
}

export interface MessageJsonStructure_ReplyErrEmpty {
  type: MessageType.ReplyErrEmpty;
  req: number;
}

export interface MessageJsonStructure_Acknowledged {
  type: MessageType.Acknowledged;
  req: number;
}

export interface MessageJsonStructure_Cancel {
  type: MessageType.Cancel;
  req: number;
}

export type MessageJSONStructure = MessageJSONStructure_RequestArg
  | MessageJsonStructure_ReplyOKJSON
  | MessageJsonStructure_ReplyOKEmpty
  | MessageJsonStructure_ReplyErrEmpty
  | MessageJsonStructure_ReplyErrError
  | MessageJsonStructure_Acknowledged
  | MessageJsonStructure_Cancel;
