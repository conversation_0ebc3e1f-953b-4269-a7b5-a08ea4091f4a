# observable spec

## background 

常规 bridge 的通信途径：

1. webview 发送 request
2. native 接受 request
3. native 异步执行任务
4. native 发送 response

典型的单步异步流

但有一些场景用这个模型很难实现，例如：
webview 需要实时监听文档的 selection change 事件。

用上面的通信模型实现，需要从 native 发送到 webivew。但 native 并不知道 webview 什么时候需要监听什么时候不需要

会造成额外的资源浪费。因此用 observable 解决这个场景的通信，observable 可以 multicast（share），可以实现在没有 subscriber 时自动 dispose


## messaging

1. webview 订阅
2. native observable 启动
3. native 发送消息 `subscriber.next(1)`
4. native 发送消息 `subscriber.next(2)`
5. native 发送消息 `subscriber.next(3)`
6. webview 在不需要时，取消订阅 `subscription.cancel()`
