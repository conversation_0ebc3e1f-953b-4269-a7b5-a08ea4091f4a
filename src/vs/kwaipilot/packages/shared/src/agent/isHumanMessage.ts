import { InternalLocalMessage, InternalLocalMessage_Human } from "./types";
import { LocalMessage } from "./types_copied_from_agent";

/**
 * 判断一条 message 是否是用户输入问题
 * @param message
 * @returns
 */
export function isHumanMessage(message: InternalLocalMessage): message is InternalLocalMessage_Human;
export function isHumanMessage(message: LocalMessage): boolean;
export function isHumanMessage(message: InternalLocalMessage | LocalMessage): boolean {
  return message.type === "say" && message.say === "text" && message.role === "user";
}
