import { SerializedTextNode, Spread } from "lexical";
import { RichEditorBoxPanelData } from "../richEditor/const";
import { CommandType } from "../misc";

export function isMentionNode(node: unknown): node is SerializedMentionNode {
  return Boolean(typeof node === "object" && node && "type" in node && node.type === "mention");
}

export type SerializedMentionNode = Spread<
  SerializedTextNode & {
    type: string;
  },
  RichEditorBoxPanelData
>;

export function isSlashCommandMentionNode(node: SerializedMentionNode): node is SerializedMentionNode {
  return node.type === "mention" && node.commandType === CommandType.SLASH;
}

export function isSharpCommandMentionNode(node: SerializedMentionNode): boolean {
  return node.type === "mention" && node.commandType === CommandType.SHARP;
}
