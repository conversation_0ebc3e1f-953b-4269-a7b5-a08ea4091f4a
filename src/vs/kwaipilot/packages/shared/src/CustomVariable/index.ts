import { CONTEXT_ITEM_MENTION_NODE_TYPE, SerializedCustomVariableNode } from "./nodes";

export interface CustomPromptData {
  id: number;
  name: string;
  content: string;
  owner_id: string;
}

export function isCustomVariableNode(node: unknown): node is SerializedCustomVariableNode {
  return Boolean(typeof node === "object" && node && "type" in node && node.type === CONTEXT_ITEM_MENTION_NODE_TYPE);
}
