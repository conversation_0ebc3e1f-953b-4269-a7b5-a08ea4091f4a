import "nunjucks";

declare module "nunjucks" {

  export namespace parser {
    interface IParser {
      extensions: Extension[];
      tokens: any;
      peeked: any;
      breakOnBlocks: string[] | null;
      dropLeadingWhitespace: boolean;

      init(tokens: any): void;
      nextToken(withWhitespace?: boolean): any;
      peekToken(): any;
      pushToken(tok: any): void;
      error(msg: string, lineno?: number, colno?: number): lib.TemplateError;
      fail(msg: string, lineno?: number, colno?: number): never;
      skip(type: number): boolean;
      expect(type: number): any;
      skipValue(type: number, val: string): boolean;
      skipSymbol(val: string): boolean;
      advanceAfterBlockEnd(name?: string): any;
      advanceAfterVariableEnd(): void;
      parseFor(): nodes.For | nodes.AsyncEach | nodes.AsyncAll;
      parseMacro(): nodes.Macro;
      parseCall(): nodes.Output;
      parseWithContext(): boolean | null;
      parseImport(): nodes.Import;
      parseFrom(): nodes.FromImport;
      parseBlock(): nodes.Block;
      parseExtends(): nodes.Extends;
      parseInclude(): nodes.Include;
      parseIf(): nodes.If | nodes.IfAsync;
      parseSet(): nodes.Set;
      parseSwitch(): nodes.Switch;
      parseStatement(): nodes.Node | null;
      parseRaw(tagName?: string): nodes.Output;
      parsePostfix(node: nodes.Node): nodes.Node;
      parseExpression(): nodes.Node;
      parseInlineIf(): nodes.Node;
      parseOr(): nodes.Node;
      parseAnd(): nodes.Node;
      parseNot(): nodes.Node;
      parseIn(): nodes.Node;
      parseIs(): nodes.Node;
      parseCompare(): nodes.Node;
      parseConcat(): nodes.Node;
      parseAdd(): nodes.Node;
      parseSub(): nodes.Node;
      parseMul(): nodes.Node;
      parseDiv(): nodes.Node;
      parseFloorDiv(): nodes.Node;
      parseMod(): nodes.Node;
      parsePow(): nodes.Node;
      parseUnary(noFilters?: boolean): nodes.Node;
      parsePrimary(noPostfix?: boolean): nodes.Node;
      parseFilterName(): nodes.Symbol;
      parseFilterArgs(node: nodes.Node): nodes.Node[];
      parseFilter(node: nodes.Node): nodes.Node;
      parseFilterStatement(): nodes.Output;
      parseAggregate(): nodes.Node | null;
      parseSignature(tolerant?: boolean, noParens?: boolean): nodes.NodeList | null;
      parseUntilBlocks(...blockNames: string[]): nodes.NodeList;
      parseNodes(): nodes.Node[];
      parse(): nodes.NodeList;
      parseAsRoot(): nodes.Root;
    }

    class Parser implements IParser {
      constructor(tokens: any);
      extensions: Extension[];
      tokens: any;
      peeked: any;
      breakOnBlocks: string[] | null;
      dropLeadingWhitespace: boolean;

      init(tokens: any): void;
      nextToken(withWhitespace?: boolean): any;
      peekToken(): any;
      pushToken(tok: any): void;
      error(msg: string, lineno?: number, colno?: number): lib.TemplateError;
      fail(msg: string, lineno?: number, colno?: number): never;
      skip(type: number): boolean;
      expect(type: number): any;
      skipValue(type: number, val: string): boolean;
      skipSymbol(val: string): boolean;
      advanceAfterBlockEnd(name?: string): any;
      advanceAfterVariableEnd(): void;
      parseFor(): nodes.For | nodes.AsyncEach | nodes.AsyncAll;
      parseMacro(): nodes.Macro;
      parseCall(): nodes.Output;
      parseWithContext(): boolean | null;
      parseImport(): nodes.Import;
      parseFrom(): nodes.FromImport;
      parseBlock(): nodes.Block;
      parseExtends(): nodes.Extends;
      parseInclude(): nodes.Include;
      parseIf(): nodes.If | nodes.IfAsync;
      parseSet(): nodes.Set;
      parseSwitch(): nodes.Switch;
      parseStatement(): nodes.Node | null;
      parseRaw(tagName?: string): nodes.Output;
      parsePostfix(node: nodes.Node): nodes.Node;
      parseExpression(): nodes.Node;
      parseInlineIf(): nodes.Node;
      parseOr(): nodes.Node;
      parseAnd(): nodes.Node;
      parseNot(): nodes.Node;
      parseIn(): nodes.Node;
      parseIs(): nodes.Node;
      parseCompare(): nodes.Node;
      parseConcat(): nodes.Node;
      parseAdd(): nodes.Node;
      parseSub(): nodes.Node;
      parseMul(): nodes.Node;
      parseDiv(): nodes.Node;
      parseFloorDiv(): nodes.Node;
      parseMod(): nodes.Node;
      parsePow(): nodes.Node;
      parseUnary(noFilters?: boolean): nodes.Node;
      parsePrimary(noPostfix?: boolean): nodes.Node;
      parseFilterName(): nodes.Symbol;
      parseFilterArgs(node: nodes.Node): nodes.Node[];
      parseFilter(node: nodes.Node): nodes.Node;
      parseFilterStatement(): nodes.Output;
      parseAggregate(): nodes.Node | null;
      parseSignature(tolerant?: boolean, noParens?: boolean): nodes.NodeList | null;
      parseUntilBlocks(...blockNames: string[]): nodes.NodeList;
      parseNodes(): nodes.Node[];
      parse(): nodes.NodeList;
      parseAsRoot(): nodes.Root;
    }

    function parse(src: string, extensions?: Extension[], opts?: any): nodes.Root;
  }

  export namespace nodes {
    class Node {
      lineno: number;
      colno: number;
      fields: string[];
      typename: string;

      init(lineno: number, colno: number, ...args: any[]): void;
      findAll(type: typeof Node, results?: Node[]): Node[];
      iterFields(func: (val: any, fieldName: string) => void): void;
    }

    class Value extends Node {
      value: any;
    }

    class NodeList extends Node {
      children: Node[];
      addChild(node: Node): void;
    }

    class Root extends NodeList {}
    class Literal extends Value {}
    class Symbol extends Value {}
    class Group extends NodeList {}
    class Array extends NodeList {}

    class Pair extends Node {
      key: Node;
      value: Node;
    }

    class Dict extends NodeList {}

    class LookupVal extends Node {
      target: Node;
      val: Node;
    }

    class If extends Node {
      cond: Node;
      body: Node;
      else_: Node;
    }

    class IfAsync extends If {}

    class InlineIf extends Node {
      cond: Node;
      body: Node;
      else_: Node;
    }

    class For extends Node {
      arr: Node;
      name: Node;
      body: Node;
      else_: Node;
    }

    class AsyncEach extends For {}
    class AsyncAll extends For {}

    class Macro extends Node {
      name: Node;
      args: Node;
      body: Node;
    }

    class Caller extends Macro {}

    class Import extends Node {
      template: Node;
      target: Node;
      withContext: boolean;
    }

    class FromImport extends Node {
      template: Node;
      names: NodeList;
      withContext: boolean;
    }

    class FunCall extends Node {
      name: Node;
      args: Node[];
    }

    class Filter extends FunCall {}

    class FilterAsync extends Filter {
      symbol: Node;
    }

    class KeywordArgs extends Dict {}

    class Block extends Node {
      name: Node;
      body: Node;
    }

    class Super extends Node {
      blockName: Node;
      symbol: Node;
    }

    class TemplateRef extends Node {
      template: Node;
    }

    class Extends extends TemplateRef {}

    class Include extends Node {
      template: Node;
      ignoreMissing: boolean;
    }

    class Set extends Node {
      targets: Node[];
      value: Node;
    }

    class Switch extends Node {
      expr: Node;
      cases: Node[];
      default: Node;
    }

    class Case extends Node {
      cond: Node;
      body: Node;
    }

    class Output extends NodeList {}
    class Capture extends Node {
      body: Node;
    }
    class TemplateData extends Literal {}

    class UnaryOp extends Node {
      target: Node;
    }

    class BinOp extends Node {
      left: Node;
      right: Node;
    }

    class In extends BinOp {}
    class Is extends BinOp {}
    class Or extends BinOp {}
    class And extends BinOp {}
    class Not extends UnaryOp {}
    class Add extends BinOp {}
    class Concat extends BinOp {}
    class Sub extends BinOp {}
    class Mul extends BinOp {}
    class Div extends BinOp {}
    class FloorDiv extends BinOp {}
    class Mod extends BinOp {}
    class Pow extends BinOp {}
    class Neg extends UnaryOp {}
    class Pos extends UnaryOp {}

    class Compare extends Node {
      expr: Node;
      ops: CompareOperand[];
    }

    class CompareOperand extends Node {
      expr: Node;
      type: string;
    }

    class CallExtension extends Node {
      extName: string;
      prop: string;
      args: NodeList;
      contentArgs: Node[];
      autoescape: boolean;
    }

    class CallExtensionAsync extends CallExtension {}

    function printNodes(node: Node, indent?: number): void;
  }

}
