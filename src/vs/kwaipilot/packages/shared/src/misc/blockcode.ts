import { SymbolKind } from "vscode";

export interface InputBlockCodePath {
  filepath: string;
  startLine?: number;
  endLine?: number;
  range?: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
}

export interface OutPutBlockCodePath {
  filepath: string;
  startLine?: number;
  endLine?: number;
  type: "path" | "code";
  symbolKind?: SymbolKind;
  range?: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
}

export interface Point {
  /**
     * Line in a source file (1-indexed integer).
     */
  line: number;

  /**
     * Column in a source file (1-indexed integer).
     */
  column: number;
  /**
     * Character in a source file (0-indexed integer).
     */
  offset?: number | undefined;
}

/**
 * Position of a node in a source document.
 *
 * A position is a range between two points.
 */
export interface Position {
  /**
     * Place of the first character of the parsed source region.
     */
  start: Point;

  /**
     * Place of the first character after the parsed source region.
     */
  end: Point;
}
