const { exec } = require("child_process");
const pkg = require('../package.json');

// 获取命令行参数
const args = process.argv.slice(2);
const isPreRelease = args.includes('--pre-release');

const version = pkg.version;
const preReleaseFlag = isPreRelease ? ' --pre-release' : '';

const command = `npx vsce publish${preReleaseFlag} --no-dependencies --packagePath ./kwaipilot-darwin-arm64-${version}.vsix ./kwaipilot-darwin-x64-${version}.vsix ./kwaipilot-linux-arm64-${version}.vsix ./kwaipilot-linux-x64-${version}.vsix ./kwaipilot-win32-x64-${version}.vsix`;

console.log(command);
exec(command, (error) => {
  if (error) {
    throw error;
  }
  console.log(
    `vsce publish completed - extension ${isPreRelease ? '(pre-release) ' : ''}created at kwaipilot-${version}.vsix`,
  );
});
