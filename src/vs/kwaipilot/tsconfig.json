{
  "compilerOptions": {
    "esModuleInterop": true,
    "module": "commonjs",
    "target": "ES2020",
    "outDir": "out",
    "lib": ["ES2020", "dom"],
    "sourceMap": true,
    "rootDir": "src",
    "strict": true,
    "types": ["node", "react"],
    "resolveJsonModule": true,
    "noEmit": true,
    "paths": {
      "@bridge": [
        "./src/base/bridge/index.ts"
      ],
      "@webview": [
        "./src/base/webview/index.ts"
      ],
    },
  },
  "include": ["src/"],
  "exclude": ["node_modules", ".vscode-test", "webview-ui", "setting-ui"],
  "references": [
    {
      "path": "./packages/shared"
    }
  ]
}
