class WebviewBridge {
  constructor() {
    this.callbacks = new Map();
    this.handlers = new Map();
    this.callbackId = 0;
    this.vscode = typeof acquireVsCodeApi === "function" ? acquireVsCodeApi() : null;
    this.messageListeners = new Set();

    // 监听来自插件的消息
    window.addEventListener("message", async (event) => {
      const message = event.data;
      if (message.protocol === "callback") {
        // 处理一次性回调
        const callback = this.callbacks.get(message.callbackId);
        if (callback) {
          callback(message.data);
          this.callbacks.delete(message.callbackId);
        }
      }
      else if (message.protocol === "callHandler") {
        // 处理注册的处理程序
        const handler = this.handlers.get(message.name);
        if (handler) {
          const response = await handler(message.data);
          this.vscode.postMessage({
            protocol: "callback",
            callbackId: message.callbackId,
            data: response,
          });
        }
      }
      else if (message.protocol === "message") {
        this.messageListeners.forEach((listener) => {
          listener(message);
        });
      }
    });
  }

  callHandler(handlerName, data, callback) {
    if (this.vscode) {
      const callbackId = this.callbackId++;
      if (callback) {
        this.callbacks.set(callbackId, callback);
      }
      this.vscode.postMessage({
        protocol: "callHandler",
        name: handlerName,
        callbackId,
        data,
      });
    }
  }

  registerHandler(name, handler) {
    this.handlers.set(name, handler);
  }

  postMessage(message) {
    if (this.vscode) {
      this.vscode.postMessage({
        protocol: "message",
        data: message,
      });
    }
  }

  addMessageListener(listener) {
    this.messageListeners.add(listener);
  }

  removeMessageListener(listener) {
    this.messageListeners.delete(listener);
  }
}

// 初始化bridge
window.bridge = new WebviewBridge();
