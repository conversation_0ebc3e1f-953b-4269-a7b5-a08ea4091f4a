import { useEffect, useMemo } from "react";
import { useAsync, useAsyncFn } from "react-use";
import { Icon } from "@iconify/react";
import { MCPServerCard } from "../components/MCPServerCard";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useMcpStore } from "../store/useMcpStore";
import { ReportOpt } from "@shared/types/logger";
import { MCPQuickAddBtn } from "../components/MCPQuickAddBtn";
import { MCPTooltip } from "../components/MCPTooltip";
import { MCPQuickAdd } from "../components/MCPQuickAdd";
import { McpServer } from "shared/lib/mcp/types";
import { Button } from "@chakra-ui/react";
import { descriptionClassName, propertyTitleClassName, titleClassName, propertyPanelClassName } from "../schema/common";

const today = new Date().toISOString().slice(0, 10);
const storageKey = "mcp_user_count_reported_date";
const lastReported = localStorage.getItem(storageKey);

/** MCP 管理页面 */
export const MCP = () => {
  const servers = useMcpStore(state => state.servers);
  const setServers = useMcpStore(state => state.setServers);
  const error = useMcpStore(state => state.error);
  const setError = useMcpStore(state => state.setError);
  const quickAddMcpServer = useMcpStore(state => state.quickAddMcpServer);
  const setQuickAddMcpServer = useMcpStore(state => state.setQuickAddMcpServer);
  const mcpServersCount = useMemo(() => servers.length, [servers]);
  const toolsCount = useMemo(() => servers.filter(server => !server.disabled).reduce((acc, server) => acc + (server.tools?.length ?? 0), 0), [servers]);

  const handleMcpMarket = () => {
    const param: ReportOpt<"mcp_jump"> = {
      key: "mcp_jump",
      type: "mcp_setting_hub",
    };
    kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    kwaiPilotBridgeAPI.openUrl("https://wanqing.corp.kuaishou.com/api/v1/mcp-server-manager/mcp-kwaipilot/common/plaza-redirect");
  };

  const reportMcpUserCount = (servers: McpServer[]) => {
    if (servers.length === 0 || lastReported === today) return;
    const param: ReportOpt<"mcp_user_conf"> = {
      key: "mcp_user_conf",
      type: "mcp_user_conf",
      content: JSON.stringify(servers.map(server => server.name).join(",")),
    };
    if (servers.some(server => !server.disabled)) {
      const param: ReportOpt<"mcp_user_confopen"> = {
        key: "mcp_user_confopen",
        type: "mcp_user_confopen",
        content: JSON.stringify(servers.filter(server => !server.disabled).map(server => server.name).join(",")),
      };
      kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    }
    kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    localStorage.setItem(storageKey, today);
  };

  useAsync(async () => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$getAllMcpServers();
    if (res.code !== 0 || res.status === "failed" || res?.data?.isError) {
      setError(res?.message ?? "获取MCP服务器列表失败");
      return;
    }
    setError("");
    reportMcpUserCount(res?.data?.mcpServers ?? []);
    setServers(res?.data?.mcpServers ?? []);
  }, [setError, setServers]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, getSettingsPath] = useAsyncFn(async () => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$getSettingsPath();
    if (res.code !== 0 || res.status === "failed") return;
    kwaiPilotBridgeAPI.editor.openFileToEditor(res.data || "");
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [__, fetchMcpDetailByMarket] = useAsyncFn(async (serverId: string) => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$fetchMcpDetailByMarket({ serverId });
    if (res.code !== 0 || res.status === "failed") return;
    await kwaiPilotBridgeAPI.extensionMCP.$installMcpServer({
      mcpServers: res.data?.serverConfig?.installationTemplate,
    });
  }, []);

  useEffect(() => {
    const sus = kwaiPilotBridgeAPI.observableAPI.mcpServers().subscribe((state) => {
      if (state.code !== 0 || state.isError) {
        setError(state?.message ?? "获取MCP服务器列表失败");
        return;
      }
      setError("");
      setServers(state.mcpServers);
    });
    return () => {
      sus.unsubscribe();
      setQuickAddMcpServer(undefined);
      window.uriQuery = undefined;
    };
  }, [setError, setServers, setQuickAddMcpServer]);

  useEffect(() => {
    const uriServerId = window.uriQuery?.split("=")?.[1];
    if (!uriServerId) return;
    fetchMcpDetailByMarket(uriServerId);
  }, [fetchMcpDetailByMarket]);

  const renderServersPlaceholder = useMemo(() => {
    if (error) {
      return (
        <div className="py-7 text-[var(--vscode-errorForeground)] mt-2 flex items-center justify-center border border-[var(--vscode-widget-border)] rounded-md bg-[var(--vscode-listFilterWidget-background)] break-all px-4">
          {error}
        </div>
      );
    }
    if (mcpServersCount === 0) {
      return (
        <div className="py-7 flex items-center justify-center border border-[var(--vscode-widget-border)] rounded-md bg-[var(--vscode-listFilterWidget-background)]">
          暂未添加MCP Servers，请添加
        </div>
      );
    }
    return null;
  }, [error, mcpServersCount]);

  const renderMcpServers = useMemo(() => {
    return servers.map(server => (
      <MCPServerCard
        key={server.name}
        {...server}
        onEdit={() => {
          const param: ReportOpt<"mcp_jump"> = {
            key: "mcp_jump",
            type: "mcp_setting_jsonconf",
            content: server.name,
          };
          kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
          getSettingsPath();
        }}
      />
    ));
  }, [getSettingsPath, servers]);

  return (
    <div>
      <div className="h-[42px] flex items-center  justify-between">
        <div className={titleClassName}>
          MCP 管理
        </div>
        {
          toolsCount > 40 && (
            <div className="text-[13px] text-[var(--vscode-descriptionForeground)] flex flex-1 justify-end items-center gap-1">
              <Icon icon="codicon:warning" className="inline-block text-[#FFBB26]" />
              <div className="line-clamp-1">
                {`已启用
            ${toolsCount}
            个工具，超40个工具会降低性能且部分模型不支持，系统将自动裁剪多余工具。`}
              </div>
            </div>
          )
        }
      </div>

      <div className={propertyPanelClassName}>
        <div className="flex items-center justify-between mb-2">
          <div>
            <div className={propertyTitleClassName}>
              MCP Servers (
              {mcpServersCount}
              )
            </div>
            <div className={"mr-6 " + descriptionClassName}>
              模型上下文协议 (Model Context Protocol, MCP) 是一种为 Kwaipilot 助理提供工具和功能来扩展助理的能力，可以手动或从快手 MCP 市场添加 MCP Servers。
            </div>
          </div>
          <div className="flex">
            <Button
              className="mr-3"
              onClick={handleMcpMarket}
            >
              MCP市场
              <Icon icon="prime:external-link" className="size-[16px] ml-1" />
            </Button>
            <Button
              className="mr-3"
              onClick={() => {
                const param: ReportOpt<"mcp_jump"> = {
                  key: "mcp_jump",
                  type: "mcp_setting_jsonconf",
                };
                kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
                getSettingsPath();
              }}
            >
              手动配置
            </Button>
            <MCPQuickAddBtn />
          </div>
        </div>
        {quickAddMcpServer && <MCPQuickAdd />}
        {renderServersPlaceholder || renderMcpServers}
        <MCPTooltip />
      </div>
    </div>
  );
};
