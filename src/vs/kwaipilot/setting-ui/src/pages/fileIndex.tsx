import { getCurrentEnvIsInIDE } from "@/utils/ide";
import Settings from "../schema/common";
import { RenderSettings } from "../schema/render";
import { userSettingsStore } from "../store/useSettingsStore";

/** 基础设置页面 */
export const FileIndex = () => {
  const settingValue = userSettingsStore(state => state);
  const host = getCurrentEnvIsInIDE() ? "ide" : "plugin";

  return (
    <RenderSettings settings={Settings.fileIndex} host={host} settingValue={settingValue}>
    </RenderSettings>
  );
};
