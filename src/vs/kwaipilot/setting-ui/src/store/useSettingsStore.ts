import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { Config, StateReturnType } from "shared/lib/state-manager/types";

type State = StateReturnType["config"];

type Action = {
  [K in keyof State as `set${Capitalize<string & K>}`]: (value: State[K]) => void;
};
export const userSettingsStore = create(
  immer<State & Action>(set => ({
    [Config.PROXY_URL]: "",
    setProxy(value: string) {
      set((state) => {
        state[Config.PROXY_URL] = value;
      });
    },

    [Config.MODEL_TYPE]: "",
    setModelType(value: string) {
      set((state) => {
        state[Config.MODEL_TYPE] = value;
      });
    },
    [Config.ENABLE]: false,
    setEnable(value: boolean) {
      set((state) => {
        state[Config.ENABLE] = value;
      });
    },
    [Config.COMMENT_COMPLETION_ENABLE]: false,
    setCommentCompletionEnable(value: boolean) {
      set((state) => {
        state[Config.COMMENT_COMPLETION_ENABLE] = value;
      });
    },
    [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: 0,
    setMaxNewTokensForCodeCompletion(value: number) {
      set((state) => {
        state[Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION] = value;
      });
    },
    [Config.CODE_COMPLETION_DELAY]: 0,
    setCodeCompletionDelay(value: number) {
      set((state) => {
        state[Config.CODE_COMPLETION_DELAY] = value;
      });
    },
    [Config.ENABLE_CODE_BLOCK_ACTION]: false,
    setEnableCodeBlockAction(value: boolean) {
      set((state) => {
        state[Config.ENABLE_CODE_BLOCK_ACTION] = value;
      });
    },

    [Config.PREDICTION_ENABLE]: false,
    setEnablePrediction(value: boolean) {
      set((state) => {
        state[Config.PREDICTION_ENABLE] = value;
      });
    },
    [Config.ENABLE_LOCAL_AGENT]: false,
    setEnableLocalAgent(value: boolean) {
      set((state) => {
        state[Config.ENABLE_LOCAL_AGENT] = value;
      });
    },
    [Config.AGENT_PREFERENCE]: "",
    setAgentPreference(value: string) {
      set((state) => {
        state[Config.AGENT_PREFERENCE] = value;
      });
    },
    [Config.ENABLE_DIAGNOSTICS_CHECK]: true,
    setEnableDiagnosticsCheck(value: boolean) {
      set((state) => {
        console.log(11111, value);
        state[Config.ENABLE_DIAGNOSTICS_CHECK] = value;
      });
    },
  })),
);
