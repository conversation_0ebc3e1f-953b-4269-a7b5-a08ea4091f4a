import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { McpServer, McpTool, MCPFeaturedServer } from "shared/lib/mcp/types";

type State = {
  /** MCP Server列表 */
  servers: McpServer[];
  /** MCP Server全局错误 */
  error?: string;
  /** 工具提示 */
  toolsTooltip?: McpTool;
  /** 当前hover action */
  hoverAction?: string;
  /** 精选mcp server列表 */
  featuredServers: MCPFeaturedServer[];
  /** 当前正在快捷配置的mcp server */
  quickAddMcpServer?: MCPFeaturedServer;
};

type Action = {
  setServers: (servers: McpServer[]) => void;
  setError: (error: string) => void;
  setToolsTooltip: (tooltip: McpTool) => void;
  setHoverAction: (action: string) => void;
  setFeaturedServers: (servers: MCPFeaturedServer[]) => void;
  setQuickAddMcpServer: (server?: MCPFeaturedServer) => void;
};

export const useMcpStore = create(
  immer<State & Action>(set => ({
    servers: [],
    error: "",
    featuredServers: [],
    quickAddMcpServer: void 0,
    setQuickAddMcpServer: (server) => {
      set((state) => {
        state.quickAddMcpServer = server;
      });
    },
    setServers: servers => set((state) => {
      state.servers = servers;
    }),
    setError: (error) => {
      set((state) => {
        state.error = error;
      });
    },
    setToolsTooltip: (tooltip) => {
      set((state) => {
        state.toolsTooltip = tooltip;
      });
    },
    setHoverAction: (action) => {
      set((state) => {
        state.hoverAction = action;
      });
    },
    setFeaturedServers: (servers) => {
      set((state) => {
        state.featuredServers = servers;
      });
    },
  })),
);
