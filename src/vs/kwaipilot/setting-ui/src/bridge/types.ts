import { NATIVE_BRIDGE_EVENT_NAME } from "shared/lib/bridge";

// 基础消息类型
export interface BridgeMessage<T = any> {
  id: string;
  event: string;
  payload?: T;
  code?: number;
  msg?: string;
}

/**
 * 无状态消息， 旧版 bridge API 强行要求有 req - res 的结构， 如果要发送单向消息， 使用这个结构
 */
export interface BridgeMessageStateless<T = any> {
  event: string;
  payload: T;
}

/**
 * @deprecated 直接使用 NATIVE_BRIDGE_EVENT_NAME 即可
 * Bridge 事件类型 NATIVE_BRIDGE_EVENT_NAME
 */
export const BridgeEventType = NATIVE_BRIDGE_EVENT_NAME;

// Bridge 回调函数类型
export type BridgeCallback = (
  payload?: any,
  callBack?: (payload?: any) => void
) => void;

// Bridge 对象
export interface Bridge {
  /**
   * 为某个事件注册监听函数，注意：只能注册一个 handler，重复注册会覆盖之前的 handler
   * @param event
   * @param handler
   * @returns
   */
  registerHandler: (event: string, handler: BridgeCallback) => void;
  callHandler: (
    event: string,
    message: string,
    responseCallback?: BridgeCallback
  ) => void;

  /**
   * postMessage 和 callHandler 区别在于没有【返回值】的逻辑
   * @param message
   * @returns
   */
  postMessage: (message: BridgeMessageStateless<unknown>) => void;
  addMessageListener(listener: (message: BridgeMessageStateless<unknown>) => unknown): void;
  removeMessageListener(listener: (message: BridgeMessageStateless<unknown>) => unknown): void;
}
