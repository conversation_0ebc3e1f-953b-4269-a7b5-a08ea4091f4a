import { selectAnatomy } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { defineMultiStyleConfig, definePartsStyle } = createMultiStyleConfigHelpers(selectAnatomy.keys);

export default defineMultiStyleConfig({
  baseStyle: definePartsStyle({
    field: {
      fontWeight: "normal",
    },
  }),
  sizes: {
    xs: definePartsStyle({
      field: {
        height: "32px",
        fontSize: "13px",
        lineHeight: "18px",
        px: "8px",
        py: 0,
        borderRadius: "4px",
      },
    }),
  },
  variants: {
    outline: definePartsStyle({
      field: {
        border: "1px solid",
        borderColor: "var(--vscode-dropdown-border)",
        color: "var(--vscode-foreground)",
        _hover: {
          borderColor: "var(--vscode-focusBorder)",
          outline: "none",
        },
        _focus: {
          borderColor: "var(--vscode-focusBorder)",
          outline: "none",
          boxShadow: "none", // 新增，去除聚焦时的阴影
        },
      },
    }),
  },
  // The default size and variant values
  defaultProps: {
    size: "xs",
    variant: "outline",
  },
});
