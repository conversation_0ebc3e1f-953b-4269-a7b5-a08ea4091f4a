// theme.ts

// 1. import `extendTheme` function
import { defineStyleConfig, defineStyle } from "@chakra-ui/react";

export default defineStyleConfig({
  // The styles all button have in common
  baseStyle: {
    fontWeight: "normal",
    borderRadius: "4px", // <-- border radius is same for all variants and sizes
    _disabled: {
      opacity: 0.4,
    },
  },
  // Two sizes: sm and md
  sizes: {
    xs: {
      fontSize: "13px",
      lineHeight: "18px",
      height: "32px",
      paddingInline: "0",
      padding: "0 12px",
    },
  },
  // Two variants: outline and solid
  variants: {
    outline: {
      border: "1px solid",
      borderColor: "var(--vscode-dropdown-border)",
      color: "var(--vscode-badge-foreground)",
      bg: "transparent",
      _hover: {
        bg: "transparent",
        borderColor: "var(--vscode-focusBorder)",
      },
      _focus: {
        bg: "transparent",
        borderColor: "var(--vscode-focusBorder)",
      },
      _expanded: {
        bg: "transparent",
        borderColor: "var(--vscode-focusBorder)",
      },
      _disabled: {
        borderColor: "var(--vscode-dropdown-border) !important",
      },
    },
    blueSolid: defineStyle({
      bg: "var(--vscode-button-background)",
      color: "var(--vscode-button-foreground)",
      border: "1px solid",
      borderColor: "transparent",
      _hover: {
        borderColor: "var(--vscode-focusBorder)",
      },
      _focus: {
        borderColor: "var(--vscode-focusBorder)",
      },
      _disabled: {
        _hover: {
          bg: "var(--vscode-button-background) !important",
        },
      },
    }),
  },
  // The default size and variant values
  defaultProps: {
    size: "xs",
    variant: "outline",
  },
});
