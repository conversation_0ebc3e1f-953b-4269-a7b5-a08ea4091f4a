import { numberInputAnatomy } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { defineMultiStyleConfig, definePartsStyle } = createMultiStyleConfigHelpers(numberInputAnatomy.keys);

export default defineMultiStyleConfig({
  sizes: {
    xs: definePartsStyle({
      field: {
        height: "32px",
        padding: "0 8px",
        fontSize: "13px",
        lineHeight: "18px",
        borderRadius: "4px",
      },
    }),
  },
  variants: {
    outline: definePartsStyle({
      field: {
        color: "var(--vscode-foreground)",
        border: "1px solid",
        borderColor: "var(--vscode-settings-checkboxBorder)",
        _hover: {
          borderColor: "var(--vscode-focusBorder)",
          outline: "none",
        },
        _focus: {
          borderColor: "var(--vscode-focusBorder)",
          outline: "none",
          boxShadow: "none", // 新增，去除聚焦时的阴影
        },
      },
      stepper: {
        borderColor: "var(--vscode-dropdown-border)",
        color: "var(--vscode-foreground)",
        fontSize: "8px",
      },
    }),
  },
  defaultProps: { size: "xs", variant: "outline" },
});
