// theme.ts

// 1. import `extendTheme` function
import { switchAnatomy } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { definePartsStyle, defineMultiStyleConfig } = createMultiStyleConfigHelpers(switchAnatomy.keys);

const baseStyle = definePartsStyle({
  container: {
    height: "22px",
  },
  thumb: {
    height: "18px",
    width: "18px",
    transform: "translateX(2px)",
    _checked: {
      transform: "translateX(26px)",
    },
  },
  track: {
    height: "100%",
    width: "46px",
    padding: 0,
    alignItems: "center",
    bg: "var(--vscode-button-secondaryBackground)",
    _checked: {
      bg: "var(--vscode-button-background)",
    },
  },
});

export default defineMultiStyleConfig({ baseStyle });
