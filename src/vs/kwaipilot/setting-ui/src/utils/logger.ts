import { LoggerSupplementaryField } from "@shared/types/logger";
import { kwaiPilotBridgeAPI } from "@/bridge";

class Logger {
  debug(msg: string, scope: string, tags?: LoggerSupplementaryField) {
    kwaiPilotBridgeAPI.printLogger({
      level: "debug",
      msg,
      scope,
      tags,
    });
  }

  error(msg: string, scope: string, tags?: LoggerSupplementaryField) {
    kwaiPilotBridgeAPI.printLogger({
      level: "error",
      msg,
      scope,
      tags,
    });
  }

  info(msg: string, scope: string, tags?: LoggerSupplementaryField) {
    kwaiPilotBridgeAPI.printLogger({
      level: "info",
      msg,
      scope,
      tags,
    });
  }

  warn(msg: string, scope: string, tags?: LoggerSupplementaryField) {
    kwaiPilotBridgeAPI.printLogger({
      level: "warn",
      msg,
      scope,
      tags,
    });
  }
}

export const logger = new Logger();
