import { useMemo } from "react";
import { useMcpStore } from "../store/useMcpStore";
import { Tooltip } from "react-tooltip";

export const MCPTooltip = () => {
  const toolsTooltip = useMcpStore(state => state.toolsTooltip);
  const hoverAction = useMcpStore(state => state.hoverAction);

  const renderToolsTooltip = useMemo(() => {
    const properties = (toolsTooltip?.inputSchema as unknown as { properties: Record<string, any> })?.properties || {};
    const required = (toolsTooltip?.inputSchema as unknown as { required: string[] })?.required || [];
    return (
      <Tooltip id="mcp-server-tools-tooltip" style={{ backgroundColor: "#3C3C3C", opacity: 1, padding: 0, borderRadius: 0 }} place="bottom-start">
        <div className="max-w-[411px] max-h-[246px] overflow-auto text-[12px]">
          <div className="p-1 border-b border-[var(--vscode-widget-border)]">
            {toolsTooltip?.description}
          </div>
          {
            Object.entries(properties).length > 0 && (
              <div className="p-1">
                <div className="mb-1">参数</div>
                <div>
                  {Object.entries(properties).map(([key, value]) => (
                    <div className="flex mt-1" key={key}>
                      <div className="mr-1 font-bold">
                        {
                          required?.includes(key) && (
                            <span className="text-[#C74E39ff] mr-1">*</span>
                          )
                        }
                        {`${key}:`}
                      </div>
                      <div>{(value as any)?.description || "--"}</div>
                    </div>
                  ))}
                </div>
              </div>
            )
          }
        </div>
      </Tooltip>
    );
  }, [toolsTooltip]);

  const renderActionTooltip = useMemo(() => {
    return (
      <Tooltip id="mcp-server-action-tooltip" style={{ backgroundColor: "#3C3C3C", opacity: 1, padding: "4px 8px", borderRadius: 0 }}>
        <div className="text-[12px]">
          {hoverAction}
        </div>
      </Tooltip>
    );
  }, [hoverAction]);

  return (
    <>
      {renderToolsTooltip}
      {renderActionTooltip}
    </>
  );
};
