import { VscodeButton } from "@vscode-elements/react-elements";
import { useMcpStore } from "../store/useMcpStore";
import { useEffect, useMemo, useCallback, useState } from "react";
import { useImmer } from "use-immer";
import clsx from "clsx";
import { useAsyncFn } from "react-use";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { InstallMcpParams } from "shared/lib/mcp/types";

export const MCPQuickAdd = () => {
  const quickAddMcpServer = useMcpStore(state => state.quickAddMcpServer);
  const setQuickAddMcpServer = useMcpStore(state => state.setQuickAddMcpServer);

  const [formValues, setFormValues] = useImmer<Record<string, string>>({});
  const [errorKeys, setErrorKeys] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState<string>("");

  const handleReset = useCallback(() => {
    setQuickAddMcpServer(undefined);
    setFormValues({});
    setErrorKeys([]);
    setErrorMessage("");
  }, [setQuickAddMcpServer, setFormValues]);

  useEffect(() => {
    if (!quickAddMcpServer?.serverConfig?.installationArguments?.length) return;
    setFormValues({});
    setFormValues((draft: Record<string, string>) => {
      quickAddMcpServer?.serverConfig?.installationArguments?.forEach((config) => {
        draft[`${config?.key}`] = "";
      });
    });
  }, [quickAddMcpServer, setFormValues]);

  const [installMcpState, installMcp] = useAsyncFn(async (params: InstallMcpParams) => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$installMcpServer(params);
    if (res.code !== 0 || res.status === "failed") {
      setErrorMessage(res.message || "安装失败");
      return;
    }
    handleReset();
  }, []);

  const formValuesValid = useMemo(() => errorKeys.length === 0, [errorKeys]);

  const handleInputChange = useCallback((valueKey: string, value: string) => {
    setFormValues((draft) => {
      draft[valueKey] = value;
    });
    if (value && errorKeys.includes(valueKey)) {
      setErrorKeys(keys => keys.filter(key => key !== valueKey));
    }
  }, [setFormValues, errorKeys]);

  const handleSubmit = useCallback(() => {
    const errors: string[] = [];
    quickAddMcpServer?.serverConfig?.installationArguments?.forEach((config) => {
      if (config.required && !formValues[`${config?.key}`]?.trim()) {
        errors.push(`${config?.key}`);
      }
    });
    setErrorKeys(errors);
    if (errors.length > 0 || !formValuesValid) return;
    const stringifyTemplate = JSON.stringify(quickAddMcpServer?.serverConfig?.installationTemplate)
      .replace(/\$\{([^}]+)\}/g, (_, key) => formValues[key]);
    installMcp({
      mcpServers: JSON.parse(stringifyTemplate),
    });
  }, [formValues, quickAddMcpServer, formValuesValid, installMcp]);

  const renderConfigs = useMemo(() => {
    if (!quickAddMcpServer?.serverConfig?.installationArguments?.length) {
      return (
        <div className="text-[12px] text-[var(--vscode-descriptionForeground)] mb-3">
          {`您将添加 ${quickAddMcpServer?.serverName} ，此MCP Server无需提供额外配置信息，点击"确认"完成添加。`}
        </div>
      );
    }

    return quickAddMcpServer?.serverConfig?.installationArguments?.map((config) => {
      return (
        <div key={config.title} className="mb-3">
          <div className="text-[12px] font-medium mb-1">{config.title}</div>
          <div className="text-[12px] text-[var(--vscode-descriptionForeground)]">
            {config?.description?.split(/(\[.*?\]\(.*?\))/).map((part: string, i: number) => {
              const match = part.match(/\[(.*?)\]\((.*?)\)/);
              if (match) {
                return (
                  <a
                    key={i}
                    href={match[2]}
                    className="text-[var(--vscode-textLink-foreground)] hover:text-[var(--vscode-textLink-activeForeground)]"
                  >
                    {match[1]}
                  </a>
                );
              }
              return part;
            })}
          </div>
          <input
            type="text"
            value={formValues[`${config?.key}`]}
            onChange={e => handleInputChange(`${config?.key}`, e.target.value)}
            placeholder={config.placeholder}
            className={clsx("mt-2 bg-[var(--vscode-settings-textInputBackground)] text-[var(--vscode-settings-textInputForeground)] px-1 py-[6px] border-[var(--vscode-settings-textInputBorder)] focus:border-[var(--vscode-focusBorder)] border-[1px] w-full !outline-none", {
              "!border-[#C74E39]": errorKeys.includes(`${config?.key}`),
            })}
          />
        </div>
      );
    });
  }, [quickAddMcpServer, formValues, errorKeys, handleInputChange]);

  return (
    <div className="rounded border border-[var(--vscode-focusBorder)] p-3">
      <div className="text-[13px] font-semibold mb-3">添加 MCP Server</div>
      {renderConfigs}
      <div className="flex justify-between items-center">
        <div className="text-[12px] text-[var(--vscode-errorForeground)]">{errorMessage}</div>
        <div className="flex items-center">
          <VscodeButton
            className="h-[28px] rounded flex items-center mr-2"
            secondary
            onClick={handleReset}
          >
            取消
          </VscodeButton>
          <VscodeButton
            className={clsx("h-[28px] rounded flex items-center", {
              "opacity-50 cursor-not-allowed": installMcpState.loading,
            })}
            onClick={handleSubmit}
          >
            确认
          </VscodeButton>
        </div>
      </div>
    </div>
  );
};
