import { Icon } from "@iconify/react";
import { McpServer } from "shared/lib/mcp/types";
import { VscodeLabel } from "@vscode-elements/react-elements";
import clsx from "clsx";
import { MCPSwitch } from "./MCPSwitch";
import { useAsyncFn } from "react-use";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useMemo } from "react";
import { useMcpStore } from "../store/useMcpStore";
import { ReportOpt } from "@shared/types/logger";

export interface MCPServerCardProps extends McpServer {
  onEdit: () => void;
}

const getStatusInfo = (status: McpServer["status"]): { color: string; text: string } => {
  const statusMap: Record<McpServer["status"], { color: string; text: string }> = {
    connected: { color: "#73C991ff", text: "可使用" },
    connecting: { color: "#CCA700ff", text: "准备中" },
    disconnected: { color: "#C74E39ff", text: "不可使用" },
  };
  return statusMap[status];
};

export const MCPServerCard = (props: MCPServerCardProps) => {
  const {
    name,
    status,
    disabled,
    tools = [],
    onEdit,
    error,
  } = props;

  const setToolsTooltip = useMcpStore(state => state.setToolsTooltip);
  const setHoverAction = useMcpStore(state => state.setHoverAction);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, handleToggleServer] = useAsyncFn(async (serverName: string, disabled: boolean) => {
    await kwaiPilotBridgeAPI.extensionMCP.$toggleMcpServer({
      serverName,
      disabled,
    });
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [__, handleDeleteServer] = useAsyncFn(async (serverName: string) => {
    await kwaiPilotBridgeAPI.extensionMCP.$deleteMcpServer({
      serverName,
    });
  }, []);

  const [refreshServerState, handleRefreshServer] = useAsyncFn(async (serverName: string) => {
    const param: ReportOpt<"mcp_action"> = {
      key: "mcp_action",
      type: "mcp_setting_editmcp",
      content: serverName,
    };
    kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    await kwaiPilotBridgeAPI.extensionMCP.$restartMcpServer({
      serverName,
    });
  }, []);

  const isRefreshLoading = useMemo(() => {
    return refreshServerState.loading || status === "connecting";
  }, [refreshServerState.loading, status]);

  const renderToolsList = useMemo(() => {
    if (tools.length === 0) {
      return <span className="text-[12px] text-[var(--vscode-descriptionForeground)]">--</span>;
    }
    return tools.map(tool => (
      <span
        onMouseEnter={() => setToolsTooltip(tool)}
        data-tooltip-id="mcp-server-tools-tooltip"
        key={tool.name}
        className="px-1.5 py-0.5 rounded text-[12px] cursor-pointer font-medium bg-[var(--vscode-badge-background)] text-[var(--vscode-badge-foreground)] hover:text-[var(--vscode-text-foreground)]"
      >
        {tool.name}
      </span>
    ));
  }, [setToolsTooltip, tools]);

  const renderError = useMemo(() => {
    if (!error) return null;
    return <span className="text-[12px] mt-[10px] text-[#C74E39ff] break-all">{error}</span>;
  }, [error]);

  return (
    <div className="flex flex-col mt-4 p-4 rounded border border-[var(--vscode-widget-border)]">
      <div className="flex items-center justify-between">
        <div className="flex items-center flex-1">
          <div className="text-[13px] font-semibold flex-1 break-all line-clamp-1">{name}</div>
          {
            !disabled && (
              <div className="flex items-center flex-shrink-0 mr-6">
                <div className="size-[4px] rounded-full mr-[5px] ml-[13px]" style={{ backgroundColor: getStatusInfo(status).color }} />
                <div className="text-[12px]" style={{ color: getStatusInfo(status).color }}>
                  {getStatusInfo(status).text}
                </div>
              </div>
            )
          }
        </div>
        <div className="flex items-center">
          <MCPSwitch
            checked={!disabled}
            onChange={(checked) => {
              handleToggleServer(name, !checked);
            }}
          />
          <div className="mx-3 h-[12px] w-[1px] bg-[var(--vscode-widget-border)]" />
          <div
            onMouseEnter={() => setHoverAction("更新")}
            data-tooltip-id="mcp-server-action-tooltip"
            onClick={() => {
              if (isRefreshLoading) return;
              handleRefreshServer(name);
            }}
            className={clsx(
              "w-[22px] h-[22px] cursor-pointer rounded flex items-center justify-center hover:bg-[var(--vscode-widget-border)]",
              {
                "opacity-50 cursor-not-allowed": isRefreshLoading,
              },
            )}
          >
            <Icon
              icon="codicon:refresh"
              className={clsx("size-[16px] text-[var(--vscode-foreground)]", {
                "animate-spin": isRefreshLoading,
              })}
            />
          </div>
          <div
            onMouseEnter={() => setHoverAction("编辑")}
            data-tooltip-id="mcp-server-action-tooltip"
            onClick={(onEdit)}
            className={clsx(
              "w-[22px] h-[22px] cursor-pointer rounded flex items-center justify-center hover:bg-[var(--vscode-widget-border)] mx-1",
            )}
          >
            <Icon icon="codicon:edit" className="size-[16px] text-[var(--vscode-foreground)]" />
          </div>
          <div
            onMouseEnter={() => setHoverAction("删除")}
            data-tooltip-id="mcp-server-action-tooltip"
            onClick={() => handleDeleteServer(name)}
            className={clsx(
              "w-[22px] h-[22px] cursor-pointer rounded flex items-center justify-center hover:bg-[var(--vscode-widget-border)]",
            )}
          >
            <Icon icon="codicon:trash" className="size-[16px] text-[var(--vscode-foreground)]" />
          </div>
        </div>
      </div>
      {
        !disabled && (
          <div className="flex items-center mt-3">
            <VscodeLabel className="text-[12px] mr-2">Tools:</VscodeLabel>
            <div className="flex flex-wrap gap-1">
              {renderToolsList}
            </div>
          </div>
        )
      }
      {renderError}
    </div>
  );
};
