import { useState, useRef, useEffect } from "react";
import clsx from "clsx";
import { useDebounce } from "react-use";

interface SwitchProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
}

export const MCPSwitch = (props: SwitchProps) => {
  const [checked, setChecked] = useState<boolean>();
  const isFirstRender = useRef(true);

  const [,] = useDebounce(
    () => {
      if (!isFirstRender.current) {
        props.onChange?.(<PERSON><PERSON><PERSON>(checked));
      }
      else {
        isFirstRender.current = false;
      }
    },
    500,
    [checked],
  );

  useEffect(() => {
    setChecked(props.checked);
  }, [props.checked]);

  return (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={checked}
        onChange={e => setChecked(e.target.checked)}
      />
      <div className={clsx("w-6 h-3.5 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:rounded-full after:h-2.5 after:w-2.5 after:transition-all", {
        "bg-[#007ADA] after:bg-[#fff]": checked,
        "bg-[#ccc] after:bg-[#fff]": !checked,
      })}
      />
    </label>
  );
};
