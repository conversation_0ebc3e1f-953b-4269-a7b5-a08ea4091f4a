import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button, Input } from "@chakra-ui/react";
import { Icon } from "@iconify/react";

export default function ChakraSelect(props: {
  value: string | number;
  options: (string | number)[];
  optionLabels?: string[];
  onChange: (value: string | number) => void;
  className?: string;
}) {
  const { value, options, optionLabels, onChange, className } = props;
  const index = options.findIndex(option => option === value);
  return (
    <Menu gutter={4} matchWidth={true}>
      <MenuButton
        as={Button}
        rightIcon={<Icon icon="codicon:chevron-down" />}
        className={className + " [&:not(:hover)]:border-[var(--vscode-settings-checkboxBorder)]"}
      >
        {optionLabels ? optionLabels[index] : options[index]}
      </MenuButton>
      <MenuList>
        {options.map((option, index) => (
          <MenuItem
            key={index}
            onClick={() => on<PERSON><PERSON><PERSON>(option)}
          >
            {optionLabels ? optionLabels[index] : option}
            <Icon icon="codicon:check" style={{ display: option === value ? "inline" : "none", marginLeft: "auto" }} />
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};
