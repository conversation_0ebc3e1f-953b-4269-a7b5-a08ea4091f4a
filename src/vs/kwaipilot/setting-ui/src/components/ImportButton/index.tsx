import { Button } from "@chakra-ui/react";
import { Popconfirm } from "antd";
import { useState } from "react";
import InfoIcon from "@/assets/info.svg?react";
import css from "./index.module.less";

interface Props {
  productName: string;
  onConfirm: () => Promise<void>;
}

export default function ImportButton({ productName, onConfirm }: Props) {
  const [loading, setLoading] = useState(false);
  const handlerClick = async () => {
    setLoading(true);
    await onConfirm();
    setLoading(false);
  };

  if (loading) return <Button isLoading={loading} loadingText="导入中..."></Button>;

  return (
    <Popconfirm
      title={`确认导入 ${productName} 配置吗？`}
      description="导入后将覆盖 Kwaipilot 当前配置，且不可恢复"
      onConfirm={handlerClick}
      okText="确认"
      cancelText="取消"
      okButtonProps={{
        className: "popconfirm-ok-button",
      }}
      cancelButtonProps={{
        className: "popconfirm-cancel-button",
      }}
      icon={<InfoIcon />}
      classNames={{
        root: css["popconfirm-root"],
      }}
    >
      <Button>
        导入
        {" "}
        {productName}
        {" "}
        配置
      </Button>
    </Popconfirm>
  );
}
