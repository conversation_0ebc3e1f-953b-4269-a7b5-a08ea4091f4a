.popconfirm-root {
  :global {
    .ant-popover-inner {
      background-color: var(--vscode-notifications-background);
      border-radius: 4px;
      padding: 16px;

      .ant-popconfirm-message {
        margin-bottom: 16px;

        .ant-popconfirm-message-icon {
          color: var(--vscode-button-foreground);
        }

        .ant-popconfirm-message-text {
          margin-left: 4px;
          font-size: 13px;
          font-weight: 600;
          font-family: 'PingFang SC';

          .ant-popconfirm-title {
            color: var(--vscode-button-foreground);
          }

          .ant-popconfirm-description {
            color: var(--vscode-foreground);
            font-size: 13px;
            font-weight: 400;
            font-family: 'PingFang SC';
          }
        }
      }

      .ant-popconfirm-buttons {
        .ant-btn {
          height: 28px;
          padding: 5px 16px;
          background: #3A3D41;
          border-radius: 4px;
          border: none;
        }

        .popconfirm-ok-button {
          background: var(--vscode-button-background);
          color: var(--vscode-button-foreground);

          &:hover {
            background: var(--vscode-button-hoverBackground);
          }
        }

        .popconfirm-cancel-button {
          background: var(--vscode-button-secondaryBackground);
          color: var(--vscode-button-secondaryForeground);

          &:hover {
            background: var(--vscode-button-secondaryHoverBackground);
          }
        }
      }

    }

    .ant-popover-arrow:before {
      background-color: var(--vscode-notifications-background);
    }
  }
}