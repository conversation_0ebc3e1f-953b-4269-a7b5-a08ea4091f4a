import { type Config } from "tailwindcss";
import multiThemePlugin from "tailwindcss-themer";
import { light, dark } from "./src/style/designToken";
import { kebabCase } from "lodash-es";

/**
 * 获取 color token
 * colorTextCommonPrimary -> text-common-primary
 * @param theme
 * @returns
 */

const getColorTokens = (theme: "light" | "dark") => {
  const tokens = theme === "light" ? light : dark;
  const tailwindColors: Record<string, string> = {};
  Object.entries(tokens).forEach(([key, value]) => {
    if (key.startsWith("color")) {
      const tailwindKey = kebabCase(key.replace(/^color/, ""));
      tailwindColors[`${tailwindKey}`] = value;
    }
  });
  return tailwindColors;
};

export default {
  corePlugins: {
    preflight: false,
  },
  mode: "jit",
  darkMode: ["class"],
  content: ["./src/**/*.{ts,tsx}"],
  prefix: "",
  theme: {},
  plugins: [
    multiThemePlugin({
      defaultTheme: {
        extend: {
          backgroundImage: {
            "main-bg":
              "linear-gradient(180deg, rgba(255, 255, 255, 0.64) 0%, rgba(255, 255, 255, 0.80) 16%, rgba(255, 255, 255, 0.85) 100%)",
            "tag-bg-kwaipilot":
              "linear-gradient(90deg, rgba(229, 244, 255, 0.30) 0%, rgba(229, 244, 255, 0.60) 100%)",
            "tag-bg-claude":
              "linear-gradient(90deg, rgba(255, 233, 212, 0.18) 0%, rgba(255, 233, 212, 0.36) 100%)",
            "tag-bg-gpt":
              "linear-gradient(90deg, rgba(229, 255, 241, 0.30) 0%, rgba(229, 255, 241, 0.60) 100%)",
          },
          colors: {
            ...getColorTokens("light"),
          },
          fontFamily: {
            "kwaipilot-text": ["PingFang SC"],
          },
          boxShadow: {
            "stop-button": "2px 4px 8px 0px rgba(9, 23, 61, 0.12)",
            "assisant-message": "0px 2px 8px 0px rgba(62, 119, 226, 0.05)",
          },
        },
      },
      themes: [
        {
          name: "dark",
          extend: {
            backgroundImage: {
              "main-bg":
                "linear-gradient(180deg, #0C233D 0%, rgba(5, 14, 24, 0.00) 25.31%), var(--Bg-, #091420)",
              "tag-bg-kwaipilot":
                "linear-gradient(90deg, rgba(150, 173, 202, 0.12) 0%, rgba(150, 173, 202, 0.12) 100%)",
              "tag-bg-claude":
                "linear-gradient(90deg, rgba(183, 147, 134, 0.12) 0%, rgba(183, 147, 134, 0.12) 100%)",
              "tag-bg-gpt":
                "linear-gradient(90deg, rgba(134, 192, 147, 0.12) 0%, rgba(134, 192, 147, 0.12) 100%)",
            },
            colors: {
              ...getColorTokens("dark"),
            },
            fontFamily: {
              "kwaipilot-text": ["PingFang SC"],
            },
            boxShadow: {
              "stop-button": "2px 4px 8px 0px rgba(9, 23, 61, 0.12)",
              "assisant-message": "0px 2px 8px 0px rgba(62, 119, 226, 0.05)",
            },
          },
        },
      ],
    }),
  ],
} satisfies Config;
