{"$schema": "vscode://schemas/color-theme", "type": "light", "colors": {"actionBar.toggledBackground": "#dddddd", "activityBar.activeBorder": "#005fb8", "activityBar.background": "#f8f8f8", "activityBar.border": "#e5e5e5", "activityBar.foreground": "#1f1f1f", "activityBar.inactiveForeground": "#616161", "activityBarBadge.background": "#005fb8", "activityBarBadge.foreground": "#ffffff", "badge.background": "#cccccc", "badge.foreground": "#3b3b3b", "button.background": "#005fb8", "button.border": "#0000001a", "button.foreground": "#ffffff", "button.hoverBackground": "#0258a8", "button.secondaryBackground": "#e5e5e5", "button.secondaryForeground": "#3b3b3b", "button.secondaryHoverBackground": "#cccccc", "chat.editedFileForeground": "#895503", "chat.slashCommandBackground": "#d2ecff", "chat.slashCommandForeground": "#306ca2", "checkbox.background": "#f8f8f8", "checkbox.border": "#cecece", "descriptionForeground": "#3b3b3b", "diffEditor.unchangedRegionBackground": "#f8f8f8", "dropdown.background": "#ffffff", "dropdown.border": "#cecece", "dropdown.foreground": "#3b3b3b", "dropdown.listBackground": "#ffffff", "editor.background": "#ffffff", "editor.foreground": "#3b3b3b", "editor.inactiveSelectionBackground": "#e5ebf1", "editor.selectionHighlightBackground": "#add6ff80", "editorGroup.border": "#e5e5e5", "editorGroupHeader.tabsBackground": "#f8f8f8", "editorGroupHeader.tabsBorder": "#e5e5e5", "editorGutter.addedBackground": "#2ea043", "editorGutter.deletedBackground": "#f85149", "editorGutter.modifiedBackground": "#005fb8", "editorIndentGuide.activeBackground1": "#939393", "editorIndentGuide.background1": "#d3d3d3", "editorLineNumber.activeForeground": "#171184", "editorLineNumber.foreground": "#6e7681", "editorOverviewRuler.border": "#e5e5e5", "editorSuggestWidget.background": "#f8f8f8", "editorWidget.background": "#f8f8f8", "errorForeground": "#f85149", "focusBorder": "#005fb8", "foreground": "#3b3b3b", "icon.foreground": "#3b3b3b", "input.background": "#ffffff", "input.border": "#cecece", "input.foreground": "#3b3b3b", "input.placeholderForeground": "#767676", "inputOption.activeBackground": "#bed6ed", "inputOption.activeBorder": "#005fb8", "inputOption.activeForeground": "#000000", "keybindingLabel.foreground": "#3b3b3b", "list.activeSelectionBackground": "#e8e8e8", "list.activeSelectionForeground": "#000000", "list.activeSelectionIconForeground": "#000000", "list.focusAndSelectionOutline": "#005fb8", "list.hoverBackground": "#f2f2f2", "menu.border": "#cecece", "menu.selectionBackground": "#005fb8", "menu.selectionForeground": "#ffffff", "notebook.cellBorderColor": "#e5e5e5", "notebook.selectedCellBackground": "#c8ddf150", "notificationCenterHeader.background": "#ffffff", "notificationCenterHeader.foreground": "#3b3b3b", "notifications.background": "#ffffff", "notifications.border": "#e5e5e5", "notifications.foreground": "#3b3b3b", "panel.background": "#f8f8f8", "panel.border": "#e5e5e5", "panelInput.border": "#e5e5e5", "panelTitle.activeBorder": "#005fb8", "panelTitle.activeForeground": "#3b3b3b", "panelTitle.inactiveForeground": "#3b3b3b", "peekViewEditor.matchHighlightBackground": "#bb800966", "peekViewResult.background": "#ffffff", "peekViewResult.matchHighlightBackground": "#bb800966", "pickerGroup.border": "#e5e5e5", "pickerGroup.foreground": "#8b949e", "ports.iconRunningProcessForeground": "#369432", "progressBar.background": "#005fb8", "quickInput.background": "#f8f8f8", "quickInput.foreground": "#3b3b3b", "searchEditor.textInputBorder": "#cecece", "settings.dropdownBackground": "#ffffff", "settings.dropdownBorder": "#cecece", "settings.headerForeground": "#1f1f1f", "settings.modifiedItemIndicator": "#bb800966", "settings.numberInputBorder": "#cecece", "settings.textInputBorder": "#cecece", "sideBar.background": "#f8f8f8", "sideBar.border": "#e5e5e5", "sideBar.foreground": "#3b3b3b", "sideBarSectionHeader.background": "#f8f8f8", "sideBarSectionHeader.border": "#e5e5e5", "sideBarSectionHeader.foreground": "#3b3b3b", "sideBarTitle.foreground": "#3b3b3b", "statusBar.background": "#f8f8f8", "statusBar.border": "#e5e5e5", "statusBar.debuggingBackground": "#fd716c", "statusBar.debuggingForeground": "#000000", "statusBar.focusBorder": "#005fb8", "statusBar.foreground": "#3b3b3b", "statusBar.noFolderBackground": "#f8f8f8", "statusBarItem.compactHoverBackground": "#cccccc", "statusBarItem.errorBackground": "#c72e0f", "statusBarItem.focusBorder": "#005fb8", "statusBarItem.hoverBackground": "#b8b8b850", "statusBarItem.prominentBackground": "#6e768166", "statusBarItem.remoteBackground": "#005fb8", "statusBarItem.remoteForeground": "#ffffff", "tab.activeBackground": "#ffffff", "tab.activeBorder": "#f8f8f8", "tab.activeBorderTop": "#005fb8", "tab.activeForeground": "#3b3b3b", "tab.border": "#e5e5e5", "tab.hoverBackground": "#ffffff", "tab.inactiveBackground": "#f8f8f8", "tab.inactiveForeground": "#868686", "tab.lastPinnedBorder": "#d4d4d4", "tab.selectedBackground": "#ffffffa5", "tab.selectedBorderTop": "#68a3da", "tab.selectedForeground": "#333333b3", "tab.unfocusedActiveBorder": "#f8f8f8", "tab.unfocusedActiveBorderTop": "#e5e5e5", "tab.unfocusedHoverBackground": "#f8f8f8", "terminal.foreground": "#3b3b3b", "terminal.inactiveSelectionBackground": "#e5ebf1", "terminal.tab.activeBorder": "#005fb8", "terminalCursor.foreground": "#005fb8", "textBlockQuote.background": "#f8f8f8", "textBlockQuote.border": "#e5e5e5", "textCodeBlock.background": "#f8f8f8", "textLink.activeForeground": "#005fb8", "textLink.foreground": "#005fb8", "textPreformat.background": "#0000001f", "textPreformat.foreground": "#3b3b3b", "textSeparator.foreground": "#21262d", "titleBar.activeBackground": "#f8f8f8", "titleBar.activeForeground": "#1e1e1e", "titleBar.border": "#e5e5e5", "titleBar.inactiveBackground": "#f8f8f8", "titleBar.inactiveForeground": "#8b949e", "welcomePage.tileBackground": "#f3f3f3", "widget.border": "#e5e5e5", "activityBar.dropBorder": "#1f1f1f", "activityBarTop.activeBorder": "#424242", "activityBarTop.dropBorder": "#424242", "activityBarTop.foreground": "#424242", "activityBarTop.inactiveForeground": "#424242bf", "activityErrorBadge.background": "#e51400", "activityErrorBadge.foreground": "#ffffff", "activityWarningBadge.background": "#bf8803", "activityWarningBadge.foreground": "#ffffff", "banner.background": "#a2a2a2", "banner.foreground": "#000000", "banner.iconForeground": "#1a85ff", "breadcrumb.activeSelectionForeground": "#2f2f2f", "breadcrumb.background": "#ffffff", "breadcrumb.focusForeground": "#2f2f2f", "breadcrumb.foreground": "#3b3b3bcc", "breadcrumbPicker.background": "#f8f8f8", "button.separator": "#ffffff66", "chart.axis": "#00000099", "chart.guide": "#00000033", "chart.line": "#236b8e", "charts.blue": "#1a85ff", "charts.foreground": "#3b3b3b", "charts.green": "#388a34", "charts.lines": "#3b3b3b80", "charts.orange": "#d18616", "charts.purple": "#652d90", "charts.red": "#e51400", "charts.yellow": "#bf8803", "chat.avatarBackground": "#f2f2f2", "chat.avatarForeground": "#3b3b3b", "chat.requestBackground": "#ffffff9e", "chat.requestBorder": "#0000001a", "checkbox.disabled.background": "#b9b9b9", "checkbox.disabled.foreground": "#797979", "checkbox.foreground": "#3b3b3b", "checkbox.selectBackground": "#f8f8f8", "checkbox.selectBorder": "#3b3b3b", "commandCenter.activeBackground": "#00000014", "commandCenter.activeBorder": "#1e1e1e4d", "commandCenter.activeForeground": "#1e1e1e", "commandCenter.background": "#0000000d", "commandCenter.border": "#1e1e1e33", "commandCenter.debuggingBackground": "#fd716c42", "commandCenter.foreground": "#1e1e1e", "commandCenter.inactiveBorder": "#8b949e40", "commandCenter.inactiveForeground": "#8b949e", "commentsView.resolvedIcon": "#61616180", "commentsView.unresolvedIcon": "#005fb8", "debugConsole.errorForeground": "#f85149", "debugConsole.infoForeground": "#1a85ff", "debugConsole.sourceForeground": "#3b3b3b", "debugConsole.warningForeground": "#bf8803", "debugConsoleInputIcon.foreground": "#3b3b3b", "debugExceptionWidget.background": "#f1dfde", "debugExceptionWidget.border": "#a31515", "debugIcon.breakpointCurrentStackframeForeground": "#be8700", "debugIcon.breakpointDisabledForeground": "#848484", "debugIcon.breakpointForeground": "#e51400", "debugIcon.breakpointStackframeForeground": "#89d185", "debugIcon.breakpointUnverifiedForeground": "#848484", "debugIcon.continueForeground": "#007acc", "debugIcon.disconnectForeground": "#a1260d", "debugIcon.pauseForeground": "#007acc", "debugIcon.restartForeground": "#388a34", "debugIcon.startForeground": "#388a34", "debugIcon.stepBackForeground": "#007acc", "debugIcon.stepIntoForeground": "#007acc", "debugIcon.stepOutForeground": "#007acc", "debugIcon.stepOverForeground": "#007acc", "debugIcon.stopForeground": "#a1260d", "debugTokenExpression.boolean": "#0000ff", "debugTokenExpression.error": "#e51400", "debugTokenExpression.name": "#9b46b0", "debugTokenExpression.number": "#098658", "debugTokenExpression.string": "#a31515", "debugTokenExpression.type": "#4a90e2", "debugTokenExpression.value": "#6c6c6ccc", "debugToolBar.background": "#f3f3f3", "debugView.exceptionLabelBackground": "#a31515", "debugView.exceptionLabelForeground": "#ffffff", "debugView.stateLabelBackground": "#88888844", "debugView.stateLabelForeground": "#3b3b3b", "debugView.valueChangedHighlight": "#569cd6", "diffEditor.diagonalFill": "#22222233", "diffEditor.insertedLineBackground": "#9bb95533", "diffEditor.insertedTextBackground": "#9ccc2c40", "diffEditor.move.border": "#8b8b8b9c", "diffEditor.moveActive.border": "#ffa500", "diffEditor.removedLineBackground": "#ff000033", "diffEditor.removedTextBackground": "#ff000033", "diffEditor.unchangedCodeBackground": "#b8b8b829", "diffEditor.unchangedRegionForeground": "#3b3b3b", "diffEditor.unchangedRegionShadow": "#737373bf", "disabledForeground": "#61616180", "editor.compositionBorder": "#000000", "editor.findMatchBackground": "#a8ac94", "editor.findMatchHighlightBackground": "#ea5c0055", "editor.findRangeHighlightBackground": "#b4b4b44d", "editor.focusedStackFrameHighlightBackground": "#cee7ce73", "editor.foldBackground": "#add6ff4d", "editor.foldPlaceholderForeground": "#808080", "editor.hoverHighlightBackground": "#add6ff26", "editor.inlineValuesBackground": "#ffc80033", "editor.inlineValuesForeground": "#00000080", "editor.lineHighlightBorder": "#eeeeee", "editor.linkedEditingBackground": "#ff00004d", "editor.placeholder.foreground": "#00000077", "editor.rangeHighlightBackground": "#fdff0033", "editor.selectionBackground": "#add6ff", "editor.snippetFinalTabstopHighlightBorder": "#0a326480", "editor.snippetTabstopHighlightBackground": "#0a326433", "editor.stackFrameHighlightBackground": "#ffff6673", "editor.symbolHighlightBackground": "#ea5c0055", "editor.wordHighlightBackground": "#57575740", "editor.wordHighlightStrongBackground": "#0e639c40", "editor.wordHighlightTextBackground": "#57575740", "editorActionList.background": "#f8f8f8", "editorActionList.focusBackground": "#e8e8e8", "editorActionList.focusForeground": "#000000", "editorActionList.foreground": "#3b3b3b", "editorBracketHighlight.foreground1": "#0431fa", "editorBracketHighlight.foreground2": "#319331", "editorBracketHighlight.foreground3": "#7b3814", "editorBracketHighlight.foreground4": "#00000000", "editorBracketHighlight.foreground5": "#00000000", "editorBracketHighlight.foreground6": "#00000000", "editorBracketHighlight.unexpectedBracket.foreground": "#ff1212cc", "editorBracketMatch.background": "#0064001a", "editorBracketMatch.border": "#b9b9b9", "editorBracketPairGuide.activeBackground1": "#00000000", "editorBracketPairGuide.activeBackground2": "#00000000", "editorBracketPairGuide.activeBackground3": "#00000000", "editorBracketPairGuide.activeBackground4": "#00000000", "editorBracketPairGuide.activeBackground5": "#00000000", "editorBracketPairGuide.activeBackground6": "#00000000", "editorBracketPairGuide.background1": "#00000000", "editorBracketPairGuide.background2": "#00000000", "editorBracketPairGuide.background3": "#00000000", "editorBracketPairGuide.background4": "#00000000", "editorBracketPairGuide.background5": "#00000000", "editorBracketPairGuide.background6": "#00000000", "editorCodeLens.foreground": "#919191", "editorCommentsWidget.rangeActiveBackground": "#005fb81a", "editorCommentsWidget.rangeBackground": "#005fb81a", "editorCommentsWidget.replyInputBackground": "#f3f3f3", "editorCommentsWidget.resolvedBorder": "#61616180", "editorCommentsWidget.unresolvedBorder": "#005fb8", "editorCursor.foreground": "#000000", "editorError.foreground": "#e51400", "editorGhostText.foreground": "#00000077", "editorGroup.dropBackground": "#2677cb2e", "editorGroup.dropIntoPromptBackground": "#f8f8f8", "editorGroup.dropIntoPromptForeground": "#3b3b3b", "editorGroupHeader.noTabsBackground": "#ffffff", "editorGutter.addedSecondaryBackground": "#83db93", "editorGutter.background": "#ffffff", "editorGutter.commentGlyphForeground": "#3b3b3b", "editorGutter.commentRangeForeground": "#d5d8e9", "editorGutter.commentUnresolvedGlyphForeground": "#3b3b3b", "editorGutter.deletedSecondaryBackground": "#fcaaa6", "editorGutter.foldingControlForeground": "#3b3b3b", "editorGutter.itemBackground": "#d5d8e9", "editorGutter.itemGlyphForeground": "#3b3b3b", "editorGutter.modifiedSecondaryBackground": "#3aa0ff", "editorHint.foreground": "#6c6c6c", "editorHoverWidget.background": "#f8f8f8", "editorHoverWidget.border": "#c8c8c8", "editorHoverWidget.foreground": "#3b3b3b", "editorHoverWidget.highlightForeground": "#0066bf", "editorHoverWidget.statusBarBackground": "#ececec", "editorIndentGuide.activeBackground2": "#00000000", "editorIndentGuide.activeBackground3": "#00000000", "editorIndentGuide.activeBackground4": "#00000000", "editorIndentGuide.activeBackground5": "#00000000", "editorIndentGuide.activeBackground6": "#00000000", "editorIndentGuide.background2": "#00000000", "editorIndentGuide.background3": "#00000000", "editorIndentGuide.background4": "#00000000", "editorIndentGuide.background5": "#00000000", "editorIndentGuide.background6": "#00000000", "editorInfo.foreground": "#1a85ff", "editorInlayHint.background": "#cccccc1a", "editorInlayHint.foreground": "#969696", "editorInlayHint.parameterBackground": "#cccccc1a", "editorInlayHint.parameterForeground": "#969696", "editorInlayHint.typeBackground": "#cccccc1a", "editorInlayHint.typeForeground": "#969696", "editorLightBulb.foreground": "#ddb100", "editorLightBulbAi.foreground": "#ddb100", "editorLightBulbAutoFix.foreground": "#007acc", "editorLink.activeForeground": "#0000ff", "editorMarkerNavigation.background": "#ffffff", "editorMarkerNavigationError.background": "#e51400", "editorMarkerNavigationError.headerBackground": "#e514001a", "editorMarkerNavigationInfo.background": "#1a85ff", "editorMarkerNavigationInfo.headerBackground": "#1a85ff1a", "editorMarkerNavigationWarning.background": "#bf8803", "editorMarkerNavigationWarning.headerBackground": "#bf88031a", "editorMinimap.inlineChatInserted": "#9ccc2c33", "editorMultiCursor.primary.foreground": "#000000", "editorMultiCursor.secondary.foreground": "#000000", "editorOverviewRuler.addedForeground": "#2ea04399", "editorOverviewRuler.bracketMatchForeground": "#a0a0a0", "editorOverviewRuler.commentForeground": "#d5d8e9", "editorOverviewRuler.commentUnresolvedForeground": "#d5d8e9", "editorOverviewRuler.commonContentForeground": "#60606066", "editorOverviewRuler.currentContentForeground": "#40c8ae80", "editorOverviewRuler.deletedForeground": "#f8514999", "editorOverviewRuler.errorForeground": "#ff1212b3", "editorOverviewRuler.findMatchForeground": "#d186167e", "editorOverviewRuler.incomingContentForeground": "#40a6ff80", "editorOverviewRuler.infoForeground": "#1a85ff", "editorOverviewRuler.inlineChatInserted": "#9ccc2c33", "editorOverviewRuler.inlineChatRemoved": "#ff000029", "editorOverviewRuler.modifiedForeground": "#005fb899", "editorOverviewRuler.rangeHighlightForeground": "#007acc99", "editorOverviewRuler.selectionHighlightForeground": "#a0a0a0cc", "editorOverviewRuler.warningForeground": "#bf8803", "editorOverviewRuler.wordHighlightForeground": "#a0a0a0cc", "editorOverviewRuler.wordHighlightStrongForeground": "#c0a0c0cc", "editorOverviewRuler.wordHighlightTextForeground": "#a0a0a0cc", "editorPane.background": "#ffffff", "editorRuler.foreground": "#d3d3d3", "editorStickyScroll.background": "#ffffff", "editorStickyScroll.shadow": "#dddddd", "editorStickyScrollHover.background": "#f0f0f0", "editorSuggestWidget.border": "#c8c8c8", "editorSuggestWidget.focusHighlightForeground": "#0066bf", "editorSuggestWidget.foreground": "#3b3b3b", "editorSuggestWidget.highlightForeground": "#0066bf", "editorSuggestWidget.selectedBackground": "#e8e8e8", "editorSuggestWidget.selectedForeground": "#000000", "editorSuggestWidget.selectedIconForeground": "#000000", "editorSuggestWidgetStatus.foreground": "#3b3b3b80", "editorUnicodeHighlight.border": "#bf8803", "editorUnnecessaryCode.opacity": "#00000077", "editorWarning.foreground": "#bf8803", "editorWatermark.foreground": "#3b3b3bad", "editorWhitespace.foreground": "#33333333", "editorWidget.border": "#c8c8c8", "editorWidget.foreground": "#3b3b3b", "extensionBadge.remoteBackground": "#005fb8", "extensionBadge.remoteForeground": "#ffffff", "extensionButton.background": "#005fb8", "extensionButton.foreground": "#ffffff", "extensionButton.hoverBackground": "#0258a8", "extensionButton.prominentBackground": "#005fb8", "extensionButton.prominentForeground": "#ffffff", "extensionButton.prominentHoverBackground": "#0258a8", "extensionButton.separator": "#ffffff66", "extensionIcon.preReleaseForeground": "#1d9271", "extensionIcon.privateForeground": "#00000060", "extensionIcon.sponsorForeground": "#b51e78", "extensionIcon.starForeground": "#df6100", "extensionIcon.verifiedForeground": "#005fb8", "gauge.background": "#007acc", "gauge.errorBackground": "#be1100", "gauge.errorForeground": "#be11004d", "gauge.foreground": "#007acc4d", "gauge.warningBackground": "#b89500", "gauge.warningForeground": "#b895004d", "git.blame.editorDecorationForeground": "#969696", "gitDecoration.addedResourceForeground": "#587c0c", "gitDecoration.conflictingResourceForeground": "#ad0707", "gitDecoration.deletedResourceForeground": "#ad0707", "gitDecoration.ignoredResourceForeground": "#8e8e90", "gitDecoration.modifiedResourceForeground": "#895503", "gitDecoration.renamedResourceForeground": "#007100", "gitDecoration.stageDeletedResourceForeground": "#ad0707", "gitDecoration.stageModifiedResourceForeground": "#895503", "gitDecoration.submoduleResourceForeground": "#1258a7", "gitDecoration.untrackedResourceForeground": "#007100", "githd.blameView.info": "#237893", "githd.historyView.author": "#001080", "githd.historyView.branch": "#af00db", "githd.historyView.email": "#795e26", "githd.historyView.filePath": "#811f3f", "githd.historyView.hash": "#a31515", "githd.historyView.more": "#001080", "githd.historyView.ref": "#008000", "githd.historyView.subject": "#0000ff", "githd.historyView.title": "#267f99", "githd.infoView.content": "#008000", "githd.infoView.new": "#09885a", "githd.infoView.old": "#a31515", "githd.infoView.path": "#000080", "gitlens.closedAutolinkedIssueIconColor": "#8250df", "gitlens.closedPullRequestIconColor": "#cf222e", "gitlens.decorations.addedForegroundColor": "#587c0c", "gitlens.decorations.branchAheadForegroundColor": "#35b15e", "gitlens.decorations.branchBehindForegroundColor": "#b15e35", "gitlens.decorations.branchDivergedForegroundColor": "#d8af1b", "gitlens.decorations.branchMissingUpstreamForegroundColor": "#ad0707", "gitlens.decorations.branchUnpublishedForegroundColor": "#3b3b3b", "gitlens.decorations.branchUpToDateForegroundColor": "#3b3b3b", "gitlens.decorations.copiedForegroundColor": "#007100", "gitlens.decorations.deletedForegroundColor": "#ad0707", "gitlens.decorations.ignoredForegroundColor": "#8e8e90", "gitlens.decorations.modifiedForegroundColor": "#895503", "gitlens.decorations.renamedForegroundColor": "#007100", "gitlens.decorations.statusMergingOrRebasingConflictForegroundColor": "#ad0707", "gitlens.decorations.statusMergingOrRebasingForegroundColor": "#d8af1b", "gitlens.decorations.untrackedForegroundColor": "#007100", "gitlens.decorations.workspaceCurrentForegroundColor": "#35b15e", "gitlens.decorations.workspaceRepoMissingForegroundColor": "#949494", "gitlens.decorations.workspaceRepoOpenForegroundColor": "#35b15e", "gitlens.decorations.worktreeHasUncommittedChangesForegroundColor": "#895503", "gitlens.decorations.worktreeMissingForegroundColor": "#ad0707", "gitlens.graphChangesColumnAddedColor": "#2da44e", "gitlens.graphChangesColumnDeletedColor": "#cf222e", "gitlens.graphLane10Color": "#2ece9d", "gitlens.graphLane1Color": "#15a0bf", "gitlens.graphLane2Color": "#0669f7", "gitlens.graphLane3Color": "#8e00c2", "gitlens.graphLane4Color": "#c517b6", "gitlens.graphLane5Color": "#d90171", "gitlens.graphLane6Color": "#cd0101", "gitlens.graphLane7Color": "#f25d2e", "gitlens.graphLane8Color": "#f2ca33", "gitlens.graphLane9Color": "#7bd938", "gitlens.graphMinimapMarkerHeadColor": "#04c814", "gitlens.graphMinimapMarkerHighlightsColor": "#f5cc00", "gitlens.graphMinimapMarkerLocalBranchesColor": "#3095e8", "gitlens.graphMinimapMarkerPullRequestsColor": "#ff8f18", "gitlens.graphMinimapMarkerRemoteBranchesColor": "#67ace4", "gitlens.graphMinimapMarkerStashesColor": "#e467e4", "gitlens.graphMinimapMarkerTagsColor": "#d2a379", "gitlens.graphMinimapMarkerUpstreamColor": "#8cd993", "gitlens.graphScrollMarkerHeadColor": "#04c814", "gitlens.graphScrollMarkerHighlightsColor": "#f5cc00", "gitlens.graphScrollMarkerLocalBranchesColor": "#3095e8", "gitlens.graphScrollMarkerPullRequestsColor": "#ff8f18", "gitlens.graphScrollMarkerRemoteBranchesColor": "#67ace4", "gitlens.graphScrollMarkerStashesColor": "#e467e4", "gitlens.graphScrollMarkerTagsColor": "#d2a379", "gitlens.graphScrollMarkerUpstreamColor": "#8cd993", "gitlens.gutterBackgroundColor": "#0000000c", "gitlens.gutterForegroundColor": "#747474", "gitlens.gutterUncommittedForegroundColor": "#00bcf299", "gitlens.launchpadIndicatorAttentionColor": "#cc9b15", "gitlens.launchpadIndicatorAttentionHoverColor": "#cc9b15", "gitlens.launchpadIndicatorBlockedColor": "#ad0707", "gitlens.launchpadIndicatorBlockedHoverColor": "#ad0707", "gitlens.launchpadIndicatorMergeableColor": "#42c954", "gitlens.launchpadIndicatorMergeableHoverColor": "#42c954", "gitlens.lineHighlightBackgroundColor": "#00bcf233", "gitlens.lineHighlightOverviewRulerColor": "#00bcf299", "gitlens.mergedPullRequestIconColor": "#8250df", "gitlens.openAutolinkedIssueIconColor": "#1a7f37", "gitlens.openPullRequestIconColor": "#1a7f37", "gitlens.trailingLineBackgroundColor": "#00000000", "gitlens.trailingLineForegroundColor": "#99999959", "gitlens.unpublishedChangesIconColor": "#35b15e", "gitlens.unpublishedCommitIconColor": "#35b15e", "gitlens.unpulledChangesIconColor": "#b15e35", "inlineChat.background": "#f8f8f8", "inlineChat.border": "#c8c8c8", "inlineChat.foreground": "#3b3b3b", "inlineChat.shadow": "#00000029", "inlineChatDiff.inserted": "#9ccc2c20", "inlineChatDiff.removed": "#ff00001a", "inlineChatInput.background": "#ffffff", "inlineChatInput.border": "#c8c8c8", "inlineChatInput.focusBorder": "#005fb8", "inlineChatInput.placeholderForeground": "#767676", "inlineEdit.gutterIndicator.background": "#5f5f5f18", "inlineEdit.gutterIndicator.primaryBackground": "#005fb880", "inlineEdit.gutterIndicator.primaryBorder": "#005fb8", "inlineEdit.gutterIndicator.primaryForeground": "#ffffff", "inlineEdit.gutterIndicator.secondaryBackground": "#e5e5e5", "inlineEdit.gutterIndicator.secondaryBorder": "#e5e5e5", "inlineEdit.gutterIndicator.secondaryForeground": "#3b3b3b", "inlineEdit.gutterIndicator.successfulBackground": "#005fb8", "inlineEdit.gutterIndicator.successfulBorder": "#005fb8", "inlineEdit.gutterIndicator.successfulForeground": "#ffffff", "inlineEdit.modifiedBackground": "#9ccc2c13", "inlineEdit.modifiedBorder": "#3e511240", "inlineEdit.modifiedChangedLineBackground": "#9bb95524", "inlineEdit.modifiedChangedTextBackground": "#9ccc2c2d", "inlineEdit.originalBackground": "#ff00000a", "inlineEdit.originalBorder": "#ff000033", "inlineEdit.originalChangedLineBackground": "#ff000029", "inlineEdit.originalChangedTextBackground": "#ff000029", "inlineEdit.tabWillAcceptModifiedBorder": "#3e511240", "inlineEdit.tabWillAcceptOriginalBorder": "#ff000033", "inputOption.hoverBackground": "#b8b8b850", "inputValidation.errorBackground": "#f2dede", "inputValidation.errorBorder": "#be1100", "inputValidation.infoBackground": "#d6ecf2", "inputValidation.infoBorder": "#007acc", "inputValidation.warningBackground": "#f6f5d2", "inputValidation.warningBorder": "#b89500", "interactive.activeCodeBorder": "#007acc", "interactive.inactiveCodeBorder": "#e4e6f1", "keybindingLabel.background": "#dddddd66", "keybindingLabel.border": "#cccccc66", "keybindingLabel.bottomBorder": "#bbbbbb66", "keybindingTable.headerBackground": "#3b3b3b0a", "keybindingTable.rowsBackground": "#3b3b3b0a", "list.deemphasizedForeground": "#8e8e90", "list.dropBackground": "#d6ebff", "list.dropBetweenBackground": "#3b3b3b", "list.errorForeground": "#b01011", "list.filterMatchBackground": "#ea5c0055", "list.focusHighlightForeground": "#0066bf", "list.focusOutline": "#005fb8", "list.highlightForeground": "#0066bf", "list.inactiveSelectionBackground": "#e4e6f1", "list.invalidItemForeground": "#b89500", "list.warningForeground": "#855f00", "listFilterWidget.background": "#f8f8f8", "listFilterWidget.noMatchesOutline": "#be1100", "listFilterWidget.outline": "#00000000", "listFilterWidget.shadow": "#00000029", "menu.background": "#ffffff", "menu.foreground": "#3b3b3b", "menu.separatorBackground": "#d4d4d4", "menubar.selectionBackground": "#b8b8b850", "menubar.selectionForeground": "#1e1e1e", "merge.commonContentBackground": "#60606029", "merge.commonHeaderBackground": "#60606066", "merge.currentContentBackground": "#40c8ae33", "merge.currentHeaderBackground": "#40c8ae80", "merge.incomingContentBackground": "#40a6ff33", "merge.incomingHeaderBackground": "#40a6ff80", "mergeEditor.change.background": "#9bb95533", "mergeEditor.change.word.background": "#9ccc2c66", "mergeEditor.changeBase.background": "#ffcccc", "mergeEditor.changeBase.word.background": "#ffa3a3", "mergeEditor.conflict.handled.minimapOverViewRuler": "#adaca8ee", "mergeEditor.conflict.handledFocused.border": "#c1c1c1cc", "mergeEditor.conflict.handledUnfocused.border": "#86868649", "mergeEditor.conflict.input1.background": "#40c8ae33", "mergeEditor.conflict.input2.background": "#40a6ff33", "mergeEditor.conflict.unhandled.minimapOverViewRuler": "#fcba03", "mergeEditor.conflict.unhandledFocused.border": "#ffa600", "mergeEditor.conflict.unhandledUnfocused.border": "#ffa600", "mergeEditor.conflictingLines.background": "#ffea0047", "minimap.chatEditHighlight": "#ffffff99", "minimap.errorHighlight": "#ff1212b3", "minimap.findMatchHighlight": "#d18616", "minimap.foregroundOpacity": "#000000", "minimap.infoHighlight": "#1a85ff", "minimap.selectionHighlight": "#add6ff", "minimap.selectionOccurrenceHighlight": "#c9c9c9", "minimap.warningHighlight": "#bf8803", "minimapGutter.addedBackground": "#2ea043", "minimapGutter.deletedBackground": "#f85149", "minimapGutter.modifiedBackground": "#005fb8", "minimapSlider.activeBackground": "#0000004d", "minimapSlider.background": "#64646433", "minimapSlider.hoverBackground": "#64646459", "multiDiffEditor.background": "#ffffff", "multiDiffEditor.border": "#cccccc", "multiDiffEditor.headerBackground": "#f8f8f8", "notebook.cellEditorBackground": "#f8f8f8", "notebook.cellInsertionIndicator": "#005fb8", "notebook.cellStatusBarItemHoverBackground": "#00000014", "notebook.cellToolbarSeparator": "#80808059", "notebook.editorBackground": "#ffffff", "notebook.focusedCellBorder": "#005fb8", "notebook.focusedEditorBorder": "#005fb8", "notebook.inactiveFocusedCellBorder": "#e5e5e5", "notebook.selectedCellBorder": "#e5e5e5", "notebook.symbolHighlightBackground": "#fdff0033", "notebookEditorOverviewRuler.runningCellForeground": "#388a34", "notebookScrollbarSlider.activeBackground": "#00000099", "notebookScrollbarSlider.background": "#64646466", "notebookScrollbarSlider.hoverBackground": "#646464b3", "notebookStatusErrorIcon.foreground": "#f85149", "notebookStatusRunningIcon.foreground": "#3b3b3b", "notebookStatusSuccessIcon.foreground": "#388a34", "notificationCenter.border": "#e5e5e5", "notificationLink.foreground": "#005fb8", "notificationToast.border": "#e5e5e5", "notificationsErrorIcon.foreground": "#e51400", "notificationsInfoIcon.foreground": "#1a85ff", "notificationsWarningIcon.foreground": "#bf8803", "panel.dropBorder": "#3b3b3b", "panelSection.border": "#e5e5e5", "panelSection.dropBackground": "#2677cb2e", "panelSectionHeader.background": "#80808033", "panelStickyScroll.background": "#f8f8f8", "panelStickyScroll.shadow": "#dddddd", "panelTitleBadge.background": "#005fb8", "panelTitleBadge.foreground": "#ffffff", "peekView.border": "#1a85ff", "peekViewEditor.background": "#f2f8fc", "peekViewEditorGutter.background": "#f2f8fc", "peekViewEditorStickyScroll.background": "#f2f8fc", "peekViewResult.fileForeground": "#1e1e1e", "peekViewResult.lineForeground": "#646465", "peekViewResult.selectionBackground": "#3399ff33", "peekViewResult.selectionForeground": "#6c6c6c", "peekViewTitle.background": "#f3f3f3", "peekViewTitleDescription.foreground": "#616161", "peekViewTitleLabel.foreground": "#000000", "problemsErrorIcon.foreground": "#e51400", "problemsInfoIcon.foreground": "#1a85ff", "problemsWarningIcon.foreground": "#bf8803", "profileBadge.background": "#c4c4c4", "profileBadge.foreground": "#333333", "profiles.sashBorder": "#e5e5e5", "prompt.frontMatter.background": "#f2f2f2", "prompt.frontMatter.inactiveBackground": "#f9f9f9", "quickInputList.focusBackground": "#e8e8e8", "quickInputList.focusForeground": "#000000", "quickInputList.focusIconForeground": "#000000", "quickInputTitle.background": "#0000000f", "radio.activeBackground": "#bed6ed", "radio.activeBorder": "#005fb8", "radio.activeForeground": "#000000", "radio.inactiveBorder": "#00000033", "radio.inactiveHoverBackground": "#b8b8b850", "sash.hoverBorder": "#005fb8", "scmGraph.foreground1": "#ffb000", "scmGraph.foreground2": "#dc267f", "scmGraph.foreground3": "#994f00", "scmGraph.foreground4": "#40b0a6", "scmGraph.foreground5": "#b66dff", "scmGraph.historyItemBaseRefColor": "#ea5c00", "scmGraph.historyItemHoverAdditionsForeground": "#587c0c", "scmGraph.historyItemHoverDefaultLabelBackground": "#cccccc", "scmGraph.historyItemHoverDefaultLabelForeground": "#3b3b3b", "scmGraph.historyItemHoverDeletionsForeground": "#ad0707", "scmGraph.historyItemHoverLabelForeground": "#ffffff", "scmGraph.historyItemRefColor": "#1a85ff", "scmGraph.historyItemRemoteRefColor": "#652d90", "scrollbar.shadow": "#dddddd", "scrollbarSlider.activeBackground": "#00000099", "scrollbarSlider.background": "#64646466", "scrollbarSlider.hoverBackground": "#646464b3", "search.resultsInfoForeground": "#3b3b3b", "searchEditor.findMatchBackground": "#ea5c0038", "settings.checkboxBackground": "#f8f8f8", "settings.checkboxBorder": "#cecece", "settings.checkboxForeground": "#3b3b3b", "settings.dropdownForeground": "#3b3b3b", "settings.dropdownListBorder": "#c8c8c8", "settings.focusedRowBackground": "#f2f2f299", "settings.focusedRowBorder": "#005fb8", "settings.headerBorder": "#e5e5e5", "settings.numberInputBackground": "#ffffff", "settings.numberInputForeground": "#3b3b3b", "settings.rowHoverBackground": "#f2f2f24d", "settings.sashBorder": "#e5e5e5", "settings.settingsHeaderHoverForeground": "#1f1f1fb3", "settings.textInputBackground": "#ffffff", "settings.textInputForeground": "#3b3b3b", "sideBar.dropBackground": "#2677cb2e", "sideBarActivityBarTop.border": "#e5e5e5", "sideBarStickyScroll.background": "#f8f8f8", "sideBarStickyScroll.shadow": "#dddddd", "sideBarTitle.background": "#f8f8f8", "sideBySideEditor.horizontalBorder": "#e5e5e5", "sideBySideEditor.verticalBorder": "#e5e5e5", "simpleFindWidget.sashBorder": "#c8c8c8", "statusBar.debuggingBorder": "#e5e5e5", "statusBar.noFolderBorder": "#e5e5e5", "statusBar.noFolderForeground": "#3b3b3b", "statusBarItem.activeBackground": "#ffffff2e", "statusBarItem.errorForeground": "#ffffff", "statusBarItem.errorHoverBackground": "#b8b8b850", "statusBarItem.errorHoverForeground": "#3b3b3b", "statusBarItem.hoverForeground": "#3b3b3b", "statusBarItem.offlineBackground": "#6c1717", "statusBarItem.offlineForeground": "#ffffff", "statusBarItem.offlineHoverBackground": "#b8b8b850", "statusBarItem.offlineHoverForeground": "#3b3b3b", "statusBarItem.prominentForeground": "#3b3b3b", "statusBarItem.prominentHoverBackground": "#b8b8b850", "statusBarItem.prominentHoverForeground": "#3b3b3b", "statusBarItem.remoteHoverBackground": "#b8b8b850", "statusBarItem.remoteHoverForeground": "#3b3b3b", "statusBarItem.warningBackground": "#725102", "statusBarItem.warningForeground": "#ffffff", "statusBarItem.warningHoverBackground": "#b8b8b850", "statusBarItem.warningHoverForeground": "#3b3b3b", "symbolIcon.arrayForeground": "#3b3b3b", "symbolIcon.booleanForeground": "#3b3b3b", "symbolIcon.classForeground": "#d67e00", "symbolIcon.colorForeground": "#3b3b3b", "symbolIcon.constantForeground": "#3b3b3b", "symbolIcon.constructorForeground": "#652d90", "symbolIcon.enumeratorForeground": "#d67e00", "symbolIcon.enumeratorMemberForeground": "#007acc", "symbolIcon.eventForeground": "#d67e00", "symbolIcon.fieldForeground": "#007acc", "symbolIcon.fileForeground": "#3b3b3b", "symbolIcon.folderForeground": "#3b3b3b", "symbolIcon.functionForeground": "#652d90", "symbolIcon.interfaceForeground": "#007acc", "symbolIcon.keyForeground": "#3b3b3b", "symbolIcon.keywordForeground": "#3b3b3b", "symbolIcon.methodForeground": "#652d90", "symbolIcon.moduleForeground": "#3b3b3b", "symbolIcon.namespaceForeground": "#3b3b3b", "symbolIcon.nullForeground": "#3b3b3b", "symbolIcon.numberForeground": "#3b3b3b", "symbolIcon.objectForeground": "#3b3b3b", "symbolIcon.operatorForeground": "#3b3b3b", "symbolIcon.packageForeground": "#3b3b3b", "symbolIcon.propertyForeground": "#3b3b3b", "symbolIcon.referenceForeground": "#3b3b3b", "symbolIcon.snippetForeground": "#3b3b3b", "symbolIcon.stringForeground": "#3b3b3b", "symbolIcon.structForeground": "#3b3b3b", "symbolIcon.textForeground": "#3b3b3b", "symbolIcon.typeParameterForeground": "#3b3b3b", "symbolIcon.unitForeground": "#3b3b3b", "symbolIcon.variableForeground": "#007acc", "tab.activeModifiedBorder": "#33aaee", "tab.dragAndDropBorder": "#3b3b3b", "tab.inactiveModifiedBorder": "#33aaee80", "tab.unfocusedActiveBackground": "#ffffff", "tab.unfocusedActiveForeground": "#3b3b3bb3", "tab.unfocusedActiveModifiedBorder": "#33aaeeb3", "tab.unfocusedInactiveBackground": "#f8f8f8", "tab.unfocusedInactiveForeground": "#86868680", "tab.unfocusedInactiveModifiedBorder": "#33aaee40", "terminal.ansiBlack": "#000000", "terminal.ansiBlue": "#0451a5", "terminal.ansiBrightBlack": "#666666", "terminal.ansiBrightBlue": "#0451a5", "terminal.ansiBrightCyan": "#0598bc", "terminal.ansiBrightGreen": "#14ce14", "terminal.ansiBrightMagenta": "#bc05bc", "terminal.ansiBrightRed": "#cd3131", "terminal.ansiBrightWhite": "#a5a5a5", "terminal.ansiBrightYellow": "#b5ba00", "terminal.ansiCyan": "#0598bc", "terminal.ansiGreen": "#107c10", "terminal.ansiMagenta": "#bc05bc", "terminal.ansiRed": "#cd3131", "terminal.ansiWhite": "#555555", "terminal.ansiYellow": "#949800", "terminal.border": "#e5e5e5", "terminal.dropBackground": "#2677cb2e", "terminal.findMatchBackground": "#a8ac94", "terminal.findMatchHighlightBackground": "#ea5c0055", "terminal.hoverHighlightBackground": "#add6ff13", "terminal.initialHintForeground": "#00000077", "terminal.selectionBackground": "#add6ff", "terminalCommandDecoration.defaultBackground": "#00000040", "terminalCommandDecoration.errorBackground": "#e51400", "terminalCommandDecoration.successBackground": "#2090d3", "terminalCommandGuide.foreground": "#e4e6f1", "terminalOverviewRuler.border": "#e5e5e5", "terminalOverviewRuler.cursorForeground": "#a0a0a0cc", "terminalOverviewRuler.findMatchForeground": "#d186167e", "terminalStickyScrollHover.background": "#f0f0f0", "terminalSymbolIcon.aliasForeground": "#652d90", "terminalSymbolIcon.argumentForeground": "#007acc", "terminalSymbolIcon.fileForeground": "#3b3b3b", "terminalSymbolIcon.flagForeground": "#d67e00", "terminalSymbolIcon.folderForeground": "#3b3b3b", "terminalSymbolIcon.methodForeground": "#652d90", "terminalSymbolIcon.optionForeground": "#d67e00", "terminalSymbolIcon.optionValueForeground": "#007acc", "testing.coverCountBadgeBackground": "#cccccc", "testing.coverCountBadgeForeground": "#3b3b3b", "testing.coveredBackground": "#9ccc2c40", "testing.coveredBorder": "#9ccc2c30", "testing.coveredGutterBackground": "#9ccc2c27", "testing.iconErrored": "#f14c4c", "testing.iconErrored.retired": "#f14c4cb3", "testing.iconFailed": "#f14c4c", "testing.iconFailed.retired": "#f14c4cb3", "testing.iconPassed": "#73c991", "testing.iconPassed.retired": "#73c991b3", "testing.iconQueued": "#cca700", "testing.iconQueued.retired": "#cca700b3", "testing.iconSkipped": "#848484", "testing.iconSkipped.retired": "#848484b3", "testing.iconUnset": "#848484", "testing.iconUnset.retired": "#848484b3", "testing.message.error.badgeBackground": "#e51400", "testing.message.error.badgeBorder": "#e51400", "testing.message.error.badgeForeground": "#ffffff", "testing.message.info.decorationForeground": "#3b3b3b80", "testing.messagePeekBorder": "#1a85ff", "testing.messagePeekHeaderBackground": "#1a85ff1a", "testing.peekBorder": "#e51400", "testing.peekHeaderBackground": "#e514001a", "testing.runAction": "#73c991", "testing.uncoveredBackground": "#ff000033", "testing.uncoveredBorder": "#ff000026", "testing.uncoveredBranchBackground": "#ff9999", "testing.uncoveredGutterBackground": "#ff00004d", "toolbar.activeBackground": "#a6a6a650", "toolbar.hoverBackground": "#b8b8b850", "tree.inactiveIndentGuidesStroke": "#a9a9a966", "tree.indentGuidesStroke": "#a9a9a9", "tree.tableColumnsBorder": "#61616120", "tree.tableOddRowsBackground": "#3b3b3b0a", "walkThrough.embeddedEditorBackground": "#f4f4f4", "walkthrough.stepTitle.foreground": "#000000", "welcomePage.progress.background": "#ffffff", "welcomePage.progress.foreground": "#005fb8", "welcomePage.tileBorder": "#0000001a", "welcomePage.tileHoverBackground": "#dfdfdf", "widget.shadow": "#00000029"}, "tokenColors": [{"scope": ["meta.embedded", "source.groovy.embedded", "string meta.image.inline.markdown", "variable.legacy.builtin.python"], "settings": {"foreground": "#000000"}}, {"scope": "emphasis", "settings": {"fontStyle": "italic"}}, {"scope": "strong", "settings": {"fontStyle": "bold"}}, {"scope": "meta.diff.header", "settings": {"foreground": "#000080"}}, {"scope": "comment", "settings": {"foreground": "#008000"}}, {"scope": "constant.language", "settings": {"foreground": "#0000FF"}}, {"scope": ["constant.numeric", "variable.other.enummember", "keyword.operator.plus.exponent", "keyword.operator.minus.exponent"], "settings": {"foreground": "#098658"}}, {"scope": "constant.regexp", "settings": {"foreground": "#811F3F"}}, {"scope": "entity.name.tag", "settings": {"foreground": "#800000"}}, {"scope": "entity.name.selector", "settings": {"foreground": "#800000"}}, {"scope": "entity.other.attribute-name", "settings": {"foreground": "#E50000"}}, {"scope": ["entity.other.attribute-name.class.css", "source.css entity.other.attribute-name.class", "entity.other.attribute-name.id.css", "entity.other.attribute-name.parent-selector.css", "entity.other.attribute-name.parent.less", "source.css entity.other.attribute-name.pseudo-class", "entity.other.attribute-name.pseudo-element.css", "source.css.less entity.other.attribute-name.id", "entity.other.attribute-name.scss"], "settings": {"foreground": "#800000"}}, {"scope": "invalid", "settings": {"foreground": "#CD3131"}}, {"scope": "markup.underline", "settings": {"fontStyle": "underline"}}, {"scope": "markup.bold", "settings": {"foreground": "#000080", "fontStyle": "bold"}}, {"scope": "markup.heading", "settings": {"foreground": "#800000", "fontStyle": "bold"}}, {"scope": "markup.italic", "settings": {"fontStyle": "italic"}}, {"scope": "markup.strikethrough", "settings": {"fontStyle": "strikethrough"}}, {"scope": "markup.inserted", "settings": {"foreground": "#098658"}}, {"scope": "markup.deleted", "settings": {"foreground": "#A31515"}}, {"scope": "markup.changed", "settings": {"foreground": "#0451A5"}}, {"scope": ["punctuation.definition.quote.begin.markdown", "punctuation.definition.list.begin.markdown"], "settings": {"foreground": "#0451A5"}}, {"scope": "markup.inline.raw", "settings": {"foreground": "#800000"}}, {"scope": "punctuation.definition.tag", "settings": {"foreground": "#800000"}}, {"scope": ["meta.preprocessor", "entity.name.function.preprocessor"], "settings": {"foreground": "#0000FF"}}, {"scope": "meta.preprocessor.string", "settings": {"foreground": "#A31515"}}, {"scope": "meta.preprocessor.numeric", "settings": {"foreground": "#098658"}}, {"scope": "meta.structure.dictionary.key.python", "settings": {"foreground": "#0451A5"}}, {"scope": "storage", "settings": {"foreground": "#0000FF"}}, {"scope": "storage.type", "settings": {"foreground": "#0000FF"}}, {"scope": ["storage.modifier", "keyword.operator.noexcept"], "settings": {"foreground": "#0000FF"}}, {"scope": ["string", "meta.embedded.assembly"], "settings": {"foreground": "#A31515"}}, {"scope": ["string.comment.buffered.block.pug", "string.quoted.pug", "string.interpolated.pug", "string.unquoted.plain.in.yaml", "string.unquoted.plain.out.yaml", "string.unquoted.block.yaml", "string.quoted.single.yaml", "string.quoted.double.xml", "string.quoted.single.xml", "string.unquoted.cdata.xml", "string.quoted.double.html", "string.quoted.single.html", "string.unquoted.html", "string.quoted.single.handlebars", "string.quoted.double.handlebars"], "settings": {"foreground": "#0000FF"}}, {"scope": "string.regexp", "settings": {"foreground": "#811F3F"}}, {"scope": ["punctuation.definition.template-expression.begin", "punctuation.definition.template-expression.end", "punctuation.section.embedded"], "settings": {"foreground": "#0000FF"}}, {"scope": ["meta.template.expression"], "settings": {"foreground": "#000000"}}, {"scope": ["support.constant.property-value", "support.constant.font-name", "support.constant.media-type", "support.constant.media", "constant.other.color.rgb-value", "constant.other.rgb-value", "support.constant.color"], "settings": {"foreground": "#0451A5"}}, {"scope": ["support.type.vendored.property-name", "support.type.property-name", "source.css variable", "source.coffee.embedded"], "settings": {"foreground": "#E50000"}}, {"scope": ["support.type.property-name.json"], "settings": {"foreground": "#0451A5"}}, {"scope": "keyword", "settings": {"foreground": "#0000FF"}}, {"scope": "keyword.control", "settings": {"foreground": "#0000FF"}}, {"scope": "keyword.operator", "settings": {"foreground": "#000000"}}, {"scope": ["keyword.operator.new", "keyword.operator.expression", "keyword.operator.cast", "keyword.operator.sizeof", "keyword.operator.alignof", "keyword.operator.typeid", "keyword.operator.alignas", "keyword.operator.instanceof", "keyword.operator.logical.python", "keyword.operator.wordlike"], "settings": {"foreground": "#0000FF"}}, {"scope": "keyword.other.unit", "settings": {"foreground": "#098658"}}, {"scope": ["punctuation.section.embedded.begin.php", "punctuation.section.embedded.end.php"], "settings": {"foreground": "#800000"}}, {"scope": "support.function.git-rebase", "settings": {"foreground": "#0451A5"}}, {"scope": "constant.sha.git-rebase", "settings": {"foreground": "#098658"}}, {"scope": ["storage.modifier.import.java", "variable.language.wildcard.java", "storage.modifier.package.java"], "settings": {"foreground": "#000000"}}, {"scope": "variable.language", "settings": {"foreground": "#0000FF"}}, {"scope": ["entity.name.function", "support.function", "support.constant.handlebars", "source.powershell variable.other.member", "entity.name.operator.custom-literal"], "settings": {"foreground": "#795E26"}}, {"scope": ["support.class", "support.type", "entity.name.type", "entity.name.namespace", "entity.other.attribute", "entity.name.scope-resolution", "entity.name.class", "storage.type.numeric.go", "storage.type.byte.go", "storage.type.boolean.go", "storage.type.string.go", "storage.type.uintptr.go", "storage.type.error.go", "storage.type.rune.go", "storage.type.cs", "storage.type.generic.cs", "storage.type.modifier.cs", "storage.type.variable.cs", "storage.type.annotation.java", "storage.type.generic.java", "storage.type.java", "storage.type.object.array.java", "storage.type.primitive.array.java", "storage.type.primitive.java", "storage.type.token.java", "storage.type.groovy", "storage.type.annotation.groovy", "storage.type.parameters.groovy", "storage.type.generic.groovy", "storage.type.object.array.groovy", "storage.type.primitive.array.groovy", "storage.type.primitive.groovy"], "settings": {"foreground": "#267F99"}}, {"scope": ["meta.type.cast.expr", "meta.type.new.expr", "support.constant.math", "support.constant.dom", "support.constant.json", "entity.other.inherited-class", "punctuation.separator.namespace.ruby"], "settings": {"foreground": "#267F99"}}, {"scope": ["keyword.control", "source.cpp keyword.operator.new", "source.cpp keyword.operator.delete", "keyword.other.using", "keyword.other.directive.using", "keyword.other.operator", "entity.name.operator"], "settings": {"foreground": "#AF00DB"}}, {"scope": ["variable", "meta.definition.variable.name", "support.variable", "entity.name.variable", "constant.other.placeholder"], "settings": {"foreground": "#001080"}}, {"scope": ["variable.other.constant", "variable.other.enummember"], "settings": {"foreground": "#0070C1"}}, {"scope": ["meta.object-literal.key"], "settings": {"foreground": "#001080"}}, {"scope": ["support.constant.property-value", "support.constant.font-name", "support.constant.media-type", "support.constant.media", "constant.other.color.rgb-value", "constant.other.rgb-value", "support.constant.color"], "settings": {"foreground": "#0451A5"}}, {"scope": ["punctuation.definition.group.regexp", "punctuation.definition.group.assertion.regexp", "punctuation.definition.character-class.regexp", "punctuation.character.set.begin.regexp", "punctuation.character.set.end.regexp", "keyword.operator.negation.regexp", "support.other.parenthesis.regexp"], "settings": {"foreground": "#D16969"}}, {"scope": ["constant.character.character-class.regexp", "constant.other.character-class.set.regexp", "constant.other.character-class.regexp", "constant.character.set.regexp"], "settings": {"foreground": "#811F3F"}}, {"scope": "keyword.operator.quantifier.regexp", "settings": {"foreground": "#000000"}}, {"scope": ["keyword.operator.or.regexp", "keyword.control.anchor.regexp"], "settings": {"foreground": "#EE0000"}}, {"scope": ["constant.character", "constant.other.option"], "settings": {"foreground": "#0000FF"}}, {"scope": "constant.character.escape", "settings": {"foreground": "#EE0000"}}, {"scope": "entity.name.label", "settings": {"foreground": "#000000"}}, {"scope": "ref.matchtext", "settings": {"foreground": "#000000"}}, {"scope": "token.info-token", "settings": {"foreground": "#316BCD"}}, {"scope": "token.warn-token", "settings": {"foreground": "#CD9731"}}, {"scope": "token.error-token", "settings": {"foreground": "#CD3131"}}, {"scope": "token.debug-token", "settings": {"foreground": "#800080"}}], "name": "kwaipilot-default-theme-light"}