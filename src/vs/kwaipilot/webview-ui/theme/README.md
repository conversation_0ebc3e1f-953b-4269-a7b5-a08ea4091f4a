# 如何获取 VSCode 提供的 token 值

> 一般来说不需要经常变动


根目录执行 拉取 vscode token 信息

```sh
node ./scripts/pull_latest_variable.mjs
```


# 如何设置默认的主题

> 如需修改默认主题 ，将默认主题粘贴到目标位置 再执行生成命令

Step1: 获取主题定义文件

一般来说某些开源的主题会有 theme.json，可以参考技术文档https://docs.corp.kuaishou.com/d/home/<USER>

当然你也可以进入命令模式，输入 D G 选择 Developer：Genderate Color Theme From Current Settings

Step2: 复制定义文件到 webview/theme/input-default-dark|light.json

Step3: pnpm transform:default-theme
