const stylelint = require("stylelint");

const ruleName = "custom/no-body-selector";
const messages = stylelint.utils.ruleMessages(ruleName, {
  rejected: "不要使用 body，需要考虑在ide里原生渲染情况，请使用 [data-element-type=\"panelview-root\"] 代替",
});

module.exports = stylelint.createPlugin(ruleName, (primary, secondaryOptionObject) => {
  return (root, result) => {
    root.walkRules((rule) => {
      if (rule.selector) {
        // 使用正则表达式检查是否使用了 body 作为选择器
        // 这将匹配 "body", "body.class", ".class body", "#id body" 等
        // 但不会匹配 [data-element-type="body"] 这样的属性选择器
        const bodySelector = /(?:^|\s|,)body(?:\s|$|\.|\[|:|::|\s*>|\s*\+|\s*~)/i;
        if (bodySelector.test(rule.selector)) {
          stylelint.utils.report({
            message: messages.rejected,
            node: rule,
            result,
            ruleName,
          });
        }
      }
    });
  };
});

module.exports.ruleName = ruleName;
module.exports.messages = messages;
