{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "ESNext"], "useDefineForClassFields": true, "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"], "@shared/*": ["../src/shared/*"]}, "types": ["vite/client", "vite-plugin-svgr/client"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "../src/shared/**/*.ts"], "exclude": ["node_modules", "./node_modules", "./node_modules/*", "./node_modules/@types/node/index.d.ts"], "references": [{"path": "../packages/shared"}, {"path": "../packages/artifact-message-parser"}]}