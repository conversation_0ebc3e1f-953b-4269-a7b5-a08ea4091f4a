import { type Plugin } from "vite";

export function bodyOperationCheckPlugin(): Plugin {
  return {
    name: "body-operation-check",
    transform(code, id) {
      // 只在开发模式下注入代码
      if (process.env.NODE_ENV === "development") {
        // 找到入口文件
        if (id.endsWith("main.tsx") || id.endsWith("index.tsx")) {
          const checkCode = `
// 开发模式下检测 document.body 的操作
const originalBody = document.body;

// DOM 操作方法
const domMethods = [
  // 添加节点
  'append',
  'appendChild',
  'prepend',
  'insertBefore',
  'insertAdjacentElement',
  'insertAdjacentHTML',
  'insertAdjacentText',
  // 删除节点
  'remove',
  'removeChild',
  'replaceChild',
  // 属性操作
  'setAttribute',
  'setAttributeNS',
  'removeAttribute',
  'removeAttributeNS',
  // 内容操作
  'replaceWith',
  'replaceChildren',
  // 克隆操作
  'cloneNode',
];

// classList 操作
const classListMethods = [
  'add',
  'remove',
  'toggle',
  'replace',
  'contains',
];

// style 操作
const styleMethods = [
  'setProperty',
  'removeProperty',
  'getPropertyValue',
  'getPropertyPriority',
];

// 需要忽略的框架相关代码
const ignorePatterns = [
  'react-dom',
  'react',
  'next',
  'vite',
  'webpack'
];

// 获取调用栈信息
function getCallStack() {
  const stack = new Error().stack || '';
  // 过滤掉前两行（Error 和 getCallStack 本身）
  const stackLines = stack.split('\\n').slice(2);
  // 只保留包含文件路径的行
  return stackLines
    .filter(line => line.includes('at '))
    .map(line => line.trim())
    .join('\\n');
}

// 检查是否在需要忽略的框架代码中
function shouldIgnore() {
  const stack = new Error().stack || '';
  return ignorePatterns.some(pattern => stack.includes(pattern));
}

// 重写 DOM 方法
domMethods.forEach(method => {
  const original = originalBody[method];
  if (typeof original === 'function') {
    originalBody[method] = function(...args) {
      if (!shouldIgnore()) {
        console.error(
          '不要直接对 body 进行操作，请使用 getRootContainerId() 对应的元素进行操作\\n' +
          '当前操作: ' + method + '\\n' +
          '调用栈:\\n' + getCallStack()
        );
      }
      return original.apply(this, args);
    };
  }
});

// 重写 classList 方法
classListMethods.forEach(method => {
  const original = originalBody.classList[method];
  if (typeof original === 'function') {
    originalBody.classList[method] = function(...args) {
      if (!shouldIgnore()) {
        console.error(
          '不要直接对 body 进行操作，请使用 getRootContainerId() 对应的元素进行操作\\n' +
          '当前操作: classList.' + method + '\\n' +
          '调用栈:\\n' + getCallStack()
        );
      }
      return original.apply(this, args);
    };
  }
});

// 重写 style 方法
styleMethods.forEach(method => {
  const original = originalBody.style[method];
  if (typeof original === 'function') {
    originalBody.style[method] = function(...args) {
      if (!shouldIgnore()) {
        console.error(
          '不要直接对 body 进行操作，请使用 getRootContainerId() 对应的元素进行操作\\n' +
          '当前操作: style.' + method + '\\n' +
          '调用栈:\\n' + getCallStack()
        );
      }
      return original.apply(this, args);
    };
  }
});
`;

          return {
            code: checkCode + code,
            map: null,
          };
        }
      }
      return null;
    },
  };
}
