import { DEFAULT_PROXY_URL } from "@/constant";

export interface EnvFlags {
  /** 是否是生产环境 */
  IS_PROD: boolean;
  /** 是否是预发环境 */
  IS_PREONLINE: boolean;
  /** 是否是staging测试环境 */
  IS_STAGING: boolean;
}

/* 当前环境 */
export const Env = getEnvByURL(window.proxyUrl || DEFAULT_PROXY_URL);

function getEnvByURL(url: string): EnvFlags {
  if (url.startsWith("https://pre-kinsight.corp.kuaishou.com")) {
    return {
      IS_PROD: false,
      IS_PREONLINE: true,
      IS_STAGING: false,
    };
  }
  if (url.startsWith("https://qa-kinsight.staging.kuaishou.com")) {
    return {
      IS_PROD: false,
      IS_PREONLINE: false,
      IS_STAGING: true,
    };
  }
  // 默认生产环境
  return {
    IS_PROD: true,
    IS_PREONLINE: false,
    IS_STAGING: false,
  };
}
