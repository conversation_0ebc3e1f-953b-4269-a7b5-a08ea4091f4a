import { kwaiPilotBridgeAPI } from "@/bridge";
import { httpClient } from "@/http";
import eventBus from "@/utils/eventBus";
import { InlineChatMessageEndTag } from "@shared/constant";
import { InlineChatUserIntention } from "@shared/types/inline-chat";

let abortController = new AbortController();
export const abortInlineChatChat = () => {
  abortController.abort();
};
export const getUserIntention = async (body: {
  sessionId: string;
  chatId: string;
  question: string;
}) => {
  const data = await httpClient.getUserIntention(body, abortController.signal);
  if (data === "TEST") {
    return InlineChatUserIntention.TEXT;
  }
  else if (data === "EXPLAIN") {
    return InlineChatUserIntention.EXPLAIN;
  }
  else {
    return InlineChatUserIntention.CODE;
  }
};

export async function startInlineChat(body: any) {
  const messageEmitter = new MessageEmitter();
  let buffer = "";
  let ignore = false;
  const endTag = "```";
  const maybeEndTag = "`";
  // 将模型返回的代码收起来，续问时要携带
  abortController = new AbortController();
  httpClient.fetchEventSource("/eapi/kwaipilot/chat/v2/generate", {
    method: "POST",
    body: JSON.stringify(body),
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
    signal: abortController.signal,
    onmessage: (event: any) => {
      if (ignore) return;
      const data = JSON.parse(event.data);
      if (data.type === "content" && data["content"] && data["content"] != "") {
        /**
         * 这里处理流式响应截断 ```，需要考虑的问题
         * 1. ```不一定同时返回
         * 2. xxx```其中xxx是有效数据，需要加进去
         */
        buffer += data.content;
        if (buffer.includes(endTag)) {
          // buffer 中直接包含 ``` ，将 ```前不为空的内容 push 到队列
          const lastData = buffer.split(endTag)[0];
          ignore = true;
          buffer = "";
          messageEmitter.updateMessage(lastData);
        }
        else if (buffer.endsWith(maybeEndTag)) {
          // 如果 buffer以`结尾，啥也不做，buffer会拼接一次，知道匹配到```或走 else，将buffer清空
        }
        else {
          messageEmitter.updateMessage(buffer);
          buffer = "";
        }
      }
    },
    onclose: () => {
      messageEmitter.updateMessage(InlineChatMessageEndTag);

      eventBus.emit("inlineChat:messageDone", "代码已生成，请在代码编辑区查看");
    },
    onerror(e: any) {
      if (e.type !== "aborted") {
        eventBus.emit("inlineChat:messageDone", "抱歉，服务不可用，请稍后重试");
      }
    },
  });
}
/**
 * 消息发射器
 * 当缓存的消息包含换行符之后发射前一行
 */
class MessageEmitter {
  private message = "";
  updateMessage(message: string) {
    this.message += message;
    if (this.message.includes("\n")) {
      const lastMessage = this.message.split("\n")[0];
      this.message = this.message.slice(lastMessage.length + 1);
      // 发出消息
      // eventBus.emit("inlineChat:message", lastMessage);
      kwaiPilotBridgeAPI.streamDiffMessage({
        message: lastMessage + "\n",
      });
    }
    if (this.message.includes(InlineChatMessageEndTag)) {
      kwaiPilotBridgeAPI.streamDiffMessage({
        message: this.message.slice(0, -InlineChatMessageEndTag.length) + "\n",
      });
      kwaiPilotBridgeAPI.streamDiffMessage({
        message: InlineChatMessageEndTag,
      });
      this.message = "";
    }
  }
}
