// chat 类型
export enum SseMessageEnum {
  HEART_BEAT = "HEART_BEAT",
  CODE_CHAT = "CodeChat",
  GPT = "gpt",
  KWAI_CHAT = "kwaiChat",
  CHAT_GPT = "ChatGPT",
  AUTO_IDENTIFY = "intelligentChat",
  MESSAGE_DONE = "MESSAGE_DONE",
  ERROR = "ERROR",
}

export enum SsePostTypeEnum {
  RECEIVE_MESSAGE = "RECEIVE_MESSAGE",
  GENERATE_COMMENT_MESSAGE = "GENERATE_COMMENT_MESSAGE",
  GENERATE_UNIT_TEST_MESSAGE = "GENERATE_UNIT_TEST_MESSAGE",
  RECEIVE_MESSAGE_DONE = "RECEIVE_MESSAGE_DONE",
}

export type IFeedbackLog = {
  chatId?: string;
  modelType?: string;
  question?: string;
  reply?: string;
  docList?: any[];
  feedback?: string;
  username?: string;
  topK?: number;
  infraPlatform?: string;
  platform?: string;
  pluginVersion?: string;
  biz?: string;
  deviceId?: string;
  deviceName?: string;
};

/** 点赞反馈埋点参数 */
export class FeedbackLogParam {
  detail: IFeedbackLog;
  constructor(detail: IFeedbackLog) {
    this.detail = detail;
  }
}

export type InstantApplyBody = {
  files: {
    filePath: string;
    fileContent: string;
  }[];
  modelOutput: string;
  // 日志相关
  sessionId: string;
  applyId: string;
  chatId: string;
  platform: string;
  username: string;
};
