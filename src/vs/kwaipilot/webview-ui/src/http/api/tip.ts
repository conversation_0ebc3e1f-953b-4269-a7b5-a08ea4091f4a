import { httpClient } from "@/http";
import dayjs from "dayjs";
import { WidgetConfig } from "@/http/interface";

export const getTipsConfig = async (): Promise<WidgetConfig | null> => {
  const info = await httpClient.getWidget();
  if (!info.platform.includes("vscode")) {
    return null;
  }
  let start = dayjs(info.startTime);
  let end = dayjs(info.endTime);
  const now = dayjs();
  if (start > end) {
    [start, end] = [end, start];
  }
  if (now < start || now > end) {
    return null;
  }
  return info;
};
