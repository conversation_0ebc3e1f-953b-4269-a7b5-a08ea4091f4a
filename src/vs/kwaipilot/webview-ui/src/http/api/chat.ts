import { IChatModelType } from "@shared/types/business";
import { httpClient } from "@/http";
import { SseMessageEnum } from "./type";
import eventBus from "@/utils/eventBus";
import { logger } from "@/utils/logger";
import baseInfoManager from "@/utils/baseInfo";
import repoChatService from "@/services/repo-chat";
import { getRecordStoreByVendor, RecordStoreVendor } from "@/store/record";
import { getUserInfo } from "@/utils/getUserInfo";
import { DefaultBaseUrl } from "shared/lib/const";

export enum ChatRequestType {
  normal = "COMMON_CHAT",
  composer = "COMPOSER",
}

export const fetchMessageAndSendWebView = async (params: {
  content: string;
  uniqueId: string;
  chatRecords: any[];
  chatType: any;
  sessionId: string;
  isResend: boolean;
  useSearch: boolean;
  refFiles: number[];
  rules?: string[];
  chatModel: {
    modelType: IChatModelType;
  };
  linkRepoId: string;
  refLinks?: string[];
  beginTimestamp: number;
  /* 指定向哪个 tab 发送消息 */
  vendor: RecordStoreVendor;
  requestType: ChatRequestType;
  startTime: number;
}) => {
  const {
    content: prompt,
    uniqueId,
    chatRecords,
    chatType,
    sessionId,
    useSearch = false,
    refFiles = [],
    chatModel,
    linkRepoId,
    refLinks = [],
    vendor,
    requestType,
    startTime,
    rules,
  } = params;

  const username = (await getUserInfo())?.name ?? "KwaiStaff";

  let searchResult: any[] = [];
  let tip: any = "";
  const sourceTab = chatType || SseMessageEnum.CHAT_GPT;
  const isChatGpt = sourceTab === SseMessageEnum.CHAT_GPT;
  const pluginVersion = baseInfoManager.pluginVersion;
  const platform = baseInfoManager.ide;
  const data: any = {
    sessionId: sessionId,
    chatId: uniqueId,
    question: prompt,
    chatRecordList: chatRecords,
    sourceTab,
    useSearch: isChatGpt ? undefined : useSearch,
    refFiles: isChatGpt ? undefined : refFiles,
    chatModelParam: isChatGpt ? undefined : chatModel,
    linkRepoId: isChatGpt ? undefined : linkRepoId,
    refLinks: isChatGpt ? undefined : refLinks,
    requestType,
    rules,

    // 以下为基本信息
    username,
    deviceId: baseInfoManager.deviceId,
    platform,
    pluginVersion,
    deviceName: baseInfoManager.hostname,
    deviceModel: baseInfoManager.platform,
    deviceOsVersion: baseInfoManager.release,
    projectName: repoChatService.repoName,
    gitRemote: repoChatService.remoteUrl,
    branchName: await repoChatService.getBranch(),
    openedFilePath: repoChatService.currentFilePath,
  };

  const costReportCommonData = {
    namespace: "chat",
    username: username,
    platform,
    modelType: isChatGpt ? "ChatGPT" : (chatModel.modelType ?? "default"),
    version: pluginVersion,
    beginTimestamp: +new Date(),
    sessionId: sessionId,
    requestId: uniqueId,
  };
  const createReportFunction = (
    stage: string,
    startTime?: number,
    extra?: {
      extra1: string;
      extra2: string;
    },
  ) => {
    let hasReport = false;
    return (exception = "") => {
      if (!hasReport) {
        const now = +new Date();
        hasReport = true;
        const costReportData = {
          ...costReportCommonData,
          beginTimestamp: startTime
            ? startTime
            : costReportCommonData.beginTimestamp,
          stage: stage,
          endTimestamp: now,
          duration: now - costReportCommonData.beginTimestamp,
          exception,
          extra1: extra?.extra1,
          extra2: extra?.extra2,
        };
        logger.info(
          `report cost: ${exception} ${
            now - costReportCommonData.beginTimestamp
          }`,
          "chat",
          {
            value: costReportCommonData,
          },
        );
        reportCost(costReportData).catch((e) => {
          logger.error(
            `report cost error: ${exception} ${
              now - costReportCommonData.beginTimestamp
            }`,
            "chat",
            {
              value: costReportCommonData,
              err: e,
            },
          );
        });
      }
    };
  };

  const reportFirstResponseIfNecessary
    = createReportFunction("e2e-first-response");
  const reportDoneResponseIfNecessary
    = createReportFunction("e2e-done-response");
  const reportErrorResponseIfNecessary = createReportFunction(
    "e2e-exception-response",
  );
  const reportSubmit2Response = createReportFunction(
    "startResponse",
    startTime,
    {
      extra1: "count",
      extra2: "success",
    },
  );
  const reportSubmit2Error = createReportFunction("startResponse", startTime, {
    extra1: "count",
    extra2: "error",
  });
  const reportSubmit2Request = createReportFunction("startRequest", startTime);

  const store = getRecordStoreByVendor(vendor);

  const abortController = new AbortController();
  store
    .getState()
    .setCurrentChatAbortController(abortController);

  store.getState().setLoadingStatu(uniqueId, "loading");
  reportSubmit2Request();
  httpClient.fetchEventSource("/eapi/kwaipilot/chat/completion", {
    method: "POST",
    body: JSON.stringify(data),
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
    signal: abortController.signal,
    // 添加 openWhenHidden 参数，防止页面可见性变化时重新开始请求
    openWhenHidden: true,
    onclose: () => {
      if (abortController?.signal.aborted) {
        logger.info("fetcheEventSource aborted", "chat");
        return;
      }

      if (searchResult && searchResult.length > 0) {
        logger.info("fetcheEventSource searchResult", "chat", {
          value: searchResult,
        });
        store.getState().addReplyChunk(uniqueId, { replyContent: "\n\n参考链接：" });

        searchResult.forEach((result) => {
          store.getState().addReplyChunk(uniqueId, { replyContent: "\n· " + result["link"] });
        });
        reportFirstResponseIfNecessary();
      }

      if (tip && tip != "") {
        logger.info("fetcheEventSource tip", "chat", {
          value: tip,
        });
        store.getState().addReplyChunk(uniqueId, { replyContent: "\n\n" + tip });
        reportFirstResponseIfNecessary();
      }

      store.getState().signalMessageDone(uniqueId);

      reportDoneResponseIfNecessary();
    },
    // FIXME: 类型安全
    onmessage: (event: any) => {
      if (abortController?.signal.aborted) {
        logger.info("fetcheEventSource aborted", "chat");
        return;
      }
      const data = JSON.parse(event.data);
      if (
        data.type === "reply"
        && data["reply"]
        && data["reply"] != ""
        && isValidReply(data["reply"])
      ) {
        store.getState().addReplyChunk(uniqueId, { replyContent: data.reply });
        reportFirstResponseIfNecessary();
        reportSubmit2Response();
      }

      if (
        isJsonString(data["reply"])
        && typeof JSON.parse(data["reply"]) != "number"
      ) {
        const searchData: any = JSON.parse(data["reply"]);
        if (
          searchData
          && searchData["docModels"]
          && Array.isArray(searchData["docModels"])
          && searchData["docModels"].length != 0
        ) {
          const replyData = searchData["docModels"];
          logger.info("Response Data: ", "chat", {
            value: replyData,
          });
          // docList[uniqueId] = replyData;

          const results = replyData.map((result) => {
            const { title, url } = result;
            const link = `[${title}](${url})`;
            return { link };
          });
          searchResult = results;
        }
        if (searchData && searchData["tip"]) {
          tip = searchData["tip"];
        }
      }

      if (data["type"] == "actionStatus") {
        // const actionStatus = data["actionStatus"];
        // console.log("=========", actionStatus, "==========");
      }

      if (data["type"] === "actionResult") {
        eventBus.emit("actionResult", {
          data: JSON.stringify(data["results"]),
          id: uniqueId,
          vendor,
        });
        reportFirstResponseIfNecessary();
      }
    },
    onerror: (e: any) => {
      reportSubmit2Error();
      if (abortController?.signal.aborted) {
        logger.info("fetcheEventSource aborted", "chat");
        return;
      }
      logger.error("fetcheEventSource onerror", "chat", {
        err: e,
      });
      store.getState().finishLoading();
      reportErrorResponseIfNecessary(e && e.stack ? e.stack : String(e));
      throw e;
    },
  });
};
const isValidReply = (reply: any) => {
  if (
    reply
    && reply != ""
    && (!isJsonString(reply)
      || typeof JSON.parse(reply) == "number"
      || typeof JSON.parse(reply) == "boolean"
      || JSON.parse(reply) == null
      || reply == "[]")
  ) {
    return true;
  }
  return false;
};

const isJsonString = (str: any) => {
  try {
    JSON.parse(str);
    return true;
  }
  catch (e) {
    return false;
  }
};

const reportCost = async (data: {
  beginTimestamp: number;
  duration: number;
  endTimestamp: number;
  modelType: string;
  namespace: string;
  platform: string;
  requestId: string;
  sessionId: string;
  stage: string;
  username: string;
  version: string;
  extra1?: string;
  extra2?: string;
  extra3?: string;
  extra4?: string;
  extra5?: string;
  extra6?: string;
}) => {
  const proxyUrl = window.proxyUrl || DefaultBaseUrl;
  const res = await fetch(`${proxyUrl}/eapi/kwaipilot/log/time`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  const result = await res.json();
  console.log("reportCost:", result);
};
