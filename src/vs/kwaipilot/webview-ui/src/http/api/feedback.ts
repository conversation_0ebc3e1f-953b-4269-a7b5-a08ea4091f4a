import { kwaiPilotBridgeAPI } from "@/bridge";
import { DEFAULT_PROXY_URL } from "@/constant";
import { FeedbackLogParam, SseMessageEnum } from "@/http/api/type";
import baseInfoManager from "@/utils/baseInfo";
import { getUserInfo } from "@/utils/getUserInfo";

/** 点赞埋点-V2 */
async function feedbackLog(param: FeedbackLogParam): Promise<any> {
  let promise;
  const feedbackLogUrl = `${
    window.proxyUrl || DEFAULT_PROXY_URL
  }/eapi/kwaipilot/qa/feedback`;
  try {
    promise = fetch(feedbackLogUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(param.detail),
    }).then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    });
  }
  catch (error) {
    console.log(error);
  }
  return promise;
}

export async function buildFeedbackLogParam(param: {
  chatId?: string;
  modelType?: string;
  question?: string;
  reply?: string;
  docList?: any[];
  feedback?: string;
  topK?: number;
  infraPlatform?: string;
}): Promise<FeedbackLogParam> {
  const {
    chatId,
    modelType,
    question,
    reply,
    docList,
    feedback,
    topK,
    infraPlatform,
  } = param;
  const username = (await getUserInfo())?.name ?? "KwaiStaff";
  const feedbackParam = new FeedbackLogParam({
    chatId,
    modelType,
    question,
    reply,
    docList,
    feedback,
    username,
    topK,
    infraPlatform,
    platform: baseInfoManager.ide,
    pluginVersion: baseInfoManager.pluginVersion,
    biz: baseInfoManager.ide,
    deviceId: baseInfoManager.deviceId,
    deviceName: buildDeviceLog().deviceName,
  });
  return feedbackParam;
}
class DeviceParam {
  deviceName?: string;
  deviceModel?: string;
  deviceOsVersion?: string;
}
function buildDeviceLog(): DeviceParam {
  const deviceParam = new DeviceParam();
  deviceParam.deviceName = baseInfoManager.hostname;
  deviceParam.deviceModel = baseInfoManager.platform;
  deviceParam.deviceOsVersion = baseInfoManager.release;
  return deviceParam;
}

/** 上报用户手动复制事件 */
export async function reportUserCopy(text: string) {
  feedbackLog(
    await buildFeedbackLogParam({
      chatId: "",
      modelType: "",
      question: "",
      reply: text,
      feedback: "copyManual",
    }),
  );
}

/** 上报用户手动点赞事件 */
export async function reportUserLike(param: {
  chatId: string;
  reply: string;
  question: string;
  isLike: boolean;
}) {
  const { chatId, reply, question, isLike } = param;
  feedbackLog(
    await buildFeedbackLogParam({
      chatId,
      modelType: SseMessageEnum.AUTO_IDENTIFY,
      question,
      reply,
      feedback: isLike == true ? "like" : "dislike",
      topK: undefined,
      infraPlatform: undefined,
    }),
  );
}

/** 上报用户复制模型返回内容 */
export async function reportCopyMessage(param: {
  isBlock: boolean;
  reply: string;
  chatId: string;
  question: string;
}) {
  const { isBlock, reply, chatId, question } = param;
  kwaiPilotBridgeAPI.showToast({
    level: "info",
    message: "已复制",
  });
  feedbackLog(
    await buildFeedbackLogParam({
      chatId,
      modelType: SseMessageEnum.AUTO_IDENTIFY,
      question,
      reply,
      docList: [],
      feedback: isBlock == true ? "codeBlockCopy" : "copy",
      topK: undefined,
      infraPlatform: undefined,
    }),
  );
}
