<svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_4257_5791)">
<circle cx="46.1084" cy="37.7398" r="7.5015" fill="#1D90F2" fill-opacity="0.8"/>
</g>
<g filter="url(#filter1_d_4257_5791)">
<rect x="12.7515" y="12.6367" width="40.1375" height="42.726" rx="4.93887" fill="url(#paint0_linear_4257_5791)"/>
</g>
<g filter="url(#filter2_f_4257_5791)">
<circle cx="45.6454" cy="32.9731" r="15.9848" fill="url(#paint1_linear_4257_5791)"/>
</g>
<mask id="mask0_4257_5791" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="29" y="16" width="33" height="33">
<circle cx="45.6454" cy="32.9731" r="15.9848" fill="url(#paint2_linear_4257_5791)"/>
</mask>
<g mask="url(#mask0_4257_5791)">
<rect width="18.1423" height="82.1513" transform="matrix(0.879017 0.476791 -0.402989 0.915205 72.4033 -4.12891)" fill="url(#paint3_linear_4257_5791)"/>
<rect width="18.1423" height="65.0233" transform="matrix(0.879017 0.476791 -0.402989 0.915205 41.2705 -5.76172)" fill="url(#paint4_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="18.1423" height="91.8794" transform="matrix(0.879017 0.476791 -0.402989 0.915205 69.3887 -21.6875)" fill="url(#paint5_linear_4257_5791)" fill-opacity="0.2"/>
<rect width="1.93081" height="61.3489" transform="matrix(0.879017 0.476791 -0.402989 0.915205 55.7368 6.25391)" fill="url(#paint6_linear_4257_5791)"/>
<rect width="1.93081" height="66.7288" transform="matrix(0.879017 0.476791 -0.402989 0.915205 59.9336 1.33594)" fill="url(#paint7_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="1.93081" height="75.2544" transform="matrix(0.879017 0.476791 -0.402989 0.915205 65.439 -6.46875)" fill="url(#paint8_linear_4257_5791)" fill-opacity="0.6"/>
<rect width="1.93081" height="64.8341" transform="matrix(0.879017 0.476791 -0.402989 0.915205 63.3105 3.05469)" fill="url(#paint9_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="1.93081" height="91.8794" transform="matrix(0.879017 0.476791 -0.402989 0.915205 76.1606 -21.6875)" fill="url(#paint10_linear_4257_5791)" fill-opacity="0.6"/>
<rect width="1.93081" height="91.8794" transform="matrix(0.879017 0.476791 -0.402989 0.915205 54.4883 -21.6875)" fill="url(#paint11_linear_4257_5791)" fill-opacity="0.3"/>
<rect width="0.0808726" height="57.3194" transform="matrix(0.879017 0.476791 -0.402989 0.915205 39.793 10.8203)" fill="url(#paint12_linear_4257_5791)" fill-opacity="0.3"/>
<rect width="0.0808726" height="57.3194" transform="matrix(0.879017 0.476791 -0.402989 0.915205 62.3965 4.94922)" fill="url(#paint13_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="0.0808726" height="57.3194" transform="matrix(0.879017 0.476791 -0.402989 0.915205 47.9346 4.94922)" fill="url(#paint14_linear_4257_5791)" fill-opacity="0.6"/>
<rect width="0.0808726" height="57.3194" transform="matrix(0.879017 0.476791 -0.402989 0.915205 44.8081 4.94922)" fill="url(#paint15_linear_4257_5791)" fill-opacity="0.75"/>
<rect width="1.93081" height="60.7745" transform="matrix(0.879017 0.476791 -0.402989 0.915205 45.0298 6.77754)" fill="url(#paint16_linear_4257_5791)" fill-opacity="0.5"/>
<rect width="1.93081" height="82.5566" transform="matrix(0.879017 0.476791 -0.402989 0.915205 74.3892 -13.1406)" fill="url(#paint17_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="1.93081" height="91.8794" transform="matrix(0.879017 0.476791 -0.402989 0.915205 80.1318 -21.6875)" fill="url(#paint18_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="1.93081" height="91.8794" transform="matrix(0.879017 0.476791 -0.402989 0.915205 82.1602 -21.6875)" fill="url(#paint19_linear_4257_5791)" fill-opacity="0.8"/>
<rect width="1.93081" height="75.778" transform="matrix(0.879017 0.476791 -0.402989 0.915205 77.3198 -6.95312)" fill="url(#paint20_linear_4257_5791)" fill-opacity="0.75"/>
<rect width="1.93081" height="91.8794" transform="matrix(0.879017 0.476791 -0.402989 0.915205 85.7051 -21.6875)" fill="url(#paint21_linear_4257_5791)" fill-opacity="0.6"/>
<rect width="0.0808726" height="57.3194" transform="matrix(0.879017 0.476791 -0.402989 0.915205 71.6387 5.91426)" fill="url(#paint22_linear_4257_5791)" fill-opacity="0.4"/>
<rect width="0.0808726" height="57.3194" transform="matrix(0.879017 0.476791 -0.402989 0.915205 73.9106 4.94922)" fill="url(#paint23_linear_4257_5791)" fill-opacity="0.9"/>
</g>
<g filter="url(#filter3_b_4257_5791)">
<path d="M12.7515 34.9779C12.7515 32.2503 14.9627 30.0391 17.6903 30.0391L28.2838 30.0391C29.7889 30.0391 31.2119 30.7254 32.1489 31.9033L36.7296 37.6618C37.2918 38.3685 38.1456 38.7804 39.0486 38.7804H47.9501C50.6778 38.7804 52.889 40.9916 52.889 43.7192V50.4244C52.889 53.1521 50.6778 55.3633 47.9501 55.3633H17.6903C14.9627 55.3633 12.7515 53.1521 12.7515 50.4244V34.9779Z" fill="white" fill-opacity="0.5"/>
<path d="M12.9984 34.9779C12.9984 32.3867 15.0991 30.286 17.6903 30.286H28.2838C29.7137 30.286 31.0655 30.938 31.9557 32.0571L36.5363 37.8155C37.1453 38.5812 38.0703 39.0273 39.0486 39.0273H47.9501C50.5414 39.0273 52.6421 41.1279 52.6421 43.7192V50.4244C52.6421 53.0157 50.5414 55.1164 47.9501 55.1164H17.6903C15.0991 55.1164 12.9984 53.0157 12.9984 50.4244V34.9779Z" stroke="#D3DFE8" stroke-width="0.493887"/>
</g>
<defs>
<filter id="filter0_f_4257_5791" x="26.2493" y="17.8807" width="39.7181" height="39.7182" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.1788" result="effect1_foregroundBlur_4257_5791"/>
</filter>
<filter id="filter1_d_4257_5791" x="8.01023" y="10.2661" width="49.6202" height="52.2085" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.37062"/>
<feGaussianBlur stdDeviation="2.37062"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.189047 0 0 0 0 0.386797 0 0 0 0 0.569336 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4257_5791"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4257_5791" result="shape"/>
</filter>
<filter id="filter2_f_4257_5791" x="27.6606" y="14.9883" width="35.9697" height="35.9696" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_4257_5791"/>
</filter>
<filter id="filter3_b_4257_5791" x="8.80037" y="26.088" width="48.0399" height="33.2264" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1.97555"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4257_5791"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_4257_5791" result="shape"/>
</filter>
<linearGradient id="paint0_linear_4257_5791" x1="32.8202" y1="12.6367" x2="23.4761" y2="35.4477" gradientUnits="userSpaceOnUse">
<stop stop-color="#ABDAFF"/>
<stop offset="1" stop-color="#E6F0F7"/>
</linearGradient>
<linearGradient id="paint1_linear_4257_5791" x1="38.5835" y1="22.1472" x2="53.9934" y2="27.7526" gradientUnits="userSpaceOnUse">
<stop stop-color="#147BF3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint2_linear_4257_5791" x1="38.5835" y1="22.1472" x2="53.9934" y2="27.7526" gradientUnits="userSpaceOnUse">
<stop stop-color="#147BF3"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint3_linear_4257_5791" x1="4.11666" y1="20.4971" x2="9.74116" y2="82.0901" gradientUnits="userSpaceOnUse">
<stop stop-color="#0AE4EE" stop-opacity="0"/>
<stop offset="1" stop-color="#0AE4EE"/>
</linearGradient>
<linearGradient id="paint4_linear_4257_5791" x1="8.54227" y1="0.668958" x2="10.1651" y2="57.7424" gradientUnits="userSpaceOnUse">
<stop stop-color="#154BF4" stop-opacity="0"/>
<stop offset="0.349459" stop-color="#154BF4" stop-opacity="0.5"/>
<stop offset="0.637743" stop-color="#154BF4" stop-opacity="0.4"/>
<stop offset="1" stop-color="#0FE0F1"/>
</linearGradient>
<linearGradient id="paint5_linear_4257_5791" x1="-6.23265" y1="12.5584" x2="20.0781" y2="71.0148" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint6_linear_4257_5791" x1="-0.663315" y1="8.38534" x2="14.5902" y2="13.9883" gradientUnits="userSpaceOnUse">
<stop stop-color="#001AFF" stop-opacity="0"/>
<stop offset="0.612337" stop-color="#35ADEE"/>
<stop offset="1" stop-color="#00F0FF"/>
</linearGradient>
<linearGradient id="paint7_linear_4257_5791" x1="-0.663315" y1="9.12068" x2="14.366" y2="14.0138" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint8_linear_4257_5791" x1="1.85945" y1="0.0700083" x2="20.1156" y2="6.38935" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint9_linear_4257_5791" x1="-0.663315" y1="8.86171" x2="14.281" y2="13.8694" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint10_linear_4257_5791" x1="1.26404" y1="0.0826728" x2="20.1353" y2="5.23341" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint11_linear_4257_5791" x1="1.26404" y1="0.0826728" x2="20.1353" y2="5.23341" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint12_linear_4257_5791" x1="0.0529445" y1="0.0515759" x2="0.90197" y2="0.0671343" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint13_linear_4257_5791" x1="0.0529445" y1="0.0515759" x2="0.90197" y2="0.0671343" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.279251" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint14_linear_4257_5791" x1="0.0529445" y1="0.0515759" x2="0.90197" y2="0.0671343" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint15_linear_4257_5791" x1="0.0529445" y1="0.0515759" x2="0.90197" y2="0.0671343" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint16_linear_4257_5791" x1="1.26404" y1="0.0546847" x2="18.5909" y2="7.20436" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3" stop-opacity="0"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint17_linear_4257_5791" x1="-0.663315" y1="11.2841" x2="14.8825" y2="15.3751" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint18_linear_4257_5791" x1="-0.663315" y1="12.5584" x2="15.0789" y2="16.2807" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint19_linear_4257_5791" x1="-0.663315" y1="12.5584" x2="15.0789" y2="16.2807" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint20_linear_4257_5791" x1="-0.663315" y1="10.3576" x2="14.6966" y2="14.7612" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint21_linear_4257_5791" x1="-0.663315" y1="12.5584" x2="15.0789" y2="16.2807" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F77F3"/>
<stop offset="0.457692" stop-color="#0E9AE7"/>
<stop offset="1" stop-color="#11E6F4"/>
</linearGradient>
<linearGradient id="paint22_linear_4257_5791" x1="0.0529445" y1="0.0515759" x2="0.90197" y2="0.0671343" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint23_linear_4257_5791" x1="0.0529445" y1="0.0515759" x2="0.90197" y2="0.0671343" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.457692" stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
