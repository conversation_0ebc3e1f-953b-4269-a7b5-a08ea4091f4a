import {
  ExtractNativeBridgePayload,
  ExtractNativeBridgeResult,
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
  WebviewBridgeParams,
  WebviewBridgeResult,
} from "@shared/types/bridge";
import { Bridge, BridgeMessage, BridgeMessageStateless } from "../bridge-share/types";
import { IKwaiPilotBridge } from "../bridge-share/IKwaiPilotBridge";
import { logger } from "@/utils/logger";

export class KwaiPilotBridge implements IKwaiPilotBridge {
  private bridge?: Bridge;
  private bridgePromise: Promise<Bridge> | undefined;
  private loggerScope = "KwaiPilotBridge";

  constructor() {
    this.bridgePromise = new Promise((resolve) => {
      this.setupWKWebViewJavascriptBridge((bridge) => {
        this.bridge = bridge;
        this.bridgePromise = undefined;
        resolve(bridge);
      });
    });
  }

  public setBridge(bridgeImplementation: Bridge): void {
    // 保持接口一致性， 这里不做任何事情
    console.log("setBridge", bridgeImplementation);
  }

  private setupWKWebViewJavascriptBridge(callback: (bridge: Bridge) => void) {
    if (window.bridge) {
      return callback(window.bridge);
    }
    if (window.WKWebViewJavascriptBridge) {
      return callback(window.WKWebViewJavascriptBridge);
    }
    if (window.WKWVJBCallbacks) {
      return window.WKWVJBCallbacks.push(callback);
    }
    window.WKWVJBCallbacks = [callback];
    window.webkit.messageHandlers.kwaipilotBridge.postMessage(null);
  }

  /**
   * 调用原生端
   * @param eventName
   * @param message
   * @param callback
   */
  public async postMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    eventName: T,
    message: BridgeMessage<ExtractNativeBridgePayload<T>>,
    callback?: (params: ExtractNativeBridgeResult<T>) => void,
  ) {
    if (this.bridgePromise) {
      await this.bridgePromise;
    }
    const data = JSON.stringify(message);

    if (callback) {
      this.bridge?.callHandler(eventName, data, (d: string) => {
        callback(this.handleResponse<ExtractNativeBridgeResult<T>>(d));
      });
    }
    else {
      this.bridge?.callHandler(eventName, data);
    }
  }

  /**
   * 注册native可以调用的handler 注意只能调用一次 多次调用会覆盖
   * @param eventName
   * @param handler
   */
  public async onMessage<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    eventName: T,
    handler: (params: WebviewBridgeResult[T]) => void,
  ) {
    if (this.bridgePromise) {
      await this.bridgePromise;
    }
    this.bridge?.registerHandler(eventName, (data: string) =>
      this.formatOnMessageHandler<T>(eventName, data, handler),
    );
  }

  /**
   * 发送单向无状态消息， 旧版 bridge API 强行要求有 req - res 的结构， 如果要发送单向消息， 使用这个结构
   * @param eventName
   * @param message
   */
  public postOneWayMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    eventName: T,
    message: ExtractNativeBridgePayload<T>,
  ) {
    this.bridge?.postMessage({
      event: eventName,
      payload: message,
    } satisfies BridgeMessageStateless<ExtractNativeBridgePayload<T>>);
  }

  /**
   * 添加消息监听器, 无状态
   * @param event
   * @param listener
   */
  public addMessageListener<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    event: T,
    listener: (payload: WebviewBridgeParams[T]) => unknown,
  ): () => void {
    function isStatelessMessage(message: unknown): message is BridgeMessageStateless {
      return typeof message === "object" && message !== null && "protocol" in message && message.protocol === "message";
    }
    const _listener = (message: unknown) => {
      // 对 event protocol 过滤
      if (isStatelessMessage(message) && message.event === event) {
        listener(message.payload);
      }
    };
    this.bridge?.addMessageListener(_listener);
    return () => {
      this.bridge?.removeMessageListener(_listener);
    };
  }

  private formatOnMessageHandler<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    eventName: T,
    data: string,
    handler: (params: WebviewBridgeResult[T]) => void,
  ) {
    try {
      const d = JSON.parse(data) as BridgeMessage;
      if (d.code !== 0) {
        logger.error(
          `call native handler: ${eventName} error`,
          this.loggerScope,
          {
            err: d.msg,
          },
        );
      }
      return handler(d.payload as WebviewBridgeResult[T]);
    }
    catch (error) {
      logger.error(
        `call native handler: ${eventName} error`,
        this.loggerScope,
        {
          err: error,
        },
      );
      throw error;
    }
  }

  private handleResponse<T>(res: string): T {
    try {
      const formatedRes = JSON.parse(res) as BridgeMessage;
      if (formatedRes.code !== 0) {
        logger.error(
          `response with error code: ${formatedRes.code}`,
          this.loggerScope,
          {
            value: formatedRes,
          },
        );
      }
      return formatedRes.payload as T;
    }
    catch (err) {
      logger.error("handleResponse error", this.loggerScope, { err });
      throw err;
    }
  }

  // 生成唯一ID
  generateId(): string {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }
}
