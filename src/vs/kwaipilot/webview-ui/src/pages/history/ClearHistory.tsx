import TrashIcon from "@/assets/icons/trash.svg?react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useHistoryStore } from "@/store/history";
import { getRecordStoreByVendor } from "@/store/record";

const ClearHistory = () => {
  const setHistoryList = useHistoryStore(state => state.setHistoryList);
  const chatVendorRecordStore = getRecordStoreByVendor("chat");
  const composerVendorRecordStore = getRecordStoreByVendor("composer");

  const clearHistory = () => {
    setHistoryList([]);
    chatVendorRecordStore.getState().setActiveSession({ value: "" });
    composerVendorRecordStore.getState().setActiveSession({ value: "" });
    kwaiPilotBridgeAPI.clearSession();
  };
  return (
    <div
      onClick={clearHistory}
      className="overflow-hidden flex gap-1 items-center px-[6px] py-[3px] cursor-pointer rounded text-icon-common-secondary hover:bg-toolbar-hoverBackground"
    >
      <TrashIcon />
      <div
        className="text-[13px] leading-[20px] whitespace-nowrap truncate flex items-center"
      >
        清空历史会话
      </div>
    </div>
  );
};

export default ClearHistory;
