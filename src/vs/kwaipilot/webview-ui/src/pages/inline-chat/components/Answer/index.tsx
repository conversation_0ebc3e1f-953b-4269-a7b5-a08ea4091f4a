import { StreamLoading } from "@/components/Streamerloading";
import { Button, useColorMode } from "@chakra-ui/react";

import LogoDark from "@/assets/images/logo-dark.svg?react";
import LogoLight from "@/assets/images/logo-light.svg?react";
// import CopyIcon from "@/assets/images/copy-bold.svg?react";
// import LikeIcon from "@/assets/images/like.svg?react";
// import LikeFillIcon from "@/assets/images/like-fill.svg?react";
// import UnLikeFillIcon from "@/assets/images/unlike-fill.svg?react";
// import UnLikeIcon from "@/assets/images/unlike.svg?react";
import PauseIcon from "@/assets/images/pause-circle-bold.svg?react";
import SpinnerIcon from "@/assets/images/spinner-gap-bold.svg?react";
// import MagicIcon from "@/assets/images/magic-wand-bold.svg?react";

// import { MessageType } from "@/utils/const";
import { InlineChatItem, useInlineChatStore } from "@/store/inline-chat";
import { customIconButton } from "@/utils/const";
import clsx from "clsx";
// import { postInlineChatMessage } from "@/utils/sessionUtils";

interface Props {
  item: InlineChatItem;
  onStop: () => void;
  showGotoChat?: boolean;
}

export const Answer = (props: Props) => {
  const { item, showGotoChat, onStop } = props;
  const { answer: content, loading: isLoading } = item;
  const { colorMode: theme } = useColorMode();
  // const handleAccept = () => {
  //   postInlineChatMessage({
  //     type: MessageType.INLINE_CHAT_ACCEPT,
  //   });
  // };
  // const handleReject = () => {
  //   postInlineChatMessage({
  //     type: MessageType.INLINE_CHAT_REJECT,
  //   });
  // };
  const clearInlineChatHistory = useInlineChatStore(
    state => state.clearInlineChatHistory,
  );
  return (
    <StreamLoading
      isLoading={isLoading}
      className="mx-[12px]"
      id={Math.random().toString()}
    >
      <div className={`dialog-item-bg-${theme} rounded-[10px]`}>
        <div
          className={`flex justify-between items-center h-[40px] dialog-border-${theme} px-[12px]`}
        >
          <div className="flex items-center">
            {theme === "dark"
              ? (
                  <LogoDark className="rounded-full w-[20px] h-[20px]" />
                )
              : (
                  <LogoLight className="rounded-full w-[20px] h-[20px]" />
                )}
            <span className={`ml-[8px] dialog-text-${theme} font-[500]`}>
              Kwaipilot
            </span>
          </div>
        </div>
        <div
          className={`dialog-text-${theme} text-[13px] leading-[18px] p-[12px] min-h-[42px]`}
        >
          {isLoading
            ? (
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Button
                      {...customIconButton}
                      fontSize="12px"
                      leftIcon={(
                        <SpinnerIcon
                          className={clsx(`animate-spin icon-fill-${theme}`)}
                          style={theme === "light" ? { fill: "#656D76" } : {}}
                        />
                      )}
                      style={theme === "light" ? { color: "#262A2F" } : {}}
                    >
                      {isLoading ? "生成中" : "重新生成"}
                    </Button>
                  </div>
                  <div className="flex items-center">
                    <Button
                      fontSize="12px"
                      leftIcon={(
                        <PauseIcon
                          className={clsx(`icon-fill-${theme}`)}
                          style={theme === "light" ? { fill: "#656D76" } : {}}
                        />
                      )}
                      {...customIconButton}
                      style={theme === "light" ? { color: "#262A2F" } : {}}
                      onClick={onStop}
                    >
                      停止
                    </Button>
                  </div>
                </div>
              )
            : showGotoChat
              ? (
                  <>
                    是否返回普通模式查看,
                    {" "}
                    <button onClick={clearInlineChatHistory}>确定</button>
                  </>
                )
              : (
                  content
                )}
        </div>
      </div>
    </StreamLoading>
  );
};
