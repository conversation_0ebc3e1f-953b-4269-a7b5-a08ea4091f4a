import { useCallback, useEffect, useState } from "react";
import { Answer } from "@/pages/inline-chat/components/Answer";
import { Question } from "@/pages/inline-chat/components/Question";
import { useInlineChatStore } from "@/store/inline-chat";

import { Chat } from "@/pages/inline-chat/components/Chat";
import { useColorMode } from "@chakra-ui/react";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { InlineChatInfo } from "@shared/types";
import { useRecordStore } from "@/store/record";
import { abortInlineChatChat } from "@/http/api/inline-chat";
import eventBus from "@/utils/eventBus";

export const Main = () => {
  const inlineChatInfo = useInlineChatStore(
    state => state.inlineChatHistory?.quote,
  ) as InlineChatInfo;
  const { colorMode: theme } = useColorMode();
  const historyList = useInlineChatStore(
    state => state.inlineChatHistory?.list,
  );
  const updateAnswer = useInlineChatStore(state => state.updateAnswer);
  const updateLoading = useInlineChatStore(state => state.updateLoading);
  const clearInlineChatHistory = useInlineChatStore(
    state => state.clearInlineChatHistory,
  );
  const activeSession = useRecordStore(state => state.activeSession);
  const setActiveSession = useRecordStore(state => state.setActiveSession);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showGotoChat, setShowGotoChat] = useState<boolean>(false);

  const setToLoading = () => {
    updateLoading(true);
    setIsLoading(true);
  };
  const onStop = () => {
    const param: ReportOpt<"inline_chat"> = {
      key: "inline_chat",
      type: "pause",
    };
    reportUserAction(param);
    // 置为 loading false
    updateLoading(false);
    setIsLoading(false);
    updateAnswer("已暂停生成代码");
    abortInlineChatChat();
    collectClick("VS_STOP_BUTTON");
  };
  const gotoChat = useCallback(() => {
    if (!historyList) {
      return;
    }
    else if (historyList.length === 1) {
      clearInlineChatHistory();
    }
    else {
      setShowGotoChat(true);
    }
    // 通过setAction Seesion 来跳转路由
    setActiveSession({ value: activeSession });
  }, [activeSession, clearInlineChatHistory, historyList, setActiveSession]);
  useEffect(() => {
    const handleMessageDone = (data?: string) => {
      data && updateAnswer(data);
      setIsLoading(false);
      updateLoading(false);
    };
    const handleMessage = (data: string) => {
      updateAnswer(data);
    };
    eventBus.on("inlineChat:message", handleMessage);
    eventBus.on("inlineChat:messageDone", handleMessageDone);
    return () => {
      eventBus.off("inlineChat:message", handleMessage);
      eventBus.off("inlineChat:messageDone", handleMessageDone);
    };
  }, [gotoChat, updateAnswer, updateLoading]);

  return (
    <div
      key={inlineChatInfo?.filepath}
      className="flex flex-col gap-2 w-full bg-transparent"
    >
      <Question
        filename={inlineChatInfo?.filepath?.split("/").pop() || ""}
        startLine={inlineChatInfo?.startLine}
        endLine={inlineChatInfo?.endLine}
        content={inlineChatInfo?.content}
        setToLoading={setToLoading}
      />
      {historyList?.map((item, index) => {
        const key = index;
        const isLast = index === historyList.length - 1;
        if (index === 0) {
          return <Answer item={item} key={key} onStop={onStop} />;
        }
        else {
          return (
            <>
              <div
                className={`rounded-lg mx-[12px] inline-dialog-item-bg-base-${theme}`}
              >
                <Chat
                  setToLoading={setToLoading}
                  item={item}
                  isAbsolute={true}
                />
              </div>
              {isLast
                ? (
                    <Answer
                      onStop={onStop}
                      item={item}
                      key={key}
                      showGotoChat={showGotoChat}
                    />
                  )
                : (
                    <Answer onStop={onStop} item={item} key={key} />
                  )}
            </>
          );
        }
      })}
      {historyList && historyList.length && !isLoading
        ? (
            <div
              className={`rounded-lg mx-[12px] inline-dialog-item-bg-base-${theme}`}
            >
              <Chat setToLoading={setToLoading} isAbsolute={true} />
            </div>
          )
        : null}
    </div>
  );
};
