import { InlineChatItem, useInlineChatStore } from "@/store/inline-chat";
import { chatId } from "@/utils/chatId";
import { ButtonStyleMap } from "@/utils/const";
import eventBus from "@/utils/eventBus";
import { getActiveSessionId } from "@/utils/getActiveSessionId";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { Button, useColorMode } from "@chakra-ui/react";
import { useCallback, useState } from "react";
import TextareaAutosize from "react-textarea-autosize";

interface Props {
  setToLoading: () => void;
  // 独立使用时需要添加背景色
  isAbsolute?: boolean;
  item?: InlineChatItem;
}
export const Chat = ({ setToLoading, isAbsolute, item }: Props) => {
  const [canSubmit, setCanSubmit] = useState<boolean>(true); // 是否可以提交，默认[true]

  const [q, setQ] = useState<string>("");
  const disabled = !!(q.trim().length === 0);
  const [isComposing, setIsComposing] = useState<boolean>(false);
  const { colorMode: theme } = useColorMode();
  const addQ = useInlineChatStore(state => state.addQuestion);
  const { question: value } = item ?? {};

  const onSubmit = useCallback(() => {
    collectClick("VS_SUBMIT_BUTTON");
    const parms: ReportOpt<"chat"> = {
      key: "chat",
      type: "inline-chat",
    };
    reportUserAction(parms);

    // 隐藏发送按钮, 将问题添加到列表中

    setCanSubmit(false);
    addQ(q);
    // 回答框开始loading
    setToLoading();

    // 告诉vscode 端，不在监听 鼠标选择事件来更新 引用

    const message = q.trim();
    if (!message) return;
    // 重新生成之前先 reject一下，确保没有diff内容
    const uniqueId
      = Date.now().toString(36) + Math.random().toString(36).substr(2);
    chatId.updateChatId(uniqueId);

    eventBus.emit("startInlineChat", {
      chatId: uniqueId,
      question: q,
      sessionId: getActiveSessionId(),
    });
  }, [q, setToLoading, addQ]);
  return (
    <div
      className={`rounded-lg min-h-[28px] p-[12px] ${
        isAbsolute ? `dialog-item-bg-${theme}` : ""
      }`}
    >
      <TextareaAutosize
        placeholder={
          isAbsolute
            ? "可继续输入指令，修改已生成代码"
            : "输入需求让 Kwaipilot 生成代码... "
        }
        className="w-full bg-transparent"
        style={{
          resize: "none",
          outline: "none",
          color: theme === "dark" ? "#F0F0F0" : "#212429",
        }}
        rows={1}
        value={value ?? q}
        disabled={!canSubmit || !!value}
        onChange={e => setQ(e.target.value)}
        onCompositionStart={() => setIsComposing(true)}
        onCompositionEnd={() => setIsComposing(false)}
        onKeyDown={(e) => {
          if (isComposing) return;
          if (q.trim() === "") return;
          if (e.shiftKey && e.key === "Enter") {
            // 执行换行
          }
          else if (e.key === "Enter") {
            // 阻止默认的Enter键行为
            e.preventDefault();
            // 提交
            onSubmit();
          }
        }}
        autoFocus={true}
      />

      <div
        className={`flex justify-end ${canSubmit && !value ? "" : "hidden"}`}
      >
        <Button
          size="xs"
          width="44px"
          height="22px"
          borderRadius="2px"
          color={
            disabled
              ? ButtonStyleMap[theme].disabled.color
              : ButtonStyleMap[theme].default.color
          }
          bg={
            disabled
              ? ButtonStyleMap[theme].disabled.bg
              : ButtonStyleMap[theme].default.bg
          }
          _hover={
            disabled
              ? ButtonStyleMap[theme].disabled
              : ButtonStyleMap[theme].default
          }
          className=""
          isDisabled={disabled}
          onClick={onSubmit}
        >
          发送
        </Button>
      </div>
    </div>
  );
};
