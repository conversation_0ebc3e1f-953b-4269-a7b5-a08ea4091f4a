import { useColorMode } from "@chakra-ui/react";
import CodeShow from "@/pages/inline-chat/components/CodeShow";
import { Chat } from "@/pages/inline-chat/components/Chat";
import { useInlineChatStore } from "@/store/inline-chat";
import { ext2IconName } from "@/pages/inline-chat/const";

interface Props {
  filename: string;
  startLine: number;
  endLine: number;
  content?: string;
  setToLoading: () => void;
}

export const Question = (props: Props) => {
  const { filename, startLine, endLine, content, setToLoading } = props;
  const { colorMode: theme } = useColorMode();
  const ext = filename.split(".").pop() ?? "ts";
  const src = `https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-file-ext-icon/${
    ext2IconName[ext] ?? "typescript"
  }.svg`;
  const firstQuesationValue = useInlineChatStore(
    state => state.inlineChatHistory?.list[0]?.question,
  );

  return (
    <div className="rounded-lg mx-[12px] bg-list-inactiveSelectionBackground">
      <div className="w-full rounded-lgbg-bg-system-default">
        <div className="w-full flex flex-col rounded-[10px] px-[12px] pt-[12px]">
          <div
            className="px-[12px] py-[7px] flex flex-row justify-between rounded-t-[8px] bg-editorHoverWidget-statusBarBackground"
          >
            <div className="flex items-center gap-[2px]">
              <img className="w-[20px] h-[20px]" src={src} />
              <div
                className={`leading-[18px] ${
                  theme === "dark" ? "text-[#fff]" : "text-[#212429]"
                }`}
              >
                {filename}
              </div>
            </div>
            <div
              className={`leading-[18px] ${
                theme === "dark" ? "text-[#fff]" : "text-[#212429]"
              }`}
            >
              Lines
              {" "}
              {startLine === endLine ? startLine : `${startLine}-${endLine}`}
            </div>
          </div>
          {content && (
            <CodeShow
              language={ext}
              content={content}
              lineNumber={endLine - startLine + 1}
              borderRadius="0 0 8px 8px"
              className="bg-editor-background"
            >
            </CodeShow>
          )}
        </div>

        <Chat
          setToLoading={setToLoading}
          item={{
            question: firstQuesationValue,
            loading: false,
          }}
          key={Math.random()}
        />
      </div>
    </div>
  );
};
