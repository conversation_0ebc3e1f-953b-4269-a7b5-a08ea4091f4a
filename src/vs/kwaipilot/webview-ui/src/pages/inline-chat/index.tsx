import { Header } from "./components/Header";

import { Main } from "./components/Main";
import { RecordProvider } from "@/store/record";
import { withProps, withProviders } from "@udecode/cn";

export const InlineChat = withProviders(
  withProps(RecordProvider, { vendor: "chat" }),
)(() => {
  return (
    <div className="w-full h-screen bg-main-bg">
      <Header />
      <Main />
    </div>
  );
});
