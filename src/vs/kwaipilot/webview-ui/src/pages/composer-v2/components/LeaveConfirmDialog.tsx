import { AlertDialog } from "@/components/Union/chakra-ui";
import {
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogCloseButton,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Flex,
  AlertDialogProps,
} from "@chakra-ui/react";

import { useRef } from "react";
import IconAlert from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_warn_surface";
import KidIcon from "@/components/Union/kid";

import { DialogButton } from "@/logics/composer/components/DialogButton";

export interface LeaveConfirmDialog_DirtyWorkingSetProps
  extends Omit<AlertDialogProps, "children" | "leastDestructiveRef"> {
  onCancel?: () => void;
  onContinue?: (action: "accept" | "reject") => void;
  indeterminatedWorkingSetFileNum: number;
}

export function LeaveConfirmDialog_DirtyWorkingSet(
  props: LeaveConfirmDialog_DirtyWorkingSetProps,
) {
  const {
    onCancel,
    onContinue,
    indeterminatedWorkingSetFileNum,
    ...restProps
  } = props;
  const cancelRef = useRef<HTMLButtonElement>(null);
  return (
    <AlertDialog {...restProps} leastDestructiveRef={cancelRef}>
      <AlertDialogOverlay>
        <AlertDialogContent className="!bg-editor-background">
          <AlertDialogHeader px={4} py={3} fontSize="13px" fontWeight="bold">
            <Flex alignItems="center" gap={1}>
              <KidIcon size={16} color="var(--vscode-editorWarning-background)" config={IconAlert} />
              存在未处理代码变更
            </Flex>
            <AlertDialogCloseButton />
          </AlertDialogHeader>

          <AlertDialogBody px={4} pt={0} pb={3}>
            {indeterminatedWorkingSetFileNum}
            {" "}
            个文件存在未处理的代码变更，请返回决策后再新建对话
          </AlertDialogBody>

          <AlertDialogFooter px={4} gap={2} pt={0}>
            <DialogButton ref={cancelRef} onClick={() => onCancel?.()}>
              取消
            </DialogButton>
            <DialogButton isPrimary onClick={() => onContinue?.("reject")}>
              全部拒绝
            </DialogButton>
            <DialogButton isPrimary onClick={() => onContinue?.("accept")}>
              全部接受
            </DialogButton>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
}

export interface LeaveConfirmDialog_StreamingProps
  extends Omit<AlertDialogProps, "children" | "leastDestructiveRef"> {
  onCancel?: () => void;
  onContinue?: () => void;
}

export function LeaveConfirmDialog_Streaming(
  props: LeaveConfirmDialog_StreamingProps,
) {
  const { onCancel, onContinue, ...restProps } = props;
  const cancelRef = useRef<HTMLButtonElement>(null);
  return (
    <AlertDialog
      {...restProps}
      leastDestructiveRef={cancelRef}
    >
      <AlertDialogOverlay>
        <AlertDialogContent className="!bg-editor-background">
          <AlertDialogHeader px={4} py={3} fontSize="13px" fontWeight="bold">
            <Flex alignItems="center" gap={1}>
              <KidIcon size={16} color="#FFBB26" config={IconAlert} />
              回答生成中
            </Flex>
            <AlertDialogCloseButton />
          </AlertDialogHeader>

          <AlertDialogBody px={4} pt={0} pb={3}>
            此时切换Tab会导致当前回答的生成被中断，是否仍要继续切换。
          </AlertDialogBody>

          <AlertDialogFooter px={4} gap={2} pt={0}>
            <DialogButton ref={cancelRef} onClick={() => onCancel?.()}>
              取消
            </DialogButton>
            <DialogButton isPrimary onClick={() => onContinue?.()}>
              继续
            </DialogButton>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
}
