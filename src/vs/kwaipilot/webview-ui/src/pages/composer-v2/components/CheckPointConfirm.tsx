import WarningIcon from "@/assets/warning.svg?react";
import CloseIcon from "@/assets/icons/close.svg?react";
import clsx from "clsx";

interface IProps {
  onOk: () => void;
  onCancel: () => void;
  open: boolean;
}

export const CheckPointConfirm: React.FC<IProps> = (props) => {
  const { onOk, onCancel, open } = props;

  if (!open) {
    return null;
  }

  return (
    <div className={clsx("px-4 py-3 border-[0.6px] border-border-common rounded-lg", "kwaipilot-checkpoint-confirm-block")}>
      <div className="flex items-center justify-between pb-3">
        <div className=" flex gap-[5px]">
          <WarningIcon></WarningIcon>
          <div className="text-text-common-primary leading-[18px] font-medium text-[13px]">确定要回退至此次问答重新发起吗？</div>
        </div>
        <div onClick={onCancel} className="cursor-pointer">
          <CloseIcon></CloseIcon>
        </div>
      </div>
      <div className="pl-5 pb-3 text-text-common-secondary leading-[20px] text-[13px]">此行为除了回退前序回答，还将恢复 AI 操作过的文件</div>
      <div className=" flex justify-end">
        <div className="flex gap-2">
          <div className="rounded bg-bg-scrollbar-default py-[2px] px-[6px] text-text-common-secondary leading-[18px] font-medium text-[12px] cursor-pointer" onClick={onCancel}>取消</div>
          <div className="rounded bg-text-common-primary py-[2px] px-[6px]  leading-[18px] font-medium text-[12px] text-[#353C47] cursor-pointer" onClick={onOk}>继续</div>
        </div>
      </div>
    </div>
  );
};
