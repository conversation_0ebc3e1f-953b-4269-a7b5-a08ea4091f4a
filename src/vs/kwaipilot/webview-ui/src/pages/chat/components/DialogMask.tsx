import { memo, useEffect } from "react";
import { useInView } from "react-intersection-observer";

// TODO: 优化组件使用方式
/**
 * 设置在滚动位置不在最底部时显示半透明遮罩
 */
const DialogMask = memo(
  ({ setShowMask }: { setShowMask: (show: boolean) => void }) => {
    const [ref, inView] = useInView();

    useEffect(() => {
      setShowMask(!inView);
    }, [inView, setShowMask]);

    return <div className="w-full h-[0.5px]" ref={ref}></div>;
  },
);

DialogMask.displayName = "DialogMask";

export default DialogMask;
