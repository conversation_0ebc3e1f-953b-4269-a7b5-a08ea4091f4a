import { useColorMode } from "@chakra-ui/react";
import { Carousel } from "antd";
import { useEffect, useRef, useState } from "react";

import LeftArrowIcon from "@/assets/left-arrow.svg?react";
import { SingleIcon } from "@/components/SingleIcon";

const cards = [
  {
    id: 1,
    icons: {
      light: (
        <div className="size-[22px] bg-center bg-no-repeat bg-cover bg-[url('https://ali.a.yximgs.com/kos/nlav12119/dZGNiLey_2024-08-27-21-46-33.png')]"></div>
      ),
      dark: (
        <div className="size-[22px] bg-center bg-no-repeat bg-cover bg-[url('https://ali.a.yximgs.com/kos/nlav12119/SgQcclaB_2024-08-27-21-47-12.png')]"></div>
      ),
    },
    title: <div className="truncate">代码续写 · 码随心动</div>,
    desc: "行级/函数级实时续写，只需单击 Tab 键即可采纳",
    content: {
      light: {
        backgroundImage:
          "url('https://ali.a.yximgs.com/kos/nlav12119/aQAQTLrm_2024-08-29-17-04-50.png')",
      },
      dark: {
        backgroundImage:
          "url('https://ali.a.yximgs.com/kos/nlav12119/naxazhQz_2024-08-29-17-05-12.png')",
      },
    },
  },
  {
    id: 2,
    icons: {
      light: (
        <div className="w-[48px] h-[22px] bg-center bg-no-repeat bg-cover bg-[url('https://ali.a.yximgs.com/kos/nlav12119/sJyasYwb_2024-08-27-21-50-17.png')]"></div>
      ),
      dark: (
        <div className="w-[48px] h-[22px] bg-center bg-no-repeat bg-cover bg-[url('https://ali.a.yximgs.com/kos/nlav12119/SeRCtnuw_2024-08-27-21-49-46.png')]"></div>
      ),
    },
    title: <div className="truncate">自然语言生成代码 · 心流不止</div>,
    desc: "通过自然语言描述问题或需求，在编辑器内直接生成代码及注释",
    content: {
      light: {
        backgroundImage:
          "url('https://ali.a.yximgs.com/kos/nlav12119/XNBhUvkJ_2024-08-29-17-05-34.png')",
      },
      dark: {
        backgroundImage:
          "url('https://ali.a.yximgs.com/kos/nlav12119/bIFLZlyR_2024-08-29-17-05-57.png')",
      },
    },
  },
  {
    id: 3,
    icons: {
      light: (
        <div className="size-[22px] bg-center bg-no-repeat bg-cover bg-[url('https://ali.a.yximgs.com/kos/nlav12119/YRZgnfXp_2024-08-27-21-50-50.png')]"></div>
      ),
      dark: (
        <div className="size-[22px] bg-center bg-no-repeat bg-cover bg-[url('https://ali.a.yximgs.com/kos/nlav12119/AjDuNyPN_2024-08-27-21-51-06.png')]"></div>
      ),
    },
    title: <div className="truncate">仓库级代码知识问答 · 尽在掌握</div>,
    desc: "基于域内代码及关联数据知识增强，自然语言对话轻松解决研发难题",
    content: {
      light: {
        backgroundImage:
          "url('https://ali.a.yximgs.com/kos/nlav12119/TsAchFnP_2024-08-27-21-31-47.png')",
      },
      dark: {
        backgroundImage:
          "url('https://ali.a.yximgs.com/kos/nlav12119/BUjqwmed_2024-08-27-21-34-04.png')",
      },
    },
  },
];

const styleMap = {
  light:
    "h-[180px] text-[#20242A] rounded-[8px] border border-[rgba(255,255,255,0.60)] bg-[rgba(255,255,255,0.6)] shadow-[0px_2px_8px_0px_rgba(62,119,226,0.08)] backdrop-blur-[8px]",
  dark: "h-[180px] text-[#E5ECF2] rounded-[8px] border border-[rgba(40,50,65,0.32)] bg-[rgba(249,252,255,0.05)] shadow-[0px_2px_8px_0px_rgba(3,10,23,0.08)]",
};

export default function Cards() {
  const { colorMode: theme } = useColorMode();
  const [renderCards, setRenderCards] = useState<any[]>([]);
  const [style, setStyle] = useState<any>({});
  const carouselRef = useRef<any>(null);
  useEffect(() => {
    setStyle(styleMap[theme]);
    setRenderCards(
      cards.map(e => ({
        ...e,
        icons: e.icons[theme],
        content: e.content[theme],
      })),
    );
  }, [theme]);

  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = renderCards.length;
  const onPageChange = (page: number) => {
    setCurrentPage((page + totalPages) % totalPages);
    carouselRef.current?.goTo((page + totalPages) % totalPages);
  };

  return (
    <div>
      <Carousel
        dots={false}
        autoplay
        ref={carouselRef}
        afterChange={e => setCurrentPage(e)}
      >
        {renderCards.map(card => (
          <div key={card.id} className={style}>
            <div className="flex flex-col items-center justify-center h-full w-full overflow-hidden">
              <div className="w-full text-[13px] flex justify-center gap-[4px] mt-[12px] truncate">
                {card.icons}
                {card.title}
              </div>
              <div className="text-[12px] mt-[4px] mb-[16px] px-[8px] text-text-common-secondary">
                {card.desc}
              </div>
              <div
                className="h-[110px] w-[320px] max-w-full bg-center bg-no-repeat bg-cover"
                style={card.content}
              >
              </div>
            </div>
          </div>
        ))}
      </Carousel>
      <div className="flex justify-end mt-[4px]">
        <div className="flex w-[86px] text-[13px] justify-between items-center">
          <SingleIcon onClick={() => onPageChange(currentPage - 1)}>
            <LeftArrowIcon />
          </SingleIcon>
          <span>
            {currentPage + 1}
            {" "}
            /
            {totalPages}
          </span>
          <SingleIcon onClick={() => onPageChange(currentPage + 1)}>
            <LeftArrowIcon className="rotate-180" />
          </SingleIcon>
        </div>
      </div>
    </div>
  );
}
