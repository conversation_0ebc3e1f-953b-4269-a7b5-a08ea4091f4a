import { getCurrentEnvIsInIDE } from "./ide";

const rootContainerId: string = "root"; // 默认 root
const bodyContainerId: string = "body"; // 默认 body

const isInIDE = getCurrentEnvIsInIDE();
export function getRootContainerId() {
  return rootContainerId;
}

/**
 * 获取 root 容器
 * @IMP: 作为 vscode 插件时，会作为 webview 进行渲染，是在一个完整的 html环境中
 * 作为 ide 内置组件渲染时，会挂载到一个 模拟的 body 容器的 div下
 * 所以需要兼容两种情况
 * @returns
 */
export function getRootContainer() {
  return (
    document.querySelector<HTMLDivElement>(
      `[data-element-type="${rootContainerId}"]`,
    ) || (document.getElementById(rootContainerId) as HTMLElement)
  );
}

/**
 * 获取 body 容器
 * @IMP: 作为 vscode 插件时，会作为 webview 进行渲染，是在一个完整的 html环境中
 * 作为 ide 内置组件渲染时，会挂载到一个 模拟的 body 容器的 div下
 * 所以需要兼容两种情况
 * @returns
 */
export function getBodyContainer() {
  return (
    document.querySelector<HTMLDivElement>(
      `[data-element-type="${bodyContainerId}"]`,
    ) || document.body
  );
}

// DOM 层级

// --- 作为 vscode 插件时，会作为 webview 进行渲染，是在一个完整的 html环境中 ----
// <body>
//   <div id="root"/>
// </body>

// --- 作为 ide 内置组件渲染时，会挂载到一个 模拟的 body 容器的 div下 ----

// <div data-element-type="body">
//   <div id="root"/>
// </div>

export const DOM = {
  getRootContainer,
  getBodyContainer,
  $(selector: string) {
    return getRootContainer().querySelector(selector);
  },
  $$(selector: string) {
    return getRootContainer().querySelectorAll(selector);
  },
  createElement(tagName: string) {
    return document.createElement(tagName);
  },
  getActiveElement() {
    // @TODO: ide 需要特殊处理下
    if (isInIDE) {
      // todo
    }
    return document.activeElement;
  },
  getVisibilityState() {
    // @TODO: ide 需要特殊处理下 需要监听的是 panel 的显示与隐藏 而不是 document 的显示与隐藏
    if (isInIDE) {
      // todo
    }
    return document.visibilityState;
  },
  registerVisibleChange(
    callback: Parameters<typeof document.addEventListener>[1],
  ) {
    // @TODO: ide 需要特殊处理下
    if (isInIDE) {
      // todo
    }
    return document.addEventListener("visibilitychange", callback);
  },
  unregisterVisibleChange(
    callback: Parameters<typeof document.removeEventListener>[1],
  ) {
    // @TODO: ide 需要特殊处理下
    if (isInIDE) {
      // todo
    }
    return document.removeEventListener("visibilitychange", callback);
  },
  registerKeydownEvent(callback: (evt: KeyboardEvent) => void) {
    // @TODO: ide 需要特殊处理下
    if (isInIDE) {
      // todo
    }
    return document.addEventListener("keydown", callback);
  },
  unregisterKeydownEvent(callback: (evt: KeyboardEvent) => void) {
    // @TODO: ide 需要特殊处理下
    if (isInIDE) {
      // todo
    }
    return document.removeEventListener("keydown", callback);
  },
};
