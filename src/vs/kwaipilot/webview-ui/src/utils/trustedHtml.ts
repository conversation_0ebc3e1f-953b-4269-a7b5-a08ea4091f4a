// 在一个单独的文件中定义全局 policy (比如 trustedTypes.ts)
let escapeHTMLPolicy: ((html: string) => TrustedHTML) | undefined = undefined;

const getEscapeHTMLPolicy = () => {
  if (!escapeHTMLPolicy) {
    try {
      // 只在第一次调用时创建 policy
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const policy = window.trustedTypes?.createPolicy("kwaipilot", {
        createHTML: (html: string) => html,
      });
      escapeHTMLPolicy = policy?.createHTML.bind(policy);
    }
    catch (e) {
      return null;
    }
  }
  return escapeHTMLPolicy;
};

// @IMP: 不要更改
const innerHtmlAttr = "innerHTML";

// 统一的 HTML 净化函数
// const dompurify = (html: string): string => {
//   return DOMPurify.sanitize(html) as string;
// };

/**
 * 安全地设置元素的 innerHTML，兼容 IDE 内的写法
 * @param __trustedElement 要设置 innerHTML 的元素
 * @param html 要设置的 HTML 内容
 * @param trusted 是否信任这个 HTML 内容（默认不信任）
 */
export function setTrustedHTML(__trustedElement: HTMLElement, html: string): void {
  try {
    // 否则使用 TrustedHTML
    const policy = getEscapeHTMLPolicy();
    if (policy) {
      // 使用类型断言确保类型安全
      __trustedElement[innerHtmlAttr] = policy(html) as unknown as string;
    }
    else {
      // 如果没有 TrustedTypes 支持，使用默认的 innerHTML
      __trustedElement[innerHtmlAttr] = html;
    }
  }
  catch (e) {
    console.error("setTrustedHTML error", e);
  }
}
