interface ThemeMapping {
  [key: string]: string;
}

export const vsCodeToShikiTheme: ThemeMapping = {
  // Light Themes
  "Default Light+": "light-plus",
  "Light+ V2": "light-plus",
  "Solarized Light": "solarized-light",
  "GitHub Light": "github-light",
  "GitHub Light Default": "github-light-default",
  "GitHub Light High Contrast": "github-light-high-contrast",
  "Vitesse Light": "vitesse-light",
  "One Light": "one-light",
  "Min Light": "min-light",
  "Catppuccin Latte": "catppuccin-latte",
  "Everforest Light": "everforest-light",
  "Material Theme Lighter": "material-theme-lighter",
  "Rosé Pine Dawn": "rose-pine-dawn",
  "Slack Ochin": "slack-ochin",
  "Snazzy Light": "snazzy-light",

  // Dark Themes
  "Default Dark+": "dark-plus",
  "Visual Studio Dark": "dark-plus",
  "One Dark Pro": "one-dark-pro",
  "GitHub Dark": "github-dark",
  "GitHub Dark Default": "github-dark-default",
  "GitHub Dark Dimmed": "github-dark-dimmed",
  "GitHub Dark High Contrast": "github-dark-high-contrast",
  "Monokai": "monokai",
  "Dracula": "dracula",
  "Dracula Soft": "dracula-soft",
  "Night Owl": "night-owl",
  "Nord": "nord",
  "Tokyo Night": "tokyo-night",
  "Material Theme": "material-theme",
  "Material Theme Darker": "material-theme-darker",
  "Material Theme Ocean": "material-theme-ocean",
  "Material Theme Palenight": "material-theme-palenight",
  "Solarized Dark": "solarized-dark",
  "Vitesse Dark": "vitesse-dark",
  "Vitesse Black": "vitesse-black",
  "Min Dark": "min-dark",
  "Ayu Dark": "ayu-dark",
  "Everforest Dark": "everforest-dark",
  "Rosé Pine": "rose-pine",
  "Rosé Pine Moon": "rose-pine-moon",
  "Slack Dark": "slack-dark",
  "Synthwave 84": "synthwave-84",
  "Poimandres": "poimandres",
  "Catppuccin Mocha": "catppuccin-mocha",
  "Catppuccin Macchiato": "catppuccin-macchiato",
  "Catppuccin Frappé": "catppuccin-frappe",
  "Kanagawa Wave": "kanagawa-wave",
  "Kanagawa Dragon": "kanagawa-dragon",
  "LaserWave": "laserwave",
  "Andromeeda": "andromeeda",
  "Aurora X": "aurora-x",
  "Houston": "houston",
  "Plastic": "plastic",
  "Red": "red",
  "Vesper": "vesper",
};

// 获取所有支持的 Shiki 主题
export const allShikiThemes = Object.values(vsCodeToShikiTheme);

// 获取常用主题（可以根据需要调整）
export const commonShikiThemes = [
  "light-plus",
  "dark-plus",
  "github-light",
  "github-dark",
  "vitesse-light",
  "vitesse-dark",
  "one-dark-pro",
  "nord",
  "tokyo-night",
  "dracula",
];

// 智能主题匹配函数
export function getShikiTheme(vsCodeTheme: string): string {
  // 直接匹配
  if (vsCodeToShikiTheme[vsCodeTheme]) {
    return vsCodeToShikiTheme[vsCodeTheme];
  }

  // 模糊匹配：将主题名转换为小写并移除特殊字符进行比较
  const normalizedTheme = vsCodeTheme.toLowerCase().replace(/[^a-z0-9]/g, "");
  for (const [key, value] of Object.entries(vsCodeToShikiTheme)) {
    if (
      key
        .toLowerCase()
        .replace(/[^a-z0-9]/g, "")
        .includes(normalizedTheme)
    ) {
      return value;
    }
  }

  // 根据主题名称判断明暗
  const isDark
    = vsCodeTheme.toLowerCase().includes("dark")
    || vsCodeTheme.toLowerCase().includes("night")
    || vsCodeTheme.toLowerCase().includes("black");

  return isDark ? "dark-plus" : "light-plus";
}
