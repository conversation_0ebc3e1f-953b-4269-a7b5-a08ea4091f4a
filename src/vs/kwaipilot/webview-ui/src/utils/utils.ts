export function extractLinks(text: string): string[] {
  const urlPattern = /http[s]?:\/\/[^\s]+/g;
  const urls = text.match(urlPattern);
  return urls || [];
}

export const wrapLineMarkdownContent = (content: string) => {
  const dict = [
    "Final Answer:",
    "Thought:",
    "Action:",
    "Action Input:",
    "Observation:",
    "Answer:",
  ];
  // 使用正则表达式将字典中的内容作为模式进行匹配
  const pattern = new RegExp(dict.join("|"), "g");
  // 使用replace方法，在匹配到字典中的内容后面添加换行符\n
  const wrappedContent = content.replace(pattern, (match) => {
    if (match === "\\n") {
      return "\n  \n";
    }
    else {
      return `  \n**${match}**  \n`;
    }
  });
  return wrappedContent;
};

export const wrapHttpLinks = (content: string) => {
  // 使用正则表达式匹配http链接，排除包含双引号的链接
  const pattern = new RegExp("(http[s]?://[^\"\\s]+)", "g");
  // 使用replace方法，在匹配到的链接前后添加空格
  const wrappedContent = content.replace(pattern, (match) => {
    return ` ${match} `;
  });
  return wrappedContent;
};

export function formatFileSize(bytes: number) {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export function deduplicateArray<T>(
  array: T[],
  equal: (a: T, b: T) => boolean,
): T[] {
  const result: T[] = [];

  for (const item of array) {
    if (!result.some(existingItem => equal(existingItem, item))) {
      result.push(item);
    }
  }

  return result;
}

export function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export const getCurrentSessionTimeString = () =>
  new Date()
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(/\//g, "/");
