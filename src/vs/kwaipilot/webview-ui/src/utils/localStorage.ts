export const LocalStorageKey = {
  sessionHistory: "sessionHistory", // 聊天记录
  curSessionId: "curSessionId", // 活跃的对话ID
  curSession: "curSession", // 活跃的对话信息
  localStorageType: "localStorageType", // 会话模型
  dialogSetting: "dialogSetting", // 会话设置，模型，联网，知识库
  showNotification: "showNotification", // 用户已知更新，关闭了tip
  inlineChatInfo: "inlineChatInfo", // inlinChat Info
  hiddenTips: "hiddenTips", // 隐藏活动tip
  surveyWidget: "surveyWidget", // 问卷挂件
  activeSessionId: "activeSessionId", // inlinechat会有独立的 sessionid
  activePath: "activePath", // 路由地址
  // FIXME: 名称不一致, 但不能轻易改动
  activaSession: "activeSession", // 当前激活的会话id
  activeComposerSessionId: "activeComposerSessionId", // composer会话id
} as const;

export const getLocalStorageValue = (key: keyof typeof LocalStorageKey) => {
  try {
    return localStorage.getItem(key);
  }
  catch (e) {
    console.error("读取 localstorage 失败，失败原因： " + e);
  }
};

export const setLocalStorageValue = (
  key: keyof typeof LocalStorageKey,
  value: string,
) => {
  try {
    if (value === null || value === undefined) {
      return localStorage.setItem(key, "");
    }
    return localStorage.setItem(key, value);
  }
  catch (e) {
    console.error("设置 localstorage 失败，失败原因： " + e);
  }
};

export const removeLocalStorageValue = (key: keyof typeof LocalStorageKey) => {
  try {
    return localStorage.removeItem(key);
  }
  catch (e) {
    console.error("移除 localstorage 失败，失败原因： " + e);
  }
};
