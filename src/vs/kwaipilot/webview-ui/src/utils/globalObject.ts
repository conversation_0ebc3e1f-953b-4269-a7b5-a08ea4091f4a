/*
用于解决循环依赖问题

例如如下经典场景:

某个工具方法需要调用路由跳转, 但因为不在组件中, 需要使用 router 实例

而 router 实例创建会引入所有组件, 此时就很容易产生循环依赖

util -> router -> pageX -> componentY -> util(循环依赖)

因此把一些容易产生循环依赖的对象挂载到全局对象上, 避免这种情况

可以使用这条命令贱虫
pnpx dpdm --tree false -T --warning false ./src/index.tsx
 */

import { type KwaiPilotBridgeAPI } from "@/bridge";
import { type createMemoryRouter } from "react-router-dom";

const mountTarget = Symbol("globalObject");

export interface GlobalObjectMap {
  router: ReturnType<typeof createMemoryRouter>;
  kwaiPilotBridgeAPI: KwaiPilotBridgeAPI;
}

declare global {
  interface Window {
    [mountTarget]: any;
  }
}

window[mountTarget] = {};

export function getGlobalObject<T extends keyof GlobalObjectMap>(key: T): GlobalObjectMap[T] {
  return window[mountTarget][key];
};

export function setGlobalObject<T extends keyof GlobalObjectMap>(key: T, value: GlobalObjectMap[T]): void {
  window[mountTarget][key] = value;
}
