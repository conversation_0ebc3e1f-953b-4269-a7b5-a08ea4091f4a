import { ActionType } from "@shared/types";
import { ReportKeys } from "@shared/types/logger";

const SendTypeMessage: Record<ActionType, string> = {
  comment: "请对以上代码生成注释",
  test: "请对以上代码生成单元测试",
  functionComment: "请对以上代码生成函数注释",
  lineComment: "请对以上代码生成行间注释",
  codeExplanation: "请解释以上代码",
  tuningSuggestion: "请优化以上代码",
  functionSplit: "请拆分上面的函数",
  explain: "请解释以上代码",
  optimization: "请对以上代码进行优化",
};
const ReportType: Record<ActionType, ReportKeys["inlay_hints"]> = {
  comment: "函数注释",
  test: "单元测试",
  functionComment: "函数注释",
  lineComment: "行间注释",
  codeExplanation: "代码解释",
  tuningSuggestion: "代码优化",
  functionSplit: "函数拆分",
  explain: "代码解释",
  optimization: "代码优化",
};

export const getActionInfo = (type: ActionType) => {
  return { message: SendTypeMessage[type], reportType: ReportType[type] };
};
