export const vscodeToShikiLanguageMap: { [key: string]: string } = {
  // 完全匹配的语言
  "javascript": "javascript",
  "typescript": "typescript",
  "python": "python",
  "java": "java",
  "html": "html",
  "css": "css",
  "scss": "scss",
  "less": "less",
  "json": "json",
  "jsonc": "jsonc",
  "yaml": "yaml",
  "markdown": "markdown",
  "vue": "vue",
  "vue-html": "vue-html",
  "xml": "xml",
  "php": "php",
  "shellscript": "shellscript",
  "tsx": "tsx",
  "jsx": "jsx",
  "sql": "sql",
  "wasm": "wasm",
  "r": "r",
  "ruby": "ruby",
  "rust": "rust",

  // 需要映射的语言
  "javascriptreact": "jsx",
  "typescriptreact": "tsx",
  "c": "cpp", // shiki 使用 cpp 支持 c
  "csharp": "cs",
  "plaintext": "txt",
  "jade": "pug",
  "dockerfile": "docker",

  // 别名映射
  "js": "javascript",
  "ts": "typescript",
  "py": "python",
  "yml": "yaml",
  "md": "markdown",

  // 默认fallback
  "default": "txt",
};

export function getShikiLanguage(vscodeLanguageId: string): string {
  return (
    vscodeToShikiLanguageMap[vscodeLanguageId.toLowerCase()]
    || vscodeToShikiLanguageMap.default
  );
}
