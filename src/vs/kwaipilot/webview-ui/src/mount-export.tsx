// NOTE: ide 组件入口，不影响插件逻辑
import { kwaiPilotBridgeAPI } from "@/bridge-export";
import { VSCodeNativeBridge } from "@/bridge-export/kwaipilotBridge";
import "./mount-export.css";
import { initHighlighterInstance } from "./utils/highlighter";
import type Radar from "@ks-radar/radar";

/**
 * 预准备的常用 VSCode Services 接口
 * 在 contribution 中直接准备好具体的服务实例，避免通过字符串 ID 查找
 */
export interface VSCodeServicesAccessor {
  // 命令服务
  commandService?: {
    executeCommand(commandId: string, ...args: any[]): Promise<any>;
  };

  // 配置服务
  configurationService?: {
    getValue<T>(section: string): T | undefined;
    updateValue(section: string, value: any): Promise<void>;
  };

  // 日志服务
  logService?: {
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string | Error, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
  };

  // 主题服务
  themeService?: {
    getColorTheme(): {
      id: string;
      type: string;
      label: string;
    };
  };

  // 存储服务
  storageService?: {
    get(key: string, scope: number): string | undefined;
    store(key: string, value: string, scope: number): void;
    remove(key: string, scope: number): void;
  };

  // 通知服务
  notificationService?: {
    info(message: string): void;
    warn(message: string): void;
    error(message: string): void;
  };

  // 视图服务
  viewsService?: {
    openView(viewId: string, focus?: boolean): Promise<any>;
    openViewContainer(viewContainerId: string, focus?: boolean): Promise<any>;
  };

  // 工作区服务
  workspaceService?: {
    getWorkspace(): any;
    getWorkspaceFolder(resource: any): any;
  };

  // 用户信息监听服务
  userInfoWatcherService?: {
    getAndWatchUserInfo(callback: (userinfo: any) => void): void;
    removeUserInfoWatcher(callback: (userinfo: any) => void): void;
    getCurrentUserInfo(): any;
  };

  radar?: Radar;

  // 扩展方法：通用服务获取器（作为备用方案）
  getService?<T>(serviceToken: any): T | undefined;
}

/**
 * 挂载应用到指定DOM元素
 *
 * @param rootElement - 要挂载应用的DOM元素
 * @param bridge - 桥接对象实现
 * @param accessor - 可选的 VSCode services accessor，用于访问 VSCode 服务
 * @returns 包含重新渲染和销毁方法的对象
 */
const mountApp = async (
  rootElement: HTMLElement,
  bridge: VSCodeNativeBridge,
  accessor?: VSCodeServicesAccessor,
) => {
  if (bridge) {
    try {
      await initHighlighterInstance();
    }
    catch (error) {
      console.error("mountApp: 初始化高亮器失败", error);
    }
    try {
      kwaiPilotBridgeAPI.setBridge(bridge);

      // 如果提供了 accessor，则将其注入到 bridge 中
      if (accessor) {
        kwaiPilotBridgeAPI.setServicesAccessor?.(accessor);
        console.log("mountApp: VSCode services accessor 已成功注入");
        kwaiPilotBridgeAPI.getSession({
          page: 1,
          pageSize: 50,
          timeRange: "all",
        }).then(() => {
          accessor?.radar?.fmp();
        });
      }
      console.log("mountApp: bridge实现已成功注入");
    }
    catch (error) {
      console.error("mountApp: bridge实现注入失败", error);
    }
  }
  else {
    console.error("mountApp: 未提供bridge实现，API调用会失败");
  }

  import("./mountApp-export").then(({ default: mountApp }) => {
    mountApp(rootElement, bridge, accessor);
  });
};

export { mountApp };
