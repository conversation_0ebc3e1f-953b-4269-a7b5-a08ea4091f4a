import { generateCustomUUID, ICachedMessage } from "@/utils/sessionUtils";
import { postMessageUtil } from "@/utils/postMessageUtil";
import { MessageType } from "@/utils/const";
import { useRecordStore } from "@/store/record";

// import "./index.css";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { chatId } from "@/utils/chatId";
import { QA } from "@/components/QA";
import { IChatModelType } from "@shared/types/business";
import { getCurrentSessionTimeString } from "@/utils/utils";
import AutoTooltip from "@/components/AutoTooltip";
import { DEFAULT_MODEL_TYPE } from "@/constant";
import { QAItem } from "@shared/types/chatHistory";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { reportUserLike } from "@/http/api/feedback";
import { produce } from "immer";
import { createPlainTextEditorState } from "../TextArea/lexical/editorState";

export const Dialog = () => {
  const activeSession = useRecordStore(state => state.activeSession);
  const clearSuggestQuestion = useRecordStore(
    state => state.clearSuggestQuestion,
  );
  const chatModel = useRecordStore(state => state.chatModel);
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  const setStopReceiveMessageId = useRecordStore(
    state => state.setStopReceiveMessageId,
  );
  const sessionHistory = useRecordStore(state => state.sessionHistory);

  const messages = useRecordStore<QAItem[]>(
    state => state.sessionHistory?.cachedMessages || [],
  );

  const setSessionHistory = useRecordStore(state => state.setSessionHistory);
  const suggestQuestion = useRecordStore(state => state.suggestQuestion);

  const onResend = (id: string, modelType?: IChatModelType) => {
    clearSuggestQuestion();
    if (!sessionHistory) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "重新生成时不存在对话历史，请联系oncall查看",
      });
      return;
    }
    // 置为 loading false
    if (loadingStatu && loadingStatu.status === "loading") {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "正在生成回答，请稍后尝试",
      });
      return;
    }
    else {
      setStopReceiveMessageId("");
    }

    collectClick("VS_RESEND_BUTTON");
    const parms: ReportOpt<"reNew"> = {
      key: "reNew",
      type: undefined,
    };
    reportUserAction(parms, id);

    const newSessionTime = getCurrentSessionTimeString();
    const index = sessionHistory.cachedMessages.findIndex(m => m.id === id);
    const targetMessage = sessionHistory.cachedMessages[index];
    const targetQuestion = targetMessage.Q;
    const newCachedMsgs = produce(sessionHistory.cachedMessages, (draft) => {
      // 重新生成不删除
      draft[index].A.push({
        ...draft[index].A[0],
        reply: "",
        actionResults: [],
        modelType: modelType
          ? modelType
          : (chatModel.modelType ?? "kwaipilot_pro_32k"),
        answerId: generateCustomUUID(),
      });
    });

    const useSearch = targetQuestion.v2 ? targetQuestion.contextItems?.some(v => v.type === "web") : /* 默认 false 处理是否有问题 */false;
    const docId = targetQuestion.v2 ? targetQuestion.contextItems?.find(v => v.type === "knowledge")?.doc.id || 0 : 0;

    setSessionHistory({
      ...sessionHistory,
      cachedMessages: newCachedMsgs,
      sessionTime: newSessionTime,
    });
    // 发送给extension
    const startTime = Date.now();
    chatId.updateChatId(id);
    postMessageUtil({
      startTime,
      content:
        newCachedMsgs[index]?.Q.formatQuestion
        || newCachedMsgs[index]?.Q.question,
      type: MessageType.RESEND_MESSAGE,
      cachedMessages: newCachedMsgs,
      // 模型类型：intelligentChat / ChatGPT
      chatType: "intelligentChat",
      sessionId: activeSession,
      uniqueId: id,
      useSearch,
      chatModel: modelType ? { modelType } : chatModel,
      docId,
      expiredIndex: sessionHistory ? [...sessionHistory.expiredIndex] : [],
      clearContextIndex: sessionHistory
        ? [...sessionHistory.clearContextIndex]
        : [],
      vendor: sessionHistory.isComposer ? "composer" : "chat",
    });
  };
  const onLikeOrUnlike = (param: { val: ICachedMessage; isLike?: boolean }) => {
    if (!sessionHistory) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "重新生成时不存在对话历史，请联系oncall查看",
      });
      return;
    }
    const { val: item, isLike } = param;

    if (!item) {
      return;
    }

    if (isLike !== undefined) {
      collectClick(isLike ? "VS_LIKE_BUTTON" : "VS_UNLIKE_BUTTON");
      if (isLike) {
        const parms: ReportOpt<"like"> = {
          key: "like",
          type: undefined,
        };
        reportUserAction(parms, item.id);
      }
      else {
        const parms: ReportOpt<"dislike"> = {
          key: "dislike",
          type: undefined,
        };
        reportUserAction(parms, item.id);
      }
      reportUserLike({
        chatId: item.id,
        reply: item.reply ?? "",
        question: item.question,
        isLike,
      });
    }
    // 新增或者更新对应的 cacheMessage
    const cachedMessages = [...sessionHistory.cachedMessages];
    const cachedMessagesIndex = cachedMessages.findIndex(
      msg => msg.id === item.id,
    );
    if (cachedMessagesIndex === -1) {
      return;
    }
    const ansIndex = cachedMessages[cachedMessagesIndex].A.findIndex(
      a => a.reply === item.reply,
    );
    if (ansIndex === -1) {
      return;
    }
    const newSessionHistory = produce(sessionHistory, (draft) => {
      draft.cachedMessages[cachedMessagesIndex].A[ansIndex].likeStatus = isLike === undefined
        ? "cancel"
        : isLike
          ? "like"
          : "unlike";
    });
    setSessionHistory(newSessionHistory);
    kwaiPilotBridgeAPI.addMessage({
      item: cachedMessages[cachedMessagesIndex],
      sessionId: activeSession,
      chatId: item.id,
    });
  };
  const handleRestoreContext = async () => {
    if (!sessionHistory) {
      return;
    }
    kwaiPilotBridgeAPI.updateSessionInfo({
      sessionId: sessionHistory.sessionId,
      expiredIndex: [],
    });
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: "已恢复上下文记忆",
    });
    setSessionHistory(
      {
        ...sessionHistory,
        expiredIndex: [],
      },
      {
        checkExpirationStatus: false,
      },
    );
  };
  const handleSuggestQuestion = (question: string) => {
    clearSuggestQuestion();
    const para: ReportOpt<"guessAsk"> = {
      key: "guessAsk",
      type: question,
    };
    reportUserAction(para);
    // todo: 发送消息
    const uniqueId
      = Date.now().toString(36) + Math.random().toString(36).substr(2);
    chatId.updateChatId(uniqueId);

    // 取最后一个 message 配置
    const lastMessage = messages.at(-1);
    const lastMessageQ = lastMessage?.Q;
    if (!lastMessageQ) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "无法获取最后一条消息的配置，请联系oncall查看",
      });
      return;
    }
    const useSearch = lastMessageQ.v2 ? lastMessageQ.contextItems?.some(v => v.type === "web") : /* 默认 false 处理是否有问题 */false;
    const docId = lastMessageQ.v2 ? lastMessageQ.contextItems?.find(v => v.type === "knowledge")?.doc.id || 0 : 0;

    const newCacheMessage: QAItem = {
      id: uniqueId,
      Q: {
        isSelf: true,
        id: uniqueId,
        question: question,
        reply: "",
        modelType: chatModel.modelType || DEFAULT_MODEL_TYPE,
        v2: true,
        plainText: question,
        editorState: createPlainTextEditorState(question),
        contextItems: [],
      },
      A: [
        {
          isSelf: false,
          modelType: chatModel.modelType ?? DEFAULT_MODEL_TYPE,
          id: uniqueId,
          question: question,
          reply: "",
          answerId: generateCustomUUID(),
        },
      ],
    };
    newCacheMessage.Q.formatQuestion = question;
    newCacheMessage.A[0].formatQuestion = question;
    const newSessionTime = getCurrentSessionTimeString();
    if (sessionHistory) {
      setSessionHistory({
        ...sessionHistory,
        cachedMessages: [...sessionHistory.cachedMessages, newCacheMessage],
        sessionTime: newSessionTime,
      });
    }
    const startTime = Date.now();
    postMessageUtil({
      startTime,
      content: question,
      type: MessageType.SEND_MESSAGE,
      cachedMessages: sessionHistory?.cachedMessages ?? [],
      chatType: "intelligentChat",
      sessionId: activeSession,
      uniqueId,
      useSearch,
      refFiles: [],
      chatModel: chatModel || { modelType: DEFAULT_MODEL_TYPE },
      docId,
      expiredIndex: sessionHistory ? [...sessionHistory.expiredIndex] : [],
      clearContextIndex: sessionHistory
        ? [...sessionHistory.clearContextIndex]
        : [],
      vendor: sessionHistory?.isComposer ? "composer" : "chat",
    });
  };
  /**
   * 渲染上下文提示
   * @param index
   * @returns
   */
  const renderATip = (index: number) => {
    if (
      !sessionHistory
      || (!sessionHistory.expiredIndex && !sessionHistory.clearContextIndex)
    ) {
      return null;
    }
    const isExpired = sessionHistory.expiredIndex?.includes(index);
    const isClear = sessionHistory.clearContextIndex?.includes(index);
    if (isClear) {
      // 用户手动清除优先级更高
      return (
        <div className="w-full flex items-center gap-3 mt-6 justify-center">
          <div className="text-[13px] leading-[19.5px] text-text-common-tertiary">
            上下文已清空
          </div>
        </div>
      );
    }
    if (isExpired) {
      return (
        <div className="w-full flex items-center gap-3 mt-6 justify-center">
          <div className="text-[13px] leading-[19.5px] text-text-common-tertiary">
            会话长时间未活跃，上下文已清空
          </div>
          {sessionHistory.expiredIndex[
            sessionHistory.expiredIndex.length - 1
          ] === index && (
            <div
              onClick={handleRestoreContext}
              className="text-[13px] leading-[19.5px] text-text-brand-default hover:text-text-brand-hover cursor-pointer"
            >
              恢复上下文
            </div>
          )}
        </div>
      );
    }
  };

  return (
    <div className="app-container  h-full flex flex-col gap-[8px] text-[13px]">
      <div className="flex flex-col gap-[16px]">
        {messages?.map((item, index) => {
          return (
            <div key={item.id + index.toString()}>
              {/** 问题和回答展示 */}
              <QA
                data={item}
                onResend={modelType => onResend(item.id, modelType)}
                onLikeOrUnlike={(item, isLike) =>
                  onLikeOrUnlike({
                    val: item,
                    isLike,
                  })}
                isLast={index === messages.length - 1}
              />
              {renderATip(index)}
            </div>
          );
        })}
      </div>
      <div className="flex flex-col items-start gap-[8px] w-full">
        {sessionHistory && sessionHistory.cachedMessages.length > 0
        && /* https://team.corp.kuaishou.com/task/******** */loadingStatu?.status !== "loading"
          ? suggestQuestion?.map((v) => {
            return (
              <div
                key={v}
                className="bg-list-inactiveSelectionBackground rounded-[6px] px-[12px] py-[8px] text-foreground hover:text-text-common-primary cursor-pointer"
                onClick={() => handleSuggestQuestion(v)}
              >
                <AutoTooltip lineClamp={2} title={v}>
                  {v}
                </AutoTooltip>
              </div>
            );
          })
          : null}
      </div>
    </div>
  );
};
