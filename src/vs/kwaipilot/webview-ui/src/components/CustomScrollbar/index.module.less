.k-custom-ms {
  scrollbar-width: none;
  :global {
    .ms-active{
      .ms-thumb{
        width: 5px !important;
        background-color: var(--vscode-scrollbarSlider-activeBackground);
      }
    }

    .ms-y {
      width: 5px;
      background: transparent;
      border-width: 0;

      .ms-thumb {
        width: 5px;
        background-color: var(--vscode-scrollbarSlider-background);
        border-radius: 5px;
        &:hover {
          width: 5px;
        }
      }

      &:hover {
        width: 5px;
        background: transparent;

        .ms-thumb {
          width: 5px;
          background-color: var(--vscode-scrollbarSlider-hoverBackground);
        }
      }
    }

    .ms-track.ms-y .ms-thumb:after {
      width: 5px;
    }

    .ms-track.ms-x .ms-thumb:after {
      height: 5px;
    }

    .ms-x {
      height: 5px;
      background: transparent;
      border-width: 0;

      .ms-thumb {
        width: 5px;
        background-color:var(--vscode-scrollbarSlider-background);
        &:hover {
          width: 5px;
        }
      }

      &:hover {
        height: 5px;
        background: transparent;

        .ms-thumb {
          height: 5px;
          background-color: var(--vscode-scrollbarSlider-hoverBackground);

          &::after {
            height: 5px;
          }
        }
      }
    }
  }
}
