import { IChatModelType } from "@shared/types/business";

const getBgAndText = (modelType: IChatModelType) => {
  switch (modelType) {
    case "kwaipilot_pro_32k":
    case "kwaipilot_pro_64k":
    case "kwaipilot_turbo_128k":
      return {
        bgName: "bg-tag-bg-kwaipilot",
        textColorName: "text-tag-text-kwaipilot",
      };
    case "GPT4":
    case "GPT4O":
      return {
        bgName: "bg-tag-bg-gpt",
        textColorName: "text-tag-text-gpt",
      };
    default:
      return {
        bgName: "bg-tag-bg-claude",
        textColorName: "text-tag-text-claude",
      };
  }
};

const getTagName = (modelType: IChatModelType) => {
  switch (modelType) {
    case "kwaipilot_pro_32k":
    case "kwaipilot_pro_64k":
      return "Kwaipilot Pro";
    case "kwaipilot_turbo_128k":
      return "Kwaipilot";
    case "GPT4":
    case "GPT4O":
      return "GPT-4o";
    default:
      return "Claude 3.7 Sonnet";
  }
};

export const CardWithTag = ({
  modelType,
  children,
}: {
  modelType: IChatModelType;
  children?: React.ReactNode;
}) => {
  const { bgName, textColorName } = getBgAndText(modelType);
  const tagName = getTagName(modelType);
  return (
    <div className="flex flex-col">
      {children}
      <div className="flex justify-end h-[22px] items-end rounded-[8px]">
        <div
          className={`h-[18px] rounded-tl-[8px] rounded-br-[6.5px] ${bgName}`}
        >
          <div
            className={`px-[8px] leading-[18px] text-[11px] ${textColorName}`}
          >
            {tagName}
          </div>
        </div>
      </div>
    </div>
  );
};
