import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import AutoTooltip from "@/components/AutoTooltip";
import { reportCopyMessage } from "@/http/api/feedback";
import { startInstantApply } from "@/http/api/instant-apply";
import repoChatService from "@/services/repo-chat";
import {
  useComposerStatusStore,
  FileStateUIType,
} from "@/store/composerStatus";
import { useRecordStore, useRecoreStoreContext } from "@/store/record";
import { logger } from "@/utils/logger";
import { reportUserAction, collectClick } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { produce } from "immer";
import { useMemo, useCallback, useState, useEffect, useContext } from "react";
import { ExtraProps } from "react-markdown";

import CopyIcon from "@/assets/copy.svg?react";
import ApplyIcon from "@/assets/apply.svg?react";
import InstantApplyIcon from "@/assets/instant-apply.svg?react";
import ReApplyIcon from "@/assets/reapply.svg?react";
import ComposerAcceptIcon from "@/assets/composer-accept.svg?react";
import ComposerRejectIcon from "@/assets/composer-reject.svg?react";
import { Highlight } from "@/components/Dialog/Highlight";
import { MarkdownRenderContext } from "./MarkdownRenderContext";
import { Icon } from "@/components/Union/t-iconify";
import { getFileExtension, getIcon } from "@/utils/fileIcon";
import { SingleIcon } from "@/components/SingleIcon";
import { Loading } from "@/logics/composer/tools/components/loading";

export type MarkdownCodeRendererProps = JSX.IntrinsicElements["code"] &
  ExtraProps;

export const MarkdownCodeRenderer = (
  props: MarkdownCodeRendererProps,
) => {
  const { children, className, node } = props;

  const renderContext = useContext(MarkdownRenderContext);

  if (!renderContext) {
    throw new Error("MarkdownCodeRenderer must be used within a MarkdownRender");
  }
  const { qaItem, detail } = renderContext;

  const isComposerMode = useRecordStore(
    state => state.sessionHistory?.isComposer,
  );

  const filepath = className?.split(":")[1]?.replace("-SPACE-", " ");
  const match = /language-(\w+)/.exec(className || "");
  const fileName = filepath ? filepath.split("/").pop() : match?.[1] || "";
  const iconType = getIcon("index" + getFileExtension(match?.[1] || "typescript"), false);

  const loadingStatu = useRecordStore(state => state.loadingStatu);
  const isLoading = useMemo(
    () =>
      !!loadingStatu
      && loadingStatu.id === detail.id
      && loadingStatu.status === "loading",
    [detail.id, loadingStatu],
  );

  const onCopy = useCallback(
    (text: string) => {
      kwaiPilotBridgeAPI.copyToClipboard(text.replace(/\n$/, ""));
      reportCopyMessage({
        isBlock: true,
        reply: text,
        chatId: detail.id,
        question: detail.question,
      });
      const parms: ReportOpt<"copy"> = {
        key: "copy",
        type: "llmMsgCode",
      };
      reportUserAction(parms, detail.id);
      collectClick("VS_COPY_RESPONSE");
    },
    [detail.id, detail.question],
  );
  const handleInstantApply = useCallback(
    async (filePath: string) => {
      // 智能采纳
      const parms: ReportOpt<"instantApply"> = {
        key: "instantApply",
        type: undefined,
      };
      reportUserAction(parms, detail.id);
      const absolutePath = repoChatService.getAbsolutePath(filePath);
      const fileRet = await kwaiPilotBridgeAPI.fs.readFile(absolutePath);
      if (!fileRet) {
        kwaiPilotBridgeAPI.showToast({
          message: "文件已被删除",
          level: "error",
        });
        return;
      }
      // NOTE: 这里filepath是相对路径，fileContent是文件内容
      startInstantApply({
        files: [{ filePath, fileContent: fileRet?.content ?? "" }],
        modelOutput: String(children),
        sessionId: activeSession,
        chatId: detail.id,
      });
    },
    // FIXME: eslint
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [children, detail.id],
  );
  const insertCodeMessage = useCallback(
    (text: string) => {
      // 查看对应代码块的diff
      logger.info(text, "markdown-render");
      const parms: ReportOpt<"codeBlockApply"> = {
        key: "codeBlockApply",
        type: "llmMsgCode",
      };
      reportUserAction(parms, detail.id);

      if (detail.section && detail.fullPath) {
        kwaiPilotBridgeAPI.editor.showCodeDiff({
          content: text,
          section: detail.section,
          fullPath: detail.fullPath,
        });
      }
      else {
        kwaiPilotBridgeAPI.editor.insertCode({
          content: text,
        });
      }
    },
    [detail.fullPath, detail.id, detail.section],
  );
  const activeSession = useRecordStore(state => state.activeSession);
  const updateFileState = useComposerStatusStore(s => s.updateFileState);

  const fileStateMap = useComposerStatusStore(state => state.fileStateMap);
  const fileState = useMemo<FileStateUIType>(() => {
    if (!isComposerMode) {
      return "init";
    }
    if (!filepath) {
      return "init";
    }
    return fileStateMap[qaItem.id]?.[filepath]?.state;
  }, [fileStateMap, filepath, isComposerMode, qaItem.id]);

  const recoredStoreContext = useRecoreStoreContext();
  const setFileState = useCallback(
    (state: FileStateUIType) => {
      if (!isComposerMode) {
        return;
      }
      if (!filepath) {
        return;
      }
      updateFileState(qaItem.id, filepath, state);
      if (state === "accepted" || state === "rejected") {
        const targetQAItem = recoredStoreContext
          .getState()
          .sessionHistory?.cachedMessages.find(v => v.id === qaItem.id);
        if (!targetQAItem) {
          return;
        }
        const modifiedQAItem = produce(targetQAItem, (draft) => {
          const latestAnswer = draft?.A.at(-1);
          if (!latestAnswer) {
            return;
          }
          latestAnswer.affectedFileState ||= {};
          latestAnswer.affectedFileState[filepath] = {
            state,
          };
        });

        kwaiPilotBridgeAPI.addMessage({
          item: modifiedQAItem,
          sessionId: activeSession,
          chatId: qaItem.id,
        });
      }
    },
    [
      isComposerMode,
      filepath,
      updateFileState,
      qaItem.id,
      recoredStoreContext,
      activeSession,
    ],
  );
  const handleReapply = useCallback(async () => {
    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "文件路径未找到",
        level: "error",
      });
      return;
    }
    // TODO: 埋点
    const absolutePath = repoChatService.getAbsolutePath(filepath);
    const fileRet = (await kwaiPilotBridgeAPI.fs.readFile(absolutePath)) ?? "";
    setFileState("applying");
    startInstantApply(
      {
        files: [{ filePath: filepath, fileContent: fileRet?.content ?? "" }],
        modelOutput: String(children),
        sessionId: activeSession,
        chatId: detail.id,
      },
    )
      .then(() => {
        setFileState("applied");
      })
      .catch((reason) => {
        logger.error("MarkdownCodeRenderer", "MarkdownCodeRenderer", {
          err: reason,
        });
        setFileState("init");
        throw reason;
      });
    // FIXME: eslint
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [children, filepath, setFileState]);

  const isApplying = useMemo(() => fileState === "applying", [fileState]);

  const handleAccept = useCallback(() => {
    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "文件路径未找到",
        level: "error",
      });
      return;
    }
    kwaiPilotBridgeAPI.editor.acceptDiff(filepath);
    setFileState("accepted");
  }, [filepath, setFileState]);
  const handleReject = useCallback(() => {
    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "文件路径未找到",
        level: "error",
      });
      return;
    }
    kwaiPilotBridgeAPI.editor.rejectDiff(filepath);
    setFileState("rejected");
  }, [filepath, setFileState]);

  const [hasApplied, setHasApplied] = useState(false);

  useEffect(() => {
    if (fileState !== "init") {
      return;
    }
    if (hasApplied) {
      return;
    }
    if (!isLoading) {
      return;
    }
    if (!isComposerMode) {
      return;
    }
    if (filepath && node?.position && detail.reply) {
      const {
        start: { offset: startOffset },
        end: { offset: endOffset },
      } = node.position;
      const reachCloingSig = detail.reply
        .slice(startOffset, endOffset)
        .endsWith("\n```");
      if (reachCloingSig) {
        setHasApplied(true);
        handleReapply().then(() => {
          // instantApply 完成后再设置状态
          setFileState("applied");
        });
      }
    }
  }, [
    filepath,
    node?.position,
    detail.reply,
    fileState,
    handleReapply,
    isLoading,
    hasApplied,
    isComposerMode,
    setFileState,
  ]);

  return match
    ? (
        <div className="border border-[var(--vscode-terminal-border)] rounded-[4px]">
          <div
            className="flex items-center gap-2 text-[13px]leading-[18px] h-[32px] bg-editorHoverWidget-statusBarBackground px-[12px] rounded-t-[4px]"
            style={{ fontFamily: "PingFang SC" }}
          >
            {/* 文件图标和名称 */}
            <div
              className="flex items-center gap-[4px] justify-center cursor-pointer text-foreground"
              onClick={() => filepath && kwaiPilotBridgeAPI.editor.openFileToEditorMaybeDiffEditor(filepath)}
            >
              <Icon icon={iconType} className="w-[14px] h-[14px]" />
              <AutoTooltip className="flex-1" title={fileName} lineClamp={1}>
                {fileName}
              </AutoTooltip>

              {/* 加载状态指示器 */}
              { fileState === "applying"
                ? (
                    <div className="flex items-center justify-center">
                      <Loading text="正在应用" />
                    </div>
                  )
                : fileState === "accepted"
                  ? <ComposerAcceptIcon className="size-[14px] text-green-500" />
                  : fileState === "rejected"
                    ? <ComposerRejectIcon className="size-[14px] text-red-500" />
                    : <span></span>}

            </div>

            {/* 工具按钮 */}
            {!isLoading && (
              <div className="flex items-center ml-auto text-foreground">

                <SingleIcon
                  title="复制"
                  onClick={() => {
                    const parms: ReportOpt<"copy"> = {
                      key: "copy",
                      type: "llmMsgCode",
                    };
                    reportUserAction(parms, detail.id);
                    onCopy(String(children).replace(/\n$/, ""));
                    collectClick("VS_COPY_CODE");
                  }}
                >
                  <CopyIcon className="size-[14px]" />
                </SingleIcon>
                <SingleIcon
                  title="插入至光标处"
                  onClick={() => insertCodeMessage(children?.toString() || "")}
                >
                  <ApplyIcon className="size-[14px]" />
                </SingleIcon>
                {
                  filepath && (
                    <>
                      {fileState === "applied"
                        ? (
                            <>
                              <SingleIcon
                                title="拒绝"
                                onClick={handleReject}
                              >
                                <ComposerRejectIcon className="size-[14px]" />
                              </SingleIcon>
                              <SingleIcon
                                title="接受"
                                onClick={handleAccept}
                              >
                                <ComposerAcceptIcon className="size-[14px]" />
                              </SingleIcon>
                            </>
                          )
                        : fileState === "init"
                          ? (
                              <SingleIcon
                                title="应用"
                                onClick={handleReapply}
                                disabled={isApplying}
                              >
                                <InstantApplyIcon />
                              </SingleIcon>
                            )
                          : (
                              <SingleIcon
                                title="重新应用"
                                onClick={() => filepath && handleInstantApply(filepath)}
                                disabled={isApplying}
                              >
                                <ReApplyIcon className="size-[14px]" />
                              </SingleIcon>
                            )}
                    </>
                  )
                }

              </div>
            )}
          </div>

          <Highlight
            language={match?.[1]}
            overflowX
          >
            {children}
          </Highlight>
        </div>

      )
    : (
        <span className={className}>{children}</span>
      );
};
