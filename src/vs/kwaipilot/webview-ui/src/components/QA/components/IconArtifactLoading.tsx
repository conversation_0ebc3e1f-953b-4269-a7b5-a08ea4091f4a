import React from "react";
import style from "./IconArtifactLoading.module.less";
import clsx from "clsx";

export function IconArtifactLoading({ className }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={clsx(" w-[10px] h-[10px] border border-solid border-text-brand-default  rounded-full  relative", className, style.circle)}>
      <div className=" w-[6px] absolute h-[6px] bg-icon-brand-default rounded-full left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"></div>
    </div>
  );
}
