import Biaoge from "@/assets/fileicons/biaoge02_area.svg?react";
import Csv from "@/assets/fileicons/csv_area.svg?react";
import Js from "@/assets/fileicons/js_area.svg?react";
import Keynote from "@/assets/fileicons/keynote_area.svg?react";
import Markdown from "@/assets/fileicons/markdown_area.svg?react";
import Pdf from "@/assets/fileicons/pdf_area.svg?react";
import Ppt from "@/assets/fileicons/ppt_area.svg?react";
import Tupian from "@/assets/fileicons/tupian_area.svg?react";
import Txt from "@/assets/fileicons/txt_area.svg?react";
import Unknow from "@/assets/fileicons/unknown_area.svg?react";
import Wendang from "@/assets/fileicons/wendang02_area.svg?react";

/** 获取文件后缀 */
export const getFileExt = (filename: string) => {
  const paths = filename.split(".");
  return paths.length > 1 ? paths.pop() : "";
};

/** 文件类型ICON字典 */
export const FileIconType = (ext?: string, type?: string) => {
  switch (ext) {
    // js
    case "js":
      return Js;
    // md
    case "md":
      return Markdown;

    // 文档
    case "doc":
    case "docx":
    case "wps":
      return Wendang;

    // 表格
    case "xls":
    case "xlsx":
      return Biaoge;

    // csv
    case "csv":
      return Csv;

    // PPT
    case "ppt":
    case "pptx":
      return Ppt;

    // Mac Office
    case "pages":
    case "numbers":
    case "key":
      return Keynote;

    // 文本
    case "txt":
    case "rtf":
      return Txt;

    // PDF
    case "pdf":
      return Pdf;

    // 压缩包
    case "zip":
    case "zipx":
    case "zix":
    case "rar":
    case "7z":
      return Unknow;

    // 视频
    case "avi":
    case "wmv":
    case "mp4":
    case "3gp":
    case "3g2":
    case "mkv":
    case "rm":
    case "rmvb":
    case "mov":
    case "qt":
    case "ogg":
    case "ogv":
    case "oga":
    case "mod":
      return Unknow;

    // 音频
    case "wav":
    case "flac":
    case "ape":
    case "mp3":
    case "aac":
      return Unknow;

    default: {
      return !type
        ? Unknow
        : type.includes("image")
          ? Tupian
          : type.includes("video")
            ? Unknow
            : type.includes("audio")
              ? Unknow
              : Unknow;
    }
  }
};
