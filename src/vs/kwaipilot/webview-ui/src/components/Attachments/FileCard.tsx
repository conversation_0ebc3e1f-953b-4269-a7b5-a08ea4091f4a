import { Box, Progress, useColorMode } from "@chakra-ui/react";
import TrashIcon from "@/assets/images/trash-bold.svg?react";
import { UploadFile } from "@/components/TextArea";
import { formatFileSize } from "@/utils/utils";
import { FileIconType, getFileExt } from "./FileIcon";

export interface FileCardProps {
  file: UploadFile;
  onDeleteFile?: () => void;
  className?: string;
}
export const FileCard = (props: FileCardProps) => {
  const { colorMode: theme } = useColorMode();

  const {
    file: { filename, status, size, type, progress },
    className,
    onDeleteFile,
  } = props;
  const formatSize = formatFileSize(size);

  const FileIcon = FileIconType(getFileExt(filename), type);

  return (
    <div
      className={`flex flex-col mb-[8px] px-[8px] py-[4px] h-[30px] rounded relative upload-card-bg-${theme} ${className}`}
    >
      <Box display="flex" alignItems="center" gap="8px" flex="1">
        <div>
          <FileIcon className={`w-[14px] h-[14px] icon-edit-fill-${theme}`} />
        </div>
        <div className="flex-1 overflow-hidden whitespace-nowrap overflow-ellipsis">
          {filename}
        </div>
        <div className="ml-[4px]">{formatSize}</div>
        <div>
          {onDeleteFile && (
            <TrashIcon
              className={`w-[14px] h-[14px] cursor-pointer icon-edit-fill-${theme}`}
              onClick={onDeleteFile}
            />
          )}
        </div>
      </Box>
      {status === "uploading"
        ? (
            <Progress
              value={progress}
              size="xs"
              height="2px"
              borderRadius="6px"
              position="absolute"
              bottom="0"
              width="calc(100% - 16px)"
              left="8px"
            />
          )
        : null}
    </div>
  );
};
