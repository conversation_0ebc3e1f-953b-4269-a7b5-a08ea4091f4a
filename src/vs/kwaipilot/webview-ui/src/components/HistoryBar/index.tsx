import { useNavigate } from "react-router-dom";
import { useToast } from "@chakra-ui/react";
import baseInfoManager from "@/utils/baseInfo";
import { useUserStore } from "@/store/user";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useState, useEffect } from "react";
import { reportUserAction } from "@/utils/weblogger";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";

import BetaIcon from "@/assets/beta-composer.svg?react";

function useDeveloperModeDisclosure(): {
  tap(): void;
  isDeveloperMode: boolean;
} {
  const MAX_TAP_DURATION = 500;
  const MAX_TAP_COUNT = 10;

  const toast = useToast();
  const [clickCount, setClickCount] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);
  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode") ?? false;

  useEffect(() => {
    if (clickCount >= MAX_TAP_COUNT) {
      kwaiPilotBridgeAPI.extensionDeveloper.$setIsDeveloperMode(!isDeveloperMode)
        .then(() => {
          toast({
            title: `开发者模式已${isDeveloperMode ? "关闭" : "开启"}`,
            status: "success",
          });
        });
      setClickCount(0);
    }
  }, [clickCount, isDeveloperMode, toast]);

  const tap = () => {
    const now = Date.now();
    if (now - lastClickTime > MAX_TAP_DURATION) {
      setClickCount(1);
    }
    else {
      setClickCount(prev => prev + 1);
    }
    setLastClickTime(now);
  };

  return {
    tap,
    isDeveloperMode,
  };
}

export const HistoryBar = ({
  current = "chat",
  action,
}: {
  // TODO: 根据路由自动匹配 current
  current?: "chat" | "history" | "composer" | "composer-v2";
  action?: React.ReactNode;
}) => {
  const navigate = useNavigate();
  const userInfo = useUserStore(state => state.userInfo);
  const { tap, isDeveloperMode } = useDeveloperModeDisclosure();

  return (
    <div className="flex justify-between w-full h-9 items-center px-[12px] py-[7px] leading-[22px] text-[13px]" onClick={tap}>
      <div className="flex gap-[16px] items-center">
        {!baseInfoManager.isXcode && (
          <div
            onClick={() => {
              reportUserAction({
                key: "compose_agent_show",
                type: undefined,
              });
              navigate("/composer-v2");
            }}
            className={`hover:text-tab-activeForeground cursor-pointer relative ${
              current === "composer-v2"
                ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:left-1/2 after:transform after:-translate-x-1/2 after:bg-tab-activeForeground after:w-12 after:rounded-[12px_12px_2px_2px]`
                : "text-tab-inactiveForeground"
            }`}
          >
            <div className="flex items-center gap-[2px]">
              <span className="group-hover:text-text-common-primary">智能体</span>
              <BetaIcon />
            </div>
          </div>
        )}
        <div
          onClick={() => {
            navigate("/chat");
          }}
          className={`hover:text-tab-activeForeground cursor-pointer relative ${
            current === "chat"
              ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:left-1/2 after:transform after:-translate-x-1/2 after:w-7 after:rounded-[12px_12px_2px_2px] after:bg-tab-activeForeground`
              : "text-tab-inactiveForeground"
          }`}
        >
          问答
        </div>
        {/* 不登录则不展示历史记录页面 */}
        {userInfo && (
          <div
            onClick={() => {
              navigate("/history");
            }}
            className={`hover:text-tab-activeForeground cursor-pointer relative ${
              current === "history"
                ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:bg-tab-activeForeground after:left-1/2 after:transform after:-translate-x-1/2 after:w-7 after:rounded-[12px_12px_2px_2px]`
                : "text-tab-inactiveForeground"
            }`}
          >
            历史
          </div>
        )}
        {isDeveloperMode && (
          <>
            <div className="text-text-common-tertiary font-semibold text-xs italic select-none">开发者模式</div>
          </>
        )}
      </div>
      <div className="flex gap-3">{action}</div>
    </div>
  );
};
