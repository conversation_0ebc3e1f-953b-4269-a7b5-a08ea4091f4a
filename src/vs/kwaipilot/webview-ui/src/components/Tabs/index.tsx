import clsx from "clsx";
import { useColorMode } from "@chakra-ui/react";
import { tabArray, IChatType } from "@/utils/sessionUtils";

import "./index.css";

export interface ITabsProps {
  value: IChatType;
  onChange: (type: IChatType) => void;
  bordered?: boolean;
}

/** 对话列表某一项展示 */
export default function Tabs(props: ITabsProps) {
  const { value, onChange, bordered } = props;
  const { colorMode: theme } = useColorMode();

  return (
    <div
      className={clsx(
        `h-[35px] flex items-center flex-shrink-0 px-[4px] w-full`,
        bordered && `tabs-${theme}`,
      )}
    >
      {tabArray.map(tab => (
        <div
          key={tab.key}
          className={clsx(
            `text-[13px] tabs-item-${theme} mx-[8px] h-[100%] leading-[35px] cursor-pointer`,
            {
              "tabs-item-active-light": tab.key === value && theme === "light",
              "tabs-item-active-dark": tab.key === value && theme === "dark",
            },
          )}
          onClick={() => {
            if (tab.key === value) {
              return;
            }
            onChange(tab.key);
          }}
        >
          {tab.name}
        </div>
      ))}
    </div>
  );
}
