import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON>Moda<PERSON>,
  Portal as ChakraPortal,
  AlertDialog as <PERSON><PERSON><PERSON><PERSON>tD<PERSON><PERSON>,
  Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@chakra-ui/react";
import { useRef, useEffect } from "react";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import { getRootContainer, getRootContainerId } from "@/utils/dom";

const withSafePortal = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
) => {
  return (props: P & { containerRef?: React.RefObject<HTMLElement> }) => {
    const defaultContainerRef = useRef<HTMLElement | null>(null);
    const [, isKwaiPilotIDE] = useIdeEnv();

    useEffect(() => {
      const containerId = getRootContainerId();
      if (!props.containerRef) {
        defaultContainerRef.current = getRootContainer();
      }
      else {
        if (props.containerRef.current === document.body) {
          const msg = `IDE 环境下，不能将 ${WrappedComponent.displayName} 的 containerRef 设置为 document.body，请使用 ${containerId} 下面的其他容器元素`;
          if (isKwaiPilotIDE) throw new Error(msg);
          console.error(msg);
        }
      }
    }, [props.containerRef, isKwaiPilotIDE]);

    return (
      <WrappedComponent
        {...props}
        containerRef={
          props.containerRef?.current ? props.containerRef : defaultContainerRef
        }
        // model 特有的属性
        portalProps={{
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          ...props?.portalProps,
          containerRef: props.containerRef?.current
            ? props.containerRef
            : defaultContainerRef,
        }}
      />
    );
  };
};

// 包装所有需要 Portal 功能的组件
export const Tooltip = withSafePortal(ChakraTooltip);
export const Popover = withSafePortal(ChakraPopover);
export const Menu = withSafePortal(ChakraMenu);
export const Modal = withSafePortal(ChakraModal);
export const Portal = withSafePortal(ChakraPortal);
export const AlertDialog = withSafePortal(ChakraAlertDialog);
export const Drwawer = withSafePortal(ChakraDrawer);
// export const AlertDialogOverlay = withSafePortal(ChakraAlertDialogOverlay);
