import { useEffect, useState } from "react";
import "@/components/Tips/index.css";
import { useColorMode } from "@chakra-ui/react";
import {
  LocalStorageKey,
  getLocalStorageValue,
  setLocalStorageValue,
} from "@/utils/localStorage";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { getTipsConfig } from "@/http/api/tip";

export function Tips() {
  const { colorMode } = useColorMode();
  const [renderInfo, setRenderInfo] = useState<any>(null);
  const [renderStyle, setRenderStyle] = useState({});
  function handleClose(ev: any) {
    ev.stopPropagation();
    ev.preventDefault();
    setLocalStorageValue(LocalStorageKey.hiddenTips, renderInfo?.name || "");
    setRenderInfo(null);
  }
  function handleLink() {
    kwaiPilotBridgeAPI.openUrl(renderInfo.link.url);
  }
  useEffect(() => {
    getTipsConfig()
      .then((info) => {
        if (info) {
          const hiddenTips
            = getLocalStorageValue(LocalStorageKey.hiddenTips) === info.name;
          if (!hiddenTips) {
            setRenderInfo(info);
          }
        }
      })
      .catch(() => setRenderInfo(null));
  }, []);

  useEffect(() => {
    if (renderInfo && renderInfo.background && renderInfo.decorate) {
      setRenderStyle({
        "--tips-bg-color": renderInfo.background[colorMode],
        "--tips-bg-image": renderInfo.decorate[colorMode],
        "--tips-icon": renderInfo.icon[colorMode],
      });
    }
    else {
      setRenderStyle({});
    }
  }, [colorMode, renderInfo]);
  return (
    <>
      {renderInfo && (
        <div className={`tips-card tips-${colorMode}`} style={renderStyle}>
          <div className="tips-container">
            <div className="tips-icon"></div>
            <div className="tips-content">
              <div className="tips-content-text">{renderInfo.title}</div>
              <div className="tips-btn-link" onClick={handleLink}>
                {renderInfo.link?.text}
              </div>
            </div>
            <div className="tips-close" onClick={handleClose}></div>
          </div>
        </div>
      )}
    </>
  );
}
