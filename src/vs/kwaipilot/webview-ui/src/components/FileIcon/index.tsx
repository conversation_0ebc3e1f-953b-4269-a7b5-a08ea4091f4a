// FIXME: eslint

import { ext2IconName } from "@/constant";
import clsx from "clsx";

interface IProps {
  filename: string;
  size?: number;
  className?: string;
}

export const FileIcon: React.FC<IProps> = (props) => {
  const { filename, size = 16, className } = props;
  const ext = filename.split(".").pop() ?? "ts";
  const src = `https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-file-ext-icon/${
    ext2IconName[ext] ?? "typescript"
  }.svg`;

  return (
    <img className={clsx(`w-[${size}px] h-[${size}px]`, className)} src={src} />
  );
};
