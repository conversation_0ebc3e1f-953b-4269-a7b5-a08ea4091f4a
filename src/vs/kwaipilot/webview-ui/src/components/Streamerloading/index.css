@property --border-gradient-angle {
  syntax: "<angle>";
  inherits: true;
  initial-value: 0deg;
}
[data-element-type="body"] {
  --outer-radius: 8px;
  --border-size: 2px;
}

.stream-loading-container {
  padding: var(--border-size);
  height: 100%;
}
.stream-loading {
  border-radius: var(--outer-radius);
}

@keyframes buttonBorderSpin {
  0% {
    --border-gradient-angle: 0turn;
  }

  100% {
    --border-gradient-angle: 1turn;
  }
}

.stream-loading-content {
  background-color: black;
  border-radius: var(--outer-radius);
}
