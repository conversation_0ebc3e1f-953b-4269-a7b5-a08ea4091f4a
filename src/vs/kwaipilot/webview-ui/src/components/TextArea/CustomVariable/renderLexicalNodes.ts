import { $createTextNode, LexicalNode } from "lexical";
import nunjucks, { nodes } from "nunjucks";
import { $createCustomVariableNode, CustomVariableNode } from "../lexical/CustomVariableNode";
import { throwNeverError } from "@/utils/throwUnknownError";
import { BuiltInVariableType, CustomVariableItemFile, displayPathDirname } from "shared";
import { SerializedCustomVariableItem } from "shared/lib/CustomVariable/nodes";

export type CompilerSupportedBuiltInVariableLiteralType = "selection" | "currentFile" | "currentDir" | "repository" | "language";

function isRootNode(node: nodes.Node): node is nodes.Root {
  return node.typename === "Root";
}

function isOutputNode(node: nodes.Node): node is nodes.Output {
  return node.typename === "Output";
}

function isTemplateDataNode(node: nodes.Node): node is nodes.TemplateData {
  return node.typename === "TemplateData";
}

function isSymbolNode(node: nodes.Node): node is nodes.Symbol {
  return node.typename === "Symbol";
}

function isLiteralNode(node: nodes.Node): node is nodes.Literal {
  return node.typename === "Literal";
}

export interface CompileLexicalNodesContext {
  repoPath: string;
  repoName: string;
  currentFile: CustomVariableItemFile | null;
  language: string;
}

/**
 * 渲染成 lexical node
 * 2025.2.6 第一版 renderer
 * 基于 jinja2 的语法 impl: https://mozilla.github.io/nunjucks/
 * 但本期只支持 {{ var }} 的语法，其他语法需要讨论如何交互，因此暂不支持
 * 因为只支持 {{ var }} 的语法，所以没有直接使用 nunjucks 的render，而是基于 nunjucks 的 parser 进行解析
 * @param template
 */
export function $compileLexicalNodes(template: string, context: CompileLexicalNodesContext) {
  const parser = nunjucks.parser;
  const parsed = parser.parse(template, [], {});
  if (!isRootNode(parsed)) {
    throw new Error("parsed is not Root");
  }

  const result: LexicalNode[] = [];

  function compileLiteral(node: { value: string }) {
    result.push($createTextNode(String(node.value)));
  }

  function compileSymbol(node: nodes.Symbol) {
    const name = node.value as BuiltInVariableType | string;

    const varNameMap: Record<CompilerSupportedBuiltInVariableLiteralType, boolean> = {
      selection: true,
      currentFile: true,
      currentDir: true,
      repository: true,
      language: true,
    };
    let lexicalNode!: CustomVariableNode;
    if (name in varNameMap) {
      const builtInVarName = name as CompilerSupportedBuiltInVariableLiteralType;
      const defaultFile: SerializedCustomVariableItem = {
        type: "file",
        uri: "",
      };
      if (builtInVarName === "selection") {
        lexicalNode = $createCustomVariableNode({
          ...context.currentFile || defaultFile,
          type: "selection",
        });
      }
      else if (builtInVarName === "currentFile") {
        lexicalNode = $createCustomVariableNode({
          ...context.currentFile || defaultFile,
          type: "file",
        });
      }
      else if (builtInVarName === "currentDir") {
        lexicalNode = $createCustomVariableNode({
          uri: context.currentFile?.uri ? displayPathDirname(context.currentFile.uri) : "",
          type: "tree",
          isWorkspaceRoot: true,
        });
      }
      else if (builtInVarName === "repository") {
        lexicalNode = $createCustomVariableNode({
          uri: context.repoPath,
          type: "repository",
          repoID: context.repoName,
          repoName: context.repoName,
        });
      }
      else if (builtInVarName === "language") {
        lexicalNode = $createCustomVariableNode({
          uri: "",
          type: "language",
          language: context.language,
        });
      }
      else {
        throwNeverError(builtInVarName);
      }
    }
    else {
      // 自定义变量
      lexicalNode = $createCustomVariableNode({
        type: "userDefined",
        variableName: name,
        variableValue: "",
        uri: "",
      });
    }

    result.push(lexicalNode);
  }

  function compileByType(node: nodes.Node) {
    if (isOutputNode(node)) {
      compileOutput(node);
    }
    else if (isSymbolNode(node)) {
      compileSymbol(node);
    }
    else if (isLiteralNode(node)) {
      compileLiteral(node);
    }
    else {
      throw new Error(`Unsupported node type: ${node.typename}`);
    }
  }

  function compileOutput(node: nodes.Output) {
    node.children.forEach((child) => {
      // TemplateData is a special case because it is never
      // autoescaped, so simply output it for optimization
      if (isTemplateDataNode(child)) {
        if (child.value) {
          compileLiteral(child);
        }
      }
      else {
        compileByType(child);
      }
    });
  }

  for (const child of parsed.children) {
    compileByType(child);
  }

  return result;
}
/**
 * 渲染成最终文本
 * @param template
 * @param data
 */
export function renderFinalText(template: string, data: Record<string, unknown>) {
  const parser = nunjucks.parser;
  const parsed = parser.parse(template, [], {});
  if (!isRootNode(parsed)) {
    throw new Error("parsed is not Root");
  }

  const buffers: string[] = [];

  function compileLiteral(node: { value: string }) {
    buffers.push(node.value);
  }

  function contextLookup(name: string) {
    return data[name];
  }

  function compileSymbol(node: nodes.Symbol) {
    const name = node.value;

    buffers.push(contextLookup(name) ?? name);
  }

  function compileByType(node: nodes.Node) {
    if (isOutputNode(node)) {
      compileOutput(node);
    }
    else if (isSymbolNode(node)) {
      compileSymbol(node);
    }
    else {
      throw new Error(`Unsupported node type: ${node.typename}`);
    }
  }

  function compileOutput(node: nodes.Output) {
    node.children.forEach((child) => {
      // TemplateData is a special case because it is never
      // autoescaped, so simply output it for optimization
      if (isTemplateDataNode(child)) {
        if (child.value) {
          compileLiteral(child);
        }
      }
      else {
        compileByType(child);
      }
    });
  }

  for (const child of parsed.children) {
    compileByType(child);
  }

  return buffers.join("");
}
