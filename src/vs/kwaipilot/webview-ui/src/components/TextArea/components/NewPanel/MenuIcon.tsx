import { FileIcon } from "@/components/FileIcon";
import { FolderIcon } from "@/components/FolderIcon";
import { SharpCommandIcon } from "@/components/SharpCommandIcon";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { useColorMode } from "@chakra-ui/react";
import {
  CommandType,
  SharpCommand,
} from "@shared/types";
import clsx from "clsx";
import { useMemo } from "react";
import { RichEditorBoxPanelData } from "../../const";

interface IProps {
  isSecondary: boolean;
  menu: RichEditorBoxPanelData;
}

export const MenuIcon: React.FC<IProps> = (props: IProps) => {
  const { isSecondary, menu } = props;
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const panelMenuStore = useRichEditPanelMenuStore();

  const disableMenu = useMemo(() => {
    return panelMenuStore.disabledMenu;
  }, [panelMenuStore.disabledMenu]);

  if (menu.commandType === CommandType.SLASH && !isSecondary) {
    return (
      <div
        className={clsx("")}
      >
        /&nbsp;
      </div>
    );
  }
  else if (menu.commandType === CommandType.SHARP && !isSecondary) {
    return (
      <div className="mr-2">
        <SharpCommandIcon
          type={menu.key}
          status={disableMenu[menu.key]?.status ? "disabled" : "normal"}
          isDark={isDark}
        />
      </div>
    );
  }
  else if (isSecondary) {
    switch (menu.key) {
      case SharpCommand.FILE:
        return (
          <FileIcon
            filename={menu.title}
            className="mr-[6px] -translate-x-[2px]"
            size={20}
          />
        );
      case SharpCommand.FOLDER:
        return <FolderIcon isDark={isDark} className="mr-[6px]" />;
      case SharpCommand.RULES:
        return <FolderIcon isDark={isDark} className="mr-[6px]" />;
    }
  }
  return null;
};
