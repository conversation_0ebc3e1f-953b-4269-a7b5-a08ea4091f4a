import { useCallback } from "react";
import { useCollection } from "./collection";

export function useSelectableMenuItemSequence({
  includeHeader,
}: {
  /** Header 是否可以被选择 */
  includeHeader: boolean;
}) {
  const { getItems, collectionRef } = useCollection();

  type SelectableItem = ({
    originalIndex: number;
    isHeader: false;
  } | {
    isHeader: true;
  }) & {
    prev?: SelectableItem;
    next?: SelectableItem;
  };

  const constructSelectableItemDoublyLinkedList = useCallback(() => {
    if (!collectionRef.current) {
      return [];
    }

    const orderedElms = getItems();

    const linkedList: SelectableItem[] = [];
    if (includeHeader) {
      linkedList.push({ isHeader: true });
    }
    linkedList.push(...Object.entries(orderedElms)
      .filter(([, v]) => !v.disabled)
      .map<SelectableItem>(([i]) => ({ originalIndex: Number(i), isHeader: false })));
    for (let i = 0; i < linkedList.length; i++) {
      linkedList[i].prev = linkedList.at((i - 1) % linkedList.length);
      linkedList[i].next = linkedList.at((i + 1) % linkedList.length);
    }
    return linkedList;
  }, [collectionRef, getItems, includeHeader]);
  const getNext = useCallback((currentIndex: number | "header"): number | "header" => {
    const selectableItemDoublyLinkedList = constructSelectableItemDoublyLinkedList();
    if (currentIndex === -1) {
      // 返回除了 header 的第一项
      return selectableItemDoublyLinkedList.find(v => !v.isHeader)?.originalIndex ?? -1;
    }
    const next = selectableItemDoublyLinkedList.find(v =>
      (!v.isHeader && v.originalIndex === currentIndex)
      || (v.isHeader && currentIndex === "header"),
    )?.next;
    return next?.isHeader ? "header" : next?.originalIndex ?? -1;
  }, [constructSelectableItemDoublyLinkedList]);
  const getPrev = useCallback((currentIndex: number | "header"): number | "header" => {
    const selectableItemDoublyLinkedList = constructSelectableItemDoublyLinkedList();
    if (currentIndex === -1) {
      // 返回 除了 header 的最后一项
      return selectableItemDoublyLinkedList.findLast(v => !v.isHeader)?.originalIndex ?? -1;
    }
    const prev = selectableItemDoublyLinkedList.find(v =>
      (!v.isHeader && v.originalIndex === currentIndex)
      || (v.isHeader && currentIndex === "header"))?.prev;
    return prev?.isHeader ? "header" : prev?.originalIndex ?? -1;
  }, [constructSelectableItemDoublyLinkedList]);
  return {
    getNext,
    getPrev,
  };
}
