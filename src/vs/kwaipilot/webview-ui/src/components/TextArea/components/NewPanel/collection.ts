import { createContext, RefObject, useCallback, useContext } from "react";
import { RichEditorBoxPanelData } from "../../const";

export const ITEM_DATA_ATTR = "data-menu-item";

export const CollectionContext = createContext<{
  itemMap: Map<
    RefObject<HTMLElement | null>,
    { ref: RefObject<HTMLElement | null> } & {
      disabled: boolean;
      data: RichEditorBoxPanelData;
    }
  >;
  collectionRef: RefObject<HTMLElement | null>;
} | null>(null);

export function useCollection() {
  const collectionContext = useContext(CollectionContext);

  if (!collectionContext) {
    throw new Error("CollectionContext is not provided");
  }

  const getItems = useCallback(() => {
    const collectionNode = collectionContext.collectionRef.current;
    if (!collectionNode) return [];
    const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));
    const items = Array.from(collectionContext.itemMap.values());
    const orderedItems = items.sort(
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!),
    );
    return orderedItems;
  }, [collectionContext.collectionRef, collectionContext.itemMap]);

  return {
    // _ map.set 不会触发重新渲染 谨慎使用
    _itemMap: collectionContext.itemMap,
    collectionRef: collectionContext.collectionRef,
    getItems,
  };
}
