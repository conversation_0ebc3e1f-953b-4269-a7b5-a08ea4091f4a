import { ModelSelector } from "./ModelSelector";
import { createContext } from "react";
import { RepoSelector } from "./RepoSelector";
import baseInfoManager from "@/utils/baseInfo";
import { SerializedEditorState } from "lexical";

export const ShowRightTextContext = createContext(false);

export const ModelArea: React.FC<{
  richEditorState: SerializedEditorState | undefined;
}> = () => {
  return (
    <div className="w-full flex justify-between gap-6">
      <div className="flex gap-1 items-center overflow-hidden">
        <ModelSelector placement="top-start" className="py-1 px-1.5 " />
        {baseInfoManager.isXcode && (
          <>
            <RepoSelector />
          </>
        )}
      </div>
    </div>
  );
};
