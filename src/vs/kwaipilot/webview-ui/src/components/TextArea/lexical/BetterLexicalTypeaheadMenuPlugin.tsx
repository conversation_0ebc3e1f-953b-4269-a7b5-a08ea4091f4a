import { LexicalTypeaheadMenuPlugin, MenuOption as _MenuOption, TypeaheadMenuPluginProps, MenuRenderFn } from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { useCallback, useMemo, useRef } from "react";
import { useLatest } from "react-use";

export interface MenuOption extends _MenuOption {
  disabled?: boolean;
}

type ItemProps<TOption extends _MenuOption> = Parameters<MenuRenderFn<TOption>>[1];

/**
 * 原生 `LexicalTypeaheadMenuPlugin` 不支持设置禁用项， 这里添加禁用的支持，包括：
 *
 * * 支持某选项为 disabled
 *
 * @param props
 * @returns
 */
export function BetterLexicalTypeaheadMenuPlugin<TOption extends MenuOption>(
  {
    options,
    menuRenderFn: menuRenderFnProp,
    ...restProps
  }: TypeaheadMenuPluginProps<TOption>): JSX.Element | null {
  const selectableOptions = useMemo(() => options.filter(v => !v.disabled), [options]);

  const latestItemPropsFiltered = useRef<ItemProps<TOption>>();

  const latestOptions = useLatest(options);
  const latestSelectableOptions = useLatest(selectableOptions);

  const setHighlightedIndex = useCallback((realIndex: number) => {
    if (!latestOptions.current[realIndex]) {
      return;
    }
    if (latestOptions.current[realIndex].disabled) {
      // 如果当前选项被禁用，则定位到下一个可选项
      const nextSelectableI = latestOptions.current.slice(realIndex + 1).findIndex(v => !v.disabled);
      if (nextSelectableI > -1) {
        realIndex = nextSelectableI;
      }
    }
    const i = latestSelectableOptions.current.findIndex(v => v === latestOptions.current[realIndex]);
    if (i === -1) {
      return;
    }

    latestItemPropsFiltered.current?.setHighlightedIndex(i);
  }, [latestOptions, latestSelectableOptions]);

  const menuRenderFn: MenuRenderFn<TOption> = useCallback((
    anchorElementRef,
    itemPropsFiltered,
    matchingString) => {
    latestItemPropsFiltered.current = itemPropsFiltered;

    const selectedIndexFiltered = itemPropsFiltered.selectedIndex;
    const realSelected = typeof selectedIndexFiltered === "number"
      ? Object.entries(latestOptions.current).find(v => v[1] === latestSelectableOptions.current[selectedIndexFiltered])
      : null;
    const itemProps = {
      selectedIndex: realSelected ? Number(realSelected[0]) : null,
      selectOptionAndCleanUp: itemPropsFiltered.selectOptionAndCleanUp,
      setHighlightedIndex,
      options,
    };

    return menuRenderFnProp(anchorElementRef, itemProps, matchingString);
  },
  [latestOptions, latestSelectableOptions, menuRenderFnProp, options, setHighlightedIndex],
  );

  return (
    <LexicalTypeaheadMenuPlugin<TOption>
      options={selectableOptions}
      menuRenderFn={menuRenderFn}
      {...restProps}
    >
    </LexicalTypeaheadMenuPlugin>
  );
}
