import { $applyNodeReplacement, DecoratorNode, DOMConversionMap, DOMConversionOutput, DOMExportOutput, EditorConfig, LexicalEditor, LexicalNode, NodeKey } from "lexical";
import { MENTION_NODE_V2_TYPE, mentionNodeV2DisplayText, MentionNodeV2Structure, SerializedMentionNodeV2 } from "shared/lib/MentionNodeV2/nodes";
import { MENTION_NODE_V2_CLASS_NAME, MentionNodeV2Label } from "./MentionNodeV2Label";
import { DOM } from "@/utils/dom";

const DOM_DATA_ATTR = "data-lexical-mention-node-v2";

export function $createMentionNodeV2({
  structure,
}: {
  structure: MentionNodeV2Structure;
}): MentionNodeV2 {
  const node = new MentionNodeV2({ structure });
  return $applyNodeReplacement(node);
}

export function $isMentionNodeV2(node: LexicalNode | null | undefined): node is MentionNodeV2 {
  return node instanceof MentionNodeV2;
}

export class MentionNodeV2 extends DecoratorNode<JSX.Element> {
  static override getType(): typeof MENTION_NODE_V2_TYPE {
    return MENTION_NODE_V2_TYPE;
  }

  static override clone(node: MentionNodeV2): MentionNodeV2 {
    return new MentionNodeV2({
      structure: node.getStructureData(),
    }, node.__key);
  }

  __structure: MentionNodeV2Structure;

  constructor({
    structure,
  }: {
    structure: MentionNodeV2Structure;
  }, key?: NodeKey) {
    super(key);
    this.__structure = structure;
  }

  override exportJSON(): SerializedMentionNodeV2 {
    return {
      structure: {
        ...this.__structure,
      },
      type: MentionNodeV2.getType(),
      version: 1,
    };
  }

  static override importJSON(serializedNode: SerializedMentionNodeV2): MentionNodeV2 {
    return $createMentionNodeV2({
      structure: serializedNode.structure,
    });
  }

  override createDOM(): HTMLElement {
    return DOM.createElement("span");
  }

  override updateDOM(): boolean {
    return false;
  }

  override exportDOM(): DOMExportOutput {
    const element = DOM.createElement("span");
    element.setAttribute(DOM_DATA_ATTR, JSON.stringify(this.__structure));
    element.className = MENTION_NODE_V2_CLASS_NAME;
    element.textContent = this.getTextContent();
    return { element };
  }

  static override importDOM(): DOMConversionMap | null {
    return {
      span: (domNode: HTMLElement) => {
        if (!domNode.hasAttribute(DOM_DATA_ATTR)) {
          return null;
        }
        return {
          conversion: convertMentionNodeV2Element,
          priority: 1,
        };
      },
    };
  }

  override getTextContent(): string {
    return mentionNodeV2DisplayText(this.__structure);
  }

  getStructureData(): MentionNodeV2Structure {
    const self = this.getLatest();
    return self.__structure;
  }

  setStructureData(structure: MentionNodeV2Structure) {
    const self = this.getWritable();
    self.__structure = structure;
  }

  override decorate(_editor: LexicalEditor, _config: EditorConfig): JSX.Element {
    return (
      <MentionNodeV2Label
        node={this}
      />
    );
  }

  override isKeyboardSelectable(): boolean {
    return false;
  }
}

function convertMentionNodeV2Element(domNode: HTMLElement): DOMConversionOutput | null {
  const data = domNode.getAttribute(DOM_DATA_ATTR);
  if (data !== null) {
    try {
      const structure: MentionNodeV2Structure = JSON.parse(data);
      const node = $createMentionNodeV2({
        structure,
      });
      return { node };
    }
    catch (error) {
      console.error(error);
      return null;
    }
  }
  return null;
}
