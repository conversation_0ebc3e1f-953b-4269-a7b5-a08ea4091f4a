/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import {
  $applyNodeReplacement,
  type DOMExportOutput,
  type EditorConfig,
  type LexicalNode,
  TextNode,
} from "lexical";
import { RichEditorBoxPanelData, RichEditorBoxPanelDataCommon } from "../const";
import { isMentionNode as _isMentionNode, SerializedMentionNode as _SerializedMentionNode } from "shared/lib/MentionNode";
import { DOM } from "@/utils/dom";

/**
 * @deprecated use `shared/lib/MentionNode` instead
 */
export type SerializedMentionNode = _SerializedMentionNode;

// function $convertMentionElement(
//   domNode: HTMLElement,
// ): DOMConversionOutput | null {
//   const textContent = domNode.textContent;

//   if (textContent !== null) {
//     const node = $createMentionNode(textContent);
//     return {
//       node,
//     };
//   }

//   return null;
// }

export class MentionNode extends TextNode {
  __mention: string;
  __data: RichEditorBoxPanelData;
  __isDark: boolean;

  static override getType(): string {
    return "mention";
  }

  static override clone(node: MentionNode): MentionNode {
    return new MentionNode(node.__mention, node.__data, node.__isDark, node.__key);
  }

  static override importJSON(serializedNode: SerializedMentionNode): MentionNode {
    // FIXME
    const node = $createMentionNode(serializedNode.title, {
      ...serializedNode,
      type: "normal",
    } satisfies RichEditorBoxPanelDataCommon, false);
    node.setTextContent(serializedNode.text);
    node.setFormat(serializedNode.format);
    node.setDetail(serializedNode.detail);
    node.setMode(serializedNode.mode);
    node.setStyle(serializedNode.style);
    return node;
  }

  constructor(
    mentionName: string,
    data: RichEditorBoxPanelData,
    isDark: boolean,
    key?: string,
  ) {
    super(mentionName, key);
    this.__mention = mentionName;
    this.__data = data;
    this.__isDark = isDark;
  }

  exportJSON(): SerializedMentionNode {
    return {
      ...super.exportJSON(),
      type: MentionNode.getType(),
      version: 1,
      ...{
        title: this.__data.title,
        key: this.__data.key,
        description: this.__data.description,
        data: this.__data.data,
        commandType: this.__data.commandType,
        uri: this.__data.uri,
      },
    };
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config);
    dom.contentEditable = "true";
    dom.classList.add(
      "items-center",
      "rounded",
      "px-1",
      "py-[1px]",
      "text-[12px]",
      "text-editor-foreground",
      "bg-toolbar-hoverBackground",
      "border-solid",
      "border",
      "border-commandCenter-inactiveBorder",
    );

    return dom;
  }

  exportDOM(): DOMExportOutput {
    const element = DOM.createElement("span");
    element.setAttribute("data-lexical-mention", "true");
    element.textContent = this.__text;
    return { element };
  }

  // static importDOM(): DOMConversionMap | null {
  //   return {
  //     span: (domNode: HTMLElement) => {
  //       if (!domNode.hasAttribute('data-lexical-mention')) {
  //         return null;
  //       }
  //       return {
  //         conversion: $convertMentionElement,
  //         priority: 1,
  //       };
  //     },
  //   };
  // }

  isTextEntity(): true {
    return true;
  }

  canInsertTextBefore(): boolean {
    return false;
  }

  canInsertTextAfter(): boolean {
    return false;
  }
}

export function $createMentionNode(
  mentionName: string,
  data: RichEditorBoxPanelData,
  isDark: boolean,
): MentionNode {
  const mentionNode = new MentionNode(mentionName, data, isDark);
  mentionNode.setMode("token").toggleDirectionless();
  return $applyNodeReplacement(mentionNode);
}

export function $isMentionNode(
  node: LexicalNode | null | undefined,
): node is MentionNode {
  return node instanceof MentionNode;
}

/**
 * @deprecated use `shared/lib/MentionNode` instead
 */
export const isMentionNode = _isMentionNode;
