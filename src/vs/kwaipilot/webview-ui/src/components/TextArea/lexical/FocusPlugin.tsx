import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useEffect } from "react";

interface IProps {
  changeFocused: (focused: boolean) => void;
}

export const FocusPlugin: React.FC<IProps> = (props: IProps) => {
  const { changeFocused } = props;
  const [editor] = useLexicalComposerContext();
  useEffect(() => {
    const rootElement = editor.getRootElement();
    if (rootElement) {
      const handleFocus = () => {
        // setFocused(true);
        changeFocused(true);
      };
      const handleBlur = () => {
        // setFocused(false);
        changeFocused(false);
      };
      rootElement.addEventListener("focus", handleFocus);
      rootElement.addEventListener("blur", handleBlur);
      return () => {
        rootElement.removeEventListener("focus", handleFocus);
        rootElement.removeEventListener("blur", handleBlur);
      };
    }
  }, [editor, changeFocused]);
  return null;
};
