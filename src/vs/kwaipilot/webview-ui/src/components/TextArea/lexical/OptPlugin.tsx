import { DialogSetting } from "@/store/record";
import {
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";
import { RefObject, useMemo } from "react";
import { CommandPluginRef } from "@/components/TextArea";
import ReactDOM from "react-dom";
import clsx from "clsx";
import SendFlyIcon from "@/assets/icons/send-fly.svg?react";
import StopIcon from "@/assets/icons/stop-circle.svg?react";
import { useColorMode } from "@chakra-ui/react";

interface IProps {
  setting?: DialogSetting;
  onSubmit: () => unknown;
  onStop?: () => unknown;
  richEditorState?: SerializedEditorState<SerializedLexicalNode>;
  disabled: boolean;
  sharpPluginRef: RefObject<CommandPluginRef | undefined>;
  optRef: RefObject<HTMLDivElement>;
  moreOpt?: React.ReactNode;
  action?: React.ReactNode;
  focused: boolean;
  uploadFileEnabled?: boolean;
  loading?: boolean;
}

export const OptPlugin: React.FC<IProps> = (props: IProps) => {
  const {
    onSubmit,
    onStop,
    disabled,
    optRef,
    moreOpt,
    loading,
    action,
  } = props;
  const { colorMode: theme } = useColorMode();

  const isDark = theme === "dark";

  const sendBtnBg = useMemo(() => {
    if (isDark) {
      if (disabled) {
        return "bg-toolbar-activeBackground";
      }
      return "";
    }

    if (disabled) {
      return "bg-toolbar-activeBackground";
    }
    return "";
  }, [disabled, isDark]);

  const sendBtnIconColor = useMemo(() => {
    if (!isDark) {
      return "text-input-background";
    }

    if (disabled) {
      return "text-tab-inactiveForeground";
    }
    return "text-button-secondaryForeground";
  }, [disabled, isDark]);

  return (
    optRef.current
    && ReactDOM.createPortal(
      <div className="rounded-b mt-1 w-full px-3 mb-3 z-10 h-[25px] flex items-center justify-between">
        <div className="flex gap-1 items-center">
          {moreOpt}
        </div>
        <div className=" flex items-center gap-3">
          {action}
          <button
            className={clsx(
              sendBtnBg,
              sendBtnIconColor,
              "flex justify-center items-center rounded p-1",
              [disabled ? "cursor-not-allowed" : "cursor-pointer"],
              [disabled ? "" : "bg-button-background"],
            )}
            disabled={disabled}
            onClick={() => {
              if (loading) {
                onStop?.();
              }
              else {
                onSubmit();
              }
            }}
          >
            {loading ? <StopIcon /> : <SendFlyIcon></SendFlyIcon>}
          </button>
        </div>
      </div>,
      optRef.current,
    )
  );
};
