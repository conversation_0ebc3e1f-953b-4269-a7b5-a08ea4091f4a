import { Tooltip } from "@/components/Union/chakra-ui";
import { Box } from "@chakra-ui/react";

import { MentionNodeV2 } from "./MentionNodeV2";
import clsx from "clsx";
import { FilenameDisplay } from "@/logics/composer/tools/components/FilenameDisplay";
import { useMemo, useRef } from "react";
import { basename } from "path-browserify";
import { displayLineRange } from "shared";
import { MentionNodeV2Structure_SlashCommand, slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";

export const MENTION_NODE_V2_CLASS_NAME = `mention-node-v2-label`;

function MentionNodeV2Label_SlashCommand({ node }: { node: MentionNodeV2 }) {
  const structure = node.__structure as MentionNodeV2Structure_SlashCommand;
  const contextItem = structure.contextItem;
  const filename = useMemo(() => contextItem ? basename(contextItem.relativePath) : "", [contextItem]);
  const rangeRef = useRef<HTMLSpanElement>(null);
  const tooltipContent = contextItem?.relativePath || "";

  return (
    <Tooltip openDelay={1000} placement="top" label={tooltipContent} maxW="90vw" wordBreak="break-all" closeOnScroll>
      <Box as="span" className={clsx(MENTION_NODE_V2_CLASS_NAME)}>
        <Box
          as="span"
          fontFamily="var(--vscode-editor-font-family)"
          className={clsx(
            "rounded leading-[18px] py-[2px] px-1 cursor-default text-foreground bg-textPreformat-background text-[12px] hover:bg-statusBarItem-hoverBackground",
          )}
          wordBreak="break-all"
          data-type={node.__structure.type}
        >
          <span className="pl-1">
            {node.__structure.type === "slashCommand" ? "/" : "#"}
          </span>
          <span className=" pr-1">
            {node.getTextContent()}
          </span>
          {slashCommandSetRequiringContextItem.has(structure.command)
            ? contextItem?.uri.trim()
              ? (
                  <>
                    <FilenameDisplay filename={filename}></FilenameDisplay>
                    {contextItem.type === "selection" && (
                      <span ref={rangeRef} className=" flex-none  text-[12px]  text-descriptionForeground">
                        :
                        {displayLineRange(contextItem.range)}
                      </span>
                    )}
                  </>
                )
              : (
                  <span className=" text-disabledForeground">
                    请选择一段代码或一个文件
                  </span>
                )
            : undefined}
        </Box>

      </Box>
    </Tooltip>
  );
}

function MentionNodeV2Label_Default({ node }: { node: MentionNodeV2 }) {
  const tooltipContent = node.__structure.relativePath;
  return (
    <Tooltip openDelay={1000} placement="top" label={tooltipContent} maxW="90vw" wordBreak="break-all" closeOnScroll>
      <Box
        as="span"
        fontFamily="var(--vscode-editor-font-family)"
        className={clsx(MENTION_NODE_V2_CLASS_NAME,
          "rounded leading-[18px] py-[2px] cursor-default text-foreground bg-textPreformat-background text-[12px] hover:bg-statusBarItem-hoverBackground",
        )}
        wordBreak="break-all"
        data-type={node.__structure.type}
      >
        <span className="pl-1">
          {node.__structure.type === "slashCommand" ? "/" : "#"}
        </span>
        <span className=" pr-1">
          {node.getTextContent()}
        </span>
      </Box>
    </Tooltip>
  );
}

export function MentionNodeV2Label({ node }: { node: MentionNodeV2 }) {
  if (node.__structure.type === "slashCommand") {
    return <MentionNodeV2Label_SlashCommand node={node} />;
  }
  return <MentionNodeV2Label_Default node={node} />;
}
