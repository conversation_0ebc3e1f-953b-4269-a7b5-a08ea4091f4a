import { Paragraph<PERSON>ode, RootNode, SerializedEditorState, SerializedLexicalNode, SerializedParagraphNode, SerializedTextNode, TextNode } from "lexical";
import { assemblePlainTextForGpt, customVariableItemMentionNodeDisplayText } from "shared/lib/CustomVariable/nodes";
import { findFirst } from "./traversal";
import { isMentionNode, isSharpCommandMentionNode, isSlashCommandMentionNode } from "shared/lib/MentionNode";
import { isCustomVariableNode } from "shared/lib/CustomVariable";
import { mentionNodeV2DisplayText, isMentionNodeV2 } from "shared/lib/MentionNodeV2/nodes";

/**
 * editorState 转换为 供 gpt 使用的纯文本 prompt
 * @param editorState
 */
export function transformToPlainTextForGpt(editorState: SerializedEditorState) {
  let finalText = "";
  function walk(node: SerializedLexicalNode) {
    if (isCustomVariableNode(node)) {
      finalText += assemblePlainTextForGpt(node);
    }
    else if (isMentionNode(node)) {
      finalText += node.title;
    }
    else if ("text" in node) {
      finalText += node.text;
    }
    if ("children" in node && Array.isArray(node.children)) {
      for (const child of node.children) {
        walk(child);
      }
    }
  }
  walk(editorState.root);
  return finalText;
}

export function transformToPlainTextForHumanReading(editorState: SerializedEditorState): string {
  const firstSlashCommand = findFirst(editorState, node => isMentionNode(node) && isSlashCommandMentionNode(node));
  if (firstSlashCommand) {
    return `/${firstSlashCommand.title}`;
  }
  let finalText = "";
  function walk(node: SerializedLexicalNode) {
    if (isCustomVariableNode(node)) {
      finalText += customVariableItemMentionNodeDisplayText(node.contextItem);
    }
    else if (isMentionNode(node)) {
      if (isSharpCommandMentionNode(node)) {
        finalText += `\n#${node.title}\n`;
      }
      else {
        throw new Error(`Unsupported mention node ${node.commandType}`);
      }
    }
    else if (isMentionNodeV2(node)) {
      finalText += mentionNodeV2DisplayText(node.structure);
    }
    else if ("text" in node) {
      finalText += node.text;
    }
    if ("children" in node && Array.isArray(node.children)) {
      for (const child of node.children) {
        walk(child);
      }
    }
  }
  walk(editorState.root);
  return finalText;
}

// TODO: 这不是官方列举的方法,脱离 editory 创建 state 是有风险的, 需要寻找更合适的方式
/**
 * 创建一个纯文本的 editorState
 * @param plainText
 * @returns
 */
export function createPlainTextEditorState(plainText: string): SerializedEditorState {
  const state: SerializedEditorState<SerializedParagraphNode> = {
    root: {
      version: 1,
      type: RootNode.getType(),
      children: [
      {
        type: ParagraphNode.getType(),
        version: 1,
        format: "",
        children: [
          {
            type: TextNode.getType(),
            version: 1,
            detail: 0,
            text: plainText,
            format: 0,
            style: "",
            mode: "normal",
          } satisfies SerializedTextNode as any,
        ],
        direction: null,
        indent: 0,
        textFormat: 0,
        textStyle: "",
      } satisfies SerializedParagraphNode,
      ],
      direction: null,
      indent: 0,
      format: "",

    },
  };
  return state;
}
