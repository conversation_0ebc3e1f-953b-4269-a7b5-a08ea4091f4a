import { LexicalSubscription, useLexicalSubscription } from "@lexical/react/useLexicalSubscription";
import { EditorState } from "lexical";

/**
 * 获取 lexical editor state， 响应式
 */
export function useLexicalEditorState() {
  const editorState = useLexicalSubscription(editor => ({
    initialValueFn: () => editor.getEditorState(),
    subscribe: callback => editor.registerUpdateListener(({ editorState }) => callback(editorState)),
  } satisfies LexicalSubscription<EditorState>));
  return editorState;
}
