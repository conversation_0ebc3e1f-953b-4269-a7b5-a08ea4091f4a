import { createContext, Dispatch, SetStateAction, useContext } from "react";

export const RichEditorContext = createContext<{
  focused: boolean;
  setFocused: (focused: boolean) => void;
  commandShown: {
    sharpShown: boolean;
    slashShown: boolean;
  };
  setCommandShown: Dispatch<SetStateAction<{
    sharpShown: boolean;
    slashShown: boolean;
  }>>;
  mentionShown: boolean;
  setMentionShown: (mentionShown: boolean) => void;
  slashV2Shown: boolean;
  setSlashV2Shown: (slashShown: boolean) => void;
} | null>(null);

export function useRichEditorContext() {
  const ctx = useContext(RichEditorContext);

  if (!ctx) {
    throw new Error("useRichEditorContext must be used within RichEditorProvider");
  }
  return ctx;
}

export function useIsFocused() {
  const { focused } = useRichEditorContext();
  return focused;
}
