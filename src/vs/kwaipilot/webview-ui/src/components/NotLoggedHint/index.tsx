import { kwaiPilotBridgeAPI } from "@/bridge";
import { useDesignToken } from "@/hooks/useDesignToken";
import { Box, Link, BoxProps } from "@chakra-ui/react";

export function NotLoggedHint(props: BoxProps) {
  const { tokens } = useDesignToken();
  return (

    <Box p={2} pt={6} {...props}>
      Hi，我是Kwaipilot，最懂快手研发的Ai工具。立即登录账号，开启Ai编程旅途吧！
      <br />
      <Link
        textDecoration="none"
        onClick={() => kwaiPilotBridgeAPI.login()}
        target="javascript:void(0)"
        className="text-blue-400 mt-[8px] inline-block"
        color={tokens.colorTextBrandDefault}
      >
        登录Kwaipilot
      </Link>
      <br />
      <Link
        onClick={() => {
          kwaiPilotBridgeAPI.openUrl(
            "https://docs.corp.kuaishou.com/k/home/<USER>/fcAAvma1OGznlywMIiYfgHz6w",
          );
        }}
        target="javascript:void(0)"
        className="text-blue-400 mt-[8px] inline-block"
        color={tokens.colorTextBrandDefault}
        textDecoration="none"
      >
        查看Kwaipilot使用手册
      </Link>
    </Box>
  );
}
