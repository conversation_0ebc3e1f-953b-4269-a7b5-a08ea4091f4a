import { RefObject, useCallback, useLayoutEffect, useRef, useState } from "react";

import ResizeObserver from "resize-observer-polyfill";

export interface DOMRectReadOnly {
  readonly x: number;
  readonly y: number;
  readonly width: number;
  readonly height: number;
  readonly top: number;
  readonly right: number;
  readonly bottom: number;
  readonly left: number;
}

/**
 *
 * @param ref 目标元素的 ref
 * @param resizeCallback 目标元素尺寸改变以后的回调函数
 */
function useResizeObserver<T extends HTMLElement>(
  ref?: RefObject<T | null>,
  resizeCallback?: (props: DOMRectReadOnly, ref?: RefObject<T | null>) => void,
): DOMRectReadOnly {
  const observer = useRef<ResizeObserver>();

  const [rect, setRect] = useState<DOMRectReadOnly>({
    width: 0,
    height: 0,
    x: 0,
    y: 0,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  });

  const wrappedResizeCallback = useCallback(
    (props: DOMRectReadOnly) => {
      resizeCallback?.(props, ref);
    },
    [resizeCallback, ref],
  );

  const observe = useCallback(() => {
    observer.current = new ResizeObserver((args) => {
      setRect(args[0].contentRect);
      wrappedResizeCallback(args[0].contentRect);
    });
    ref?.current && observer.current.observe(ref.current);
  }, [ref, wrappedResizeCallback]);

  const disconnect = useCallback(() => {
    observer?.current?.disconnect();
  }, [observer]);

  useLayoutEffect(() => {
    observe();
    return () => {
      disconnect();
    };
  }, [observer, disconnect, observe, ref]);

  return rect;
}

export default useResizeObserver;
