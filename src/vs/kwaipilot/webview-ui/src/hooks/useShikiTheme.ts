import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { useColorMode } from "@chakra-ui/react";
import { useMemo } from "react";
import { useIdeEnv } from "./useIdeEnv";

export function useShikiTheme() {
  const { colorMode: theme } = useColorMode();
  const [ide] = useIdeEnv();
  const isDark = theme === "dark";
  const currentThemeName = useVsEditorConfig(state => state.currentThemeName);
  const shikiTheme = useMemo(() => {
    if (ide === "kwaipilot-xcode") {
      if (isDark) {
        return "kwaipilot-default-theme-dark";
      }
      else {
        return "kwaipilot-default-theme-light";
      }
    }
    else {
      return currentThemeName;
    }
  }, [currentThemeName, ide, isDark]);

  return shikiTheme;
}
