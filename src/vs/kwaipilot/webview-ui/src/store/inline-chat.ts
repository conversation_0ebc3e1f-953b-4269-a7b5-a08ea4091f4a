import { create } from "zustand";
import { v4 as uuidv4 } from "uuid";
import {
  removeLocalStorageValue,
  setLocalStorageValue,
} from "@/utils/localStorage";
import { InlineChatInfo } from "@shared/types";

export interface InlineChatItem {
  question?: string;
  answer?: string;
  loading: boolean;
}
export interface InlineChatHistory {
  quote: InlineChatInfo;
  list: InlineChatItem[];
}
export interface InlineChatStoreType {
  inlineChatHistory?: InlineChatHistory;
  setQuote: (quote: InlineChatInfo) => void;
  addQuestion: (question: string) => void;
  updateAnswer: (answer: string) => void;
  updateLoading: (loading: boolean) => void;
  clearInlineChatHistory: () => void;
}

export const useInlineChatStore = create<InlineChatStoreType>(set => ({
  inlineChatHistory: undefined,
  setQuote(quote) {
    set(() => {
      const newData = { inlineChatHistory: { quote, list: [] } };
      setLocalStorageValue(
        "inlineChatInfo",
        JSON.stringify(newData.inlineChatHistory),
      );
      setLocalStorageValue("activeSessionId", uuidv4());
      return newData;
    });
  },
  addQuestion(question) {
    set((state) => {
      if (!state.inlineChatHistory) {
        return state;
      }
      const { list, quote } = state.inlineChatHistory;
      list.push({ question, answer: "", loading: false });
      const newData = { inlineChatHistory: { quote, list } };
      setLocalStorageValue(
        "inlineChatInfo",
        JSON.stringify(newData.inlineChatHistory),
      );
      return newData;
    });
  },
  updateAnswer(answer) {
    set((state) => {
      if (!state.inlineChatHistory) {
        return state;
      }
      const { list, quote } = state.inlineChatHistory;
      const last = list[list.length - 1];
      if (last) {
        last.answer = last.answer + answer;
      }
      const newData = { inlineChatHistory: { quote, list } };
      setLocalStorageValue(
        "inlineChatInfo",
        JSON.stringify(newData.inlineChatHistory),
      );
      return newData;
    });
  },
  updateLoading(loading) {
    set((state) => {
      if (!state.inlineChatHistory) {
        return state;
      }
      const { list, quote } = state.inlineChatHistory;
      const last = list[list.length - 1];
      if (last) {
        last.loading = loading;
      }
      const newData = { inlineChatHistory: { quote, list } };
      setLocalStorageValue(
        "inlineChatInfo",
        JSON.stringify(newData.inlineChatHistory),
      );
      return newData;
    });
  },
  clearInlineChatHistory() {
    set({ inlineChatHistory: undefined });
    removeLocalStorageValue("inlineChatInfo");
    removeLocalStorageValue("activeSessionId");
  },
}));
