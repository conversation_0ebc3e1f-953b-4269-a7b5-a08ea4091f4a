import { create } from "zustand";
import path from "path-browserify";
import {
  CommandType,
  DisableRichEditorMenu,
  SharpCommand,
  SlashCommand,
} from "@shared/types";
import { RichEditorBoxPanelData, RichEditorBoxPanelDataCustomPrompt, RichEditorMenuType } from "@/components/TextArea/const";
import { RichEditorDisabledReason } from "@shared/constant";
import { CustomPromptData } from "shared/lib/CustomVariable";
import repoChatService from "@/services/repo-chat";
import { URI } from "vscode-uri";
import { isRuleFile } from "shared/lib/util";
import { Doc } from "shared/lib/business";

export interface RichEditorMenu {
  disabledMenu: DisableRichEditorMenu;
  sharpCommand: RichEditorBoxPanelData[];
  codeSearchDefaultFiles: RichEditorBoxPanelData[];
  codeSearchDefaultDir: RichEditorBoxPanelData[];
  codeSearchWorkspaceFiles: RichEditorBoxPanelData[];
  codeSearchWorkspaceDir: RichEditorBoxPanelData[];
  customPrompts: RichEditorBoxPanelDataCustomPrompt[];
  ruleFiles: RichEditorBoxPanelData[];
  setDisabledMenu: (params: DisableRichEditorMenu) => void;
  setCodeSearchDefaultFiles: (files: string[]) => void;
  setCodeSearchDefaultDir: (files: string[]) => void;
  setCodeSearchWorkspaceFiles: (files: string[]) => void;
  setCodeSearchWorkspaceDir: (files: string[]) => void;
  updateCurrentFilePath: (files: string) => void;
  updateCodeBasePath: (files: string) => void;
  setCustomPrompts: (prompts: CustomPromptData[]) => void;
  setRuleFiles: (files: string[]) => void;
  docList: Doc[];
  setDocList: (value: Doc[]) => void;
}

const filterPath = [".", "/"];

export const useRichEditPanelMenuStore = create<RichEditorMenu>((set, get) => {
  return {
    disabledMenu: {
      [SharpCommand.CURRENT_FILE]: {
        status: true,
        msg: RichEditorDisabledReason.unopenedFile,
      },
      [SharpCommand.FOLDER]: {
        status: false,
        msg: "",
      },
      [SharpCommand.FILE]: {
        status: true,
        msg: "",
      },
      [SharpCommand.CODEBASE]: {
        status: false,
        msg: "",
      },
      [SharpCommand.RULES]: {
        status: false,
        msg: "",
      },
      [SlashCommand.LINE_CODE_COMMENT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CODE_EXPLAIN]: {
        status: true,
        msg: "",
      },
      [SlashCommand.FUNC_COMMENT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CODE_REFACTOR]: {
        status: true,
        msg: "",
      },
      [SlashCommand.UNIT_TEST]: {
        status: true,
        msg: "",
      },
      [SlashCommand.FUNC_SPLIT]: {
        status: true,
        msg: "",
      },
      [SlashCommand.CLEAR_CONVERSATION]: {
        status: false,
        msg: "",
      },
    },
    sharpCommand: [
      {
        key: SharpCommand.CURRENT_FILE,
        title: "当前文件",
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.CODEBASE,
        title: "代码库",
        description: "",
        type: "normal",
        data: "",
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.FOLDER,
        title: "目录",
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.FOLDER,
        data: SharpCommand.FOLDER,
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.FILE,
        title: "文件",
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.FILE,
        data: SharpCommand.FOLDER,
        commandType: CommandType.SHARP,
        uri: "",
      },
      {
        key: SharpCommand.RULES,
        title: "规则",
        description: "",
        type: "submenu",
        submenu: RichEditorMenuType.RULES,
        data: SharpCommand.RULES,
        commandType: CommandType.SHARP,
        uri: "",
      },
    ],
    codeSearchDefaultFiles: [],
    codeSearchDefaultDir: [],
    codeSearchWorkspaceFiles: [],
    codeSearchWorkspaceDir: [],
    customPrompts: [],
    ruleFiles: [],

    setDisabledMenu(params: DisableRichEditorMenu) {
      set({
        disabledMenu: {
          ...get().disabledMenu,
          ...params,
        },
      });
    },
    setCodeSearchDefaultFiles(files: string[]) {
      files = files.filter(f => !filterPath.includes(f) && !isRuleFile(f));
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FILE,
          type: "normal",
          data: file,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
          commandType: CommandType.SHARP,
        } satisfies RichEditorBoxPanelData;
      });
      set({
        codeSearchDefaultFiles: data,
      });
    },
    setCodeSearchDefaultDir(dir: string[]) {
      dir = dir.filter(f => !filterPath.includes(f));
      const data: RichEditorBoxPanelData[] = dir.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FOLDER,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        codeSearchDefaultDir: data,
      });
    },
    setCodeSearchWorkspaceFiles(files: string[]) {
      files = files.filter(f => !filterPath.includes(f));
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FILE,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        codeSearchWorkspaceFiles: data,
      });
    },
    setCodeSearchWorkspaceDir(dir: string[]) {
      dir = dir.filter(f => !filterPath.includes(f));
      const data: RichEditorBoxPanelData[] = dir.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.FOLDER,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        codeSearchWorkspaceDir: data,
      });
    },

    updateCurrentFilePath(path: string) {
      const sharpCommand = get().sharpCommand;
      const current = sharpCommand.find(
        item => item.key === SharpCommand.CURRENT_FILE,
      );
      if (current) {
        current.data = path;
        set({
          sharpCommand,
        });
      }
    },
    updateCodeBasePath(path: string) {
      const sharpCommand = get().sharpCommand;
      const current = sharpCommand.find(
        item => item.key === SharpCommand.CODEBASE,
      );
      if (current) {
        current.data = path;
        set({
          sharpCommand,
        });
      }
    },
    setCustomPrompts(prompts: CustomPromptData[]) {
      set({
        customPrompts: prompts.map<RichEditorBoxPanelDataCustomPrompt>(i => ({
          key: SlashCommand.CUSTOM_PROMPT,
          title: i.name,
          description: i.content,
          type: "customPrompt",
          data: i.content,
          commandType: CommandType.SLASH,
          raw: i,
          uri: "",
        })),
      });
    },
    setRuleFiles(files: string[]) {
      const data: RichEditorBoxPanelData[] = files.map((file) => {
        return {
          title: path.basename(file),
          description: file,
          key: SharpCommand.RULES,
          type: "normal",
          data: file,
          commandType: CommandType.SHARP,
          uri: URI.file(repoChatService.getAbsolutePath(file)).toString(),
        };
      });
      set({
        ruleFiles: data,
      });
    },
    docList: [],
    setDocList: (value: Doc[]) => {
      set({
        docList: value,
      });
    },
  };
});
