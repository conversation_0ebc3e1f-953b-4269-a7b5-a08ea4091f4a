/**
 * 由于在xcode中，多个webview共享同一个进程，所以需要通过用户操作来切换问答的仓库
 */

import { create } from "zustand";

interface WorkspaceState {
  activeRepo: string;
  setActiveRepo: (repo: string) => void;
  allRepos: string[];
  setAllRepos: (repos: string[]) => void;
}

export const useWorkspaceStore = create<WorkspaceState>(set => ({
  activeRepo: "",
  setActiveRepo: repo => set({ activeRepo: repo }),
  allRepos: [],
  setAllRepos: repos => set({ allRepos: repos }),
}));
