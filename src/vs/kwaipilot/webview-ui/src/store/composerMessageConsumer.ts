import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { create } from "zustand";

// TODO: 使用类似 @open-draft/deferred-promise 库支持 result
// eslint-disable-next-line @typescript-eslint/no-empty-object-type, @typescript-eslint/no-unused-vars
interface DeferredPromise<T> extends Promise<T> {

}

export interface MessageState<T> {
  message: T;
}

export interface ComposerMessageConsumerState {
  toBeAddedComposerContext: MessageState<MentionNodeV2Structure>[];
  produceComposerContext(node: MentionNodeV2Structure): void;
  consumeComposerContext(message: MessageState<MentionNodeV2Structure>): void;
}

/**
 * ComposerMessageConsumer
 *
 * 从 bridge 到 webview-ui 的消息处理队列.
 *
 * webview 中的组件不一定处于 active 状态, 因此发送来的消息不一定被处理
 *
 * TODO:
 *
 * * 支持返回值
 * * 锁机制
 * * 统一生产消费 method
 */
export const useComposerMessageConsumer = create<ComposerMessageConsumerState>(set => ({
  toBeAddedComposerContext: [],
  produceComposerContext(node) {
    set(state => ({
      toBeAddedComposerContext: [...state.toBeAddedComposerContext, { message: node }],
    }));
  },
  consumeComposerContext(message) {
    set((state) => {
      const x = state.toBeAddedComposerContext.filter(
        item => item !== message,
      );
      return {
        toBeAddedComposerContext: x,
      };
    });
  },
}));
