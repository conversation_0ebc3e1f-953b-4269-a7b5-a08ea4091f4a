/*
禁用 tailwin preflight 手动引入一部分必要的 reset
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: theme("borderColor.DEFAULT", currentColor); /* 2 */
}

::before,
::after {
  --tw-content: "";
}

@tailwind base;
@tailwind components;
@tailwind utilities;

[data-element-type="body"] {
  padding: 0;
  --custom-menu-trigger-bg: var(--vscode-textPreformat-background);
}
[data-element-type="body"].vscode-dark {
  background: #050e18 !important;
  --custom-text-common: #f8f8f8;
}
[data-element-type="body"].vscode-light {
  background: linear-gradient(170deg, #fcfdfd 0%, #edf3fd 72.59%) !important;
  --custom-text-common: #000;
}

[data-vscode-theme-kind="vscode-high-contrast-light"],
[data-vscode-theme-kind="vscode-high-contrast"] {
  --custom-menu-trigger-bg: var(--vscode-list-hoverBackground);
}

.main-text-dark {
  color: white;
}
.main-text-light {
  color: #262a2f;
}
.sub-text-dark {
  color: #b4bcd0;
}
.sub-text-light {
  color: #676d75;
}
.main-card-bg-dark {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.07) 100%
  );
}
.main-card-bg-light {
  border: 1px solid #fff;
  background: rgba(255, 255, 255, 0.7);
}

.tag-bg-dark {
  background: rgba(255, 255, 255, 0.08);
}
.tag-bg-light {
  background: #f5f6f8;
}

.icon-fill-dark {
  fill: white;
}
.icon-fill-light {
  fill: #262a2f;
}
.icon-avatar-fill-dark {
  fill: #8b949e;
}
.icon-avatar-fill-light {
  fill: #8b949e;
}
.icon-sub-fill-dark {
  fill: white;
}
.icon-sub-fill-light {
  fill: #676d75;
}
.icon-edit-fill-dark {
  fill: #8b949e;
}
.icon-edit-fill-light {
  fill: #676d75;
}

.blue-text-dark {
  color: #5aa7ff;
}

.blue-text-light {
  color: #326bfb;
}

.icon-fill-blue-dark {
  fill: #5aa7ff;
}

.icon-fill-blue-light {
  fill: #326bfb;
}

.link-text-dark {
  color: #ffffff;
}

.link-text-dark a {
  color: #5aa7ff;
  cursor: pointer;
}

.link-text-light {
  color: #212429;
}

.link-text-light a {
  color: #326bfb;
  cursor: pointer;
}

.upload-card-bg-dark {
  background: #2f373f;
  color: #fff;
}
.upload-card-bg-light {
  background: #fbfcfd;
  color: #262a2f;
}

/* 滚动条样式 */
.no-scrollbar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
.custom-scrollbar-dark::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.custom-scrollbar-dark::-webkit-scrollbar-track {
  background: rgba(#0f161b, 0.1);
  border-radius: 6px;
}
.custom-scrollbar-dark::-webkit-scrollbar-thumb {
  background: #0f161b;
  border-radius: 6px;
}
.custom-scrollbar-dark::-webkit-scrollbar-thumb:hover {
  background: #0f161b;
}

.custom-scrollbar-light::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar-light::-webkit-scrollbar-track {
  background: rgba(#d0d7de, 0.1);
  border-radius: 6cm;
}
.custom-scrollbar-light::-webkit-scrollbar-thumb {
  background: #d0d7de;
  border-radius: 6px;
}
.custom-scrollbar-light::-webkit-scrollbar-thumb:hover {
  background: #898a8c;
}

.kwaipilot-rich-editor-menu:hover .kwaipilot-rich-editor-menu-description {
  display: flex;
}
