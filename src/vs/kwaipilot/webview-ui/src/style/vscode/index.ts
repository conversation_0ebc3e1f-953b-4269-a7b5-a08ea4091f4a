import * as _vsVarNames from "./vscode-identifiers";
import { mapValues } from "lodash-es";

/**
 * vsVarNames 获取某个变量的名称 例如 `vsVarNames.actionBarToggledBackground` = `--vscode-actionBar-toggledBackground`
 */
export const vsVarNames = _vsVarNames;

/**
 * vsCss 获取某个变量的使用, 例如:
 *
 *  `vsCss.textPreformatBackground` === `var(--vscode-textPreformat-background)`
 *
 * 直接传入 chakra-ui 的 box 中:👇🏻
 *
 * ```jsx
 * <Box bg={vsCss.textPreformatBackground}></Box>
 * ```
 */
export const vsCss: Record<keyof typeof vsVarNames, string> = mapValues(vsVarNames, varName => asCssVariable(varName));

export function asCssVariable(varName: string): string {
  return `var(${varName})`;
}
