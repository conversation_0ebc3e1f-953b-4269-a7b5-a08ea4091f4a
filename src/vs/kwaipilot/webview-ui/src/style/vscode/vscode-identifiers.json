{"actionBar-toggledBackground": "var(--vscode-actionBar-toggledBackground)", "activityBar-activeBackground": "var(--vscode-activityBar-activeBackground)", "activityBar-activeBorder": "var(--vscode-activityBar-activeBorder)", "activityBar-activeFocusBorder": "var(--vscode-activityBar-activeFocusBorder)", "activityBar-background": "var(--vscode-activityBar-background)", "activityBar-border": "var(--vscode-activityBar-border)", "activityBar-dropBorder": "var(--vscode-activityBar-dropBorder)", "activityBar-foreground": "var(--vscode-activityBar-foreground)", "activityBar-inactiveForeground": "var(--vscode-activityBar-inactiveForeground)", "activityBarBadge-background": "var(--vscode-activityBarBadge-background)", "activityBarBadge-foreground": "var(--vscode-activityBarBadge-foreground)", "activityBarTop-activeBackground": "var(--vscode-activityBarTop-activeBackground)", "activityBarTop-activeBorder": "var(--vscode-activityBarTop-activeBorder)", "activityBarTop-background": "var(--vscode-activityBarTop-background)", "activityBarTop-dropBorder": "var(--vscode-activityBarTop-dropBorder)", "activityBarTop-foreground": "var(--vscode-activityBarTop-foreground)", "activityBarTop-inactiveForeground": "var(--vscode-activityBarTop-inactiveForeground)", "activityErrorBadge-background": "var(--vscode-activityErrorBadge-background)", "activityErrorBadge-foreground": "var(--vscode-activityErrorBadge-foreground)", "activityWarningBadge-background": "var(--vscode-activityWarningBadge-background)", "activityWarningBadge-foreground": "var(--vscode-activityWarningBadge-foreground)", "badge-background": "var(--vscode-badge-background)", "badge-foreground": "var(--vscode-badge-foreground)", "banner-background": "var(--vscode-banner-background)", "banner-foreground": "var(--vscode-banner-foreground)", "banner-iconForeground": "var(--vscode-banner-iconForeground)", "breadcrumb-activeSelectionForeground": "var(--vscode-breadcrumb-activeSelectionForeground)", "breadcrumb-background": "var(--vscode-breadcrumb-background)", "breadcrumb-focusForeground": "var(--vscode-breadcrumb-focusForeground)", "breadcrumb-foreground": "var(--vscode-breadcrumb-foreground)", "breadcrumbPicker-background": "var(--vscode-breadcrumbPicker-background)", "button-background": "var(--vscode-button-background)", "button-border": "var(--vscode-button-border)", "button-foreground": "var(--vscode-button-foreground)", "button-hoverBackground": "var(--vscode-button-hoverBackground)", "button-secondaryBackground": "var(--vscode-button-secondaryBackground)", "button-secondaryForeground": "var(--vscode-button-secondaryForeground)", "button-secondaryHoverBackground": "var(--vscode-button-secondaryHoverBackground)", "button-separator": "var(--vscode-button-separator)", "chart-axis": "var(--vscode-chart-axis)", "chart-guide": "var(--vscode-chart-guide)", "chart-line": "var(--vscode-chart-line)", "charts-blue": "var(--vscode-charts-blue)", "charts-foreground": "var(--vscode-charts-foreground)", "charts-green": "var(--vscode-charts-green)", "charts-lines": "var(--vscode-charts-lines)", "charts-orange": "var(--vscode-charts-orange)", "charts-purple": "var(--vscode-charts-purple)", "charts-red": "var(--vscode-charts-red)", "charts-yellow": "var(--vscode-charts-yellow)", "chat-avatarBackground": "var(--vscode-chat-avatarBackground)", "chat-avatarForeground": "var(--vscode-chat-avatarForeground)", "chat-editedFileForeground": "var(--vscode-chat-editedFileForeground)", "chat-requestBackground": "var(--vscode-chat-requestBackground)", "chat-requestBorder": "var(--vscode-chat-requestBorder)", "chat-slashCommandBackground": "var(--vscode-chat-slashCommandBackground)", "chat-slashCommandForeground": "var(--vscode-chat-slashCommandForeground)", "checkbox-background": "var(--vscode-checkbox-background)", "checkbox-border": "var(--vscode-checkbox-border)", "checkbox-foreground": "var(--vscode-checkbox-foreground)", "checkbox-selectBackground": "var(--vscode-checkbox-selectBackground)", "checkbox-selectBorder": "var(--vscode-checkbox-selectBorder)", "commandCenter-activeBackground": "var(--vscode-commandCenter-activeBackground)", "commandCenter-activeBorder": "var(--vscode-commandCenter-activeBorder)", "commandCenter-activeForeground": "var(--vscode-commandCenter-activeForeground)", "commandCenter-background": "var(--vscode-commandCenter-background)", "commandCenter-border": "var(--vscode-commandCenter-border)", "commandCenter-debuggingBackground": "var(--vscode-commandCenter-debuggingBackground)", "commandCenter-foreground": "var(--vscode-commandCenter-foreground)", "commandCenter-inactiveBorder": "var(--vscode-commandCenter-inactiveBorder)", "commandCenter-inactiveForeground": "var(--vscode-commandCenter-inactiveForeground)", "commentsView-resolvedIcon": "var(--vscode-commentsView-resolvedIcon)", "commentsView-unresolvedIcon": "var(--vscode-commentsView-unresolvedIcon)", "contrastActiveBorder": "var(--vscode-contrastActiveBorder)", "contrastBorder": "var(--vscode-contrastBorder)", "debugConsole-errorForeground": "var(--vscode-debugConsole-errorForeground)", "debugConsole-infoForeground": "var(--vscode-debugConsole-infoForeground)", "debugConsole-sourceForeground": "var(--vscode-debugConsole-sourceForeground)", "debugConsole-warningForeground": "var(--vscode-debugConsole-warningForeground)", "debugConsoleInputIcon-foreground": "var(--vscode-debugConsoleInputIcon-foreground)", "debugExceptionWidget-background": "var(--vscode-debugExceptionWidget-background)", "debugExceptionWidget-border": "var(--vscode-debugExceptionWidget-border)", "debugIcon-breakpointCurrentStackframeForeground": "var(--vscode-debugIcon-breakpointCurrentStackframeForeground)", "debugIcon-breakpointDisabledForeground": "var(--vscode-debugIcon-breakpointDisabledForeground)", "debugIcon-breakpointForeground": "var(--vscode-debugIcon-breakpointForeground)", "debugIcon-breakpointStackframeForeground": "var(--vscode-debugIcon-breakpointStackframeForeground)", "debugIcon-breakpointUnverifiedForeground": "var(--vscode-debugIcon-breakpointUnverifiedForeground)", "debugIcon-continueForeground": "var(--vscode-debugIcon-continueForeground)", "debugIcon-disconnectForeground": "var(--vscode-debugIcon-disconnectForeground)", "debugIcon-pauseForeground": "var(--vscode-debugIcon-pauseForeground)", "debugIcon-restartForeground": "var(--vscode-debugIcon-restartForeground)", "debugIcon-startForeground": "var(--vscode-debugIcon-startForeground)", "debugIcon-stepBackForeground": "var(--vscode-debugIcon-stepBackForeground)", "debugIcon-stepIntoForeground": "var(--vscode-debugIcon-stepIntoForeground)", "debugIcon-stepOutForeground": "var(--vscode-debugIcon-stepOutForeground)", "debugIcon-stepOverForeground": "var(--vscode-debugIcon-stepOverForeground)", "debugIcon-stopForeground": "var(--vscode-debugIcon-stopForeground)", "debugTokenExpression-boolean": "var(--vscode-debugTokenExpression-boolean)", "debugTokenExpression-error": "var(--vscode-debugTokenExpression-error)", "debugTokenExpression-name": "var(--vscode-debugTokenExpression-name)", "debugTokenExpression-number": "var(--vscode-debugTokenExpression-number)", "debugTokenExpression-string": "var(--vscode-debugTokenExpression-string)", "debugTokenExpression-type": "var(--vscode-debugTokenExpression-type)", "debugTokenExpression-value": "var(--vscode-debugTokenExpression-value)", "debugToolBar-background": "var(--vscode-debugToolBar-background)", "debugToolBar-border": "var(--vscode-debugToolBar-border)", "debugView-exceptionLabelBackground": "var(--vscode-debugView-exceptionLabelBackground)", "debugView-exceptionLabelForeground": "var(--vscode-debugView-exceptionLabelForeground)", "debugView-stateLabelBackground": "var(--vscode-debugView-stateLabelBackground)", "debugView-stateLabelForeground": "var(--vscode-debugView-stateLabelForeground)", "debugView-valueChangedHighlight": "var(--vscode-debugView-valueChangedHighlight)", "descriptionForeground": "var(--vscode-descriptionForeground)", "diffEditor-border": "var(--vscode-diffEditor-border)", "diffEditor-diagonalFill": "var(--vscode-diffEditor-diagonalFill)", "diffEditor-insertedLineBackground": "var(--vscode-diffEditor-insertedLineBackground)", "diffEditor-insertedTextBackground": "var(--vscode-diffEditor-insertedTextBackground)", "diffEditor-insertedTextBorder": "var(--vscode-diffEditor-insertedTextBorder)", "diffEditor-move-border": "var(--vscode-diffEditor-move-border)", "diffEditor-moveActive-border": "var(--vscode-diffEditor-moveActive-border)", "diffEditor-removedLineBackground": "var(--vscode-diffEditor-removedLineBackground)", "diffEditor-removedTextBackground": "var(--vscode-diffEditor-removedTextBackground)", "diffEditor-removedTextBorder": "var(--vscode-diffEditor-removedTextBorder)", "diffEditor-unchangedCodeBackground": "var(--vscode-diffEditor-unchangedCodeBackground)", "diffEditor-unchangedRegionBackground": "var(--vscode-diffEditor-unchangedRegionBackground)", "diffEditor-unchangedRegionForeground": "var(--vscode-diffEditor-unchangedRegionForeground)", "diffEditor-unchangedRegionShadow": "var(--vscode-diffEditor-unchangedRegionShadow)", "diffEditorGutter-insertedLineBackground": "var(--vscode-diffEditorGutter-insertedLineBackground)", "diffEditorGutter-removedLineBackground": "var(--vscode-diffEditorGutter-removedLineBackground)", "diffEditorOverview-insertedForeground": "var(--vscode-diffEditorOverview-insertedForeground)", "diffEditorOverview-removedForeground": "var(--vscode-diffEditorOverview-removedForeground)", "disabledForeground": "var(--vscode-disabledForeground)", "dropdown-background": "var(--vscode-dropdown-background)", "dropdown-border": "var(--vscode-dropdown-border)", "dropdown-foreground": "var(--vscode-dropdown-foreground)", "dropdown-listBackground": "var(--vscode-dropdown-listBackground)", "editor-background": "var(--vscode-editor-background)", "editor-compositionBorder": "var(--vscode-editor-compositionBorder)", "editor-findMatchBackground": "var(--vscode-editor-findMatchBackground)", "editor-findMatchBorder": "var(--vscode-editor-findMatchBorder)", "editor-findMatchForeground": "var(--vscode-editor-findMatchForeground)", "editor-findMatchHighlightBackground": "var(--vscode-editor-findMatchHighlightBackground)", "editor-findMatchHighlightBorder": "var(--vscode-editor-findMatchHighlightBorder)", "editor-findMatchHighlightForeground": "var(--vscode-editor-findMatchHighlightForeground)", "editor-findRangeHighlightBackground": "var(--vscode-editor-findRangeHighlightBackground)", "editor-findRangeHighlightBorder": "var(--vscode-editor-findRangeHighlightBorder)", "editor-focusedStackFrameHighlightBackground": "var(--vscode-editor-focusedStackFrameHighlightBackground)", "editor-foldBackground": "var(--vscode-editor-foldBackground)", "editor-foldPlaceholderForeground": "var(--vscode-editor-foldPlaceholderForeground)", "editor-foreground": "var(--vscode-editor-foreground)", "editor-hoverHighlightBackground": "var(--vscode-editor-hoverHighlightBackground)", "editor-inactiveSelectionBackground": "var(--vscode-editor-inactiveSelectionBackground)", "editor-inlineValuesBackground": "var(--vscode-editor-inlineValuesBackground)", "editor-inlineValuesForeground": "var(--vscode-editor-inlineValuesForeground)", "editor-lineHighlightBackground": "var(--vscode-editor-lineHighlightBackground)", "editor-lineHighlightBorder": "var(--vscode-editor-lineHighlightBorder)", "editor-linkedEditingBackground": "var(--vscode-editor-linkedEditingBackground)", "editor-placeholder-foreground": "var(--vscode-editor-placeholder-foreground)", "editor-rangeHighlightBackground": "var(--vscode-editor-rangeHighlightBackground)", "editor-rangeHighlightBorder": "var(--vscode-editor-rangeHighlightBorder)", "editor-selectionBackground": "var(--vscode-editor-selectionBackground)", "editor-selectionForeground": "var(--vscode-editor-selectionForeground)", "editor-selectionHighlightBackground": "var(--vscode-editor-selectionHighlightBackground)", "editor-selectionHighlightBorder": "var(--vscode-editor-selectionHighlightBorder)", "editor-snippetFinalTabstopHighlightBackground": "var(--vscode-editor-snippetFinalTabstopHighlightBackground)", "editor-snippetFinalTabstopHighlightBorder": "var(--vscode-editor-snippetFinalTabstopHighlightBorder)", "editor-snippetTabstopHighlightBackground": "var(--vscode-editor-snippetTabstopHighlightBackground)", "editor-snippetTabstopHighlightBorder": "var(--vscode-editor-snippetTabstopHighlightBorder)", "editor-stackFrameHighlightBackground": "var(--vscode-editor-stackFrameHighlightBackground)", "editor-symbolHighlightBackground": "var(--vscode-editor-symbolHighlightBackground)", "editor-symbolHighlightBorder": "var(--vscode-editor-symbolHighlightBorder)", "editor-wordHighlightBackground": "var(--vscode-editor-wordHighlightBackground)", "editor-wordHighlightBorder": "var(--vscode-editor-wordHighlightBorder)", "editor-wordHighlightStrongBackground": "var(--vscode-editor-wordHighlightStrongBackground)", "editor-wordHighlightStrongBorder": "var(--vscode-editor-wordHighlightStrongBorder)", "editor-wordHighlightTextBackground": "var(--vscode-editor-wordHighlightTextBackground)", "editor-wordHighlightTextBorder": "var(--vscode-editor-wordHighlightTextBorder)", "editorActionList-background": "var(--vscode-editorActionList-background)", "editorActionList-focusBackground": "var(--vscode-editorActionList-focusBackground)", "editorActionList-focusForeground": "var(--vscode-editorActionList-focusForeground)", "editorActionList-foreground": "var(--vscode-editorActionList-foreground)", "editorActiveLineNumber-foreground": "var(--vscode-editorActiveLineNumber-foreground)", "editorBracketHighlight-foreground1": "var(--vscode-editorBracketHighlight-foreground1)", "editorBracketHighlight-foreground2": "var(--vscode-editorBracketHighlight-foreground2)", "editorBracketHighlight-foreground3": "var(--vscode-editorBracketHighlight-foreground3)", "editorBracketHighlight-foreground4": "var(--vscode-editorBracketHighlight-foreground4)", "editorBracketHighlight-foreground5": "var(--vscode-editorBracketHighlight-foreground5)", "editorBracketHighlight-foreground6": "var(--vscode-editorBracketHighlight-foreground6)", "editorBracketHighlight-unexpectedBracket-foreground": "var(--vscode-editorBracketHighlight-unexpectedBracket-foreground)", "editorBracketMatch-background": "var(--vscode-editorBracketMatch-background)", "editorBracketMatch-border": "var(--vscode-editorBracketMatch-border)", "editorBracketPairGuide-activeBackground1": "var(--vscode-editorBracketPairGuide-activeBackground1)", "editorBracketPairGuide-activeBackground2": "var(--vscode-editorBracketPairGuide-activeBackground2)", "editorBracketPairGuide-activeBackground3": "var(--vscode-editorBracketPairGuide-activeBackground3)", "editorBracketPairGuide-activeBackground4": "var(--vscode-editorBracketPairGuide-activeBackground4)", "editorBracketPairGuide-activeBackground5": "var(--vscode-editorBracketPairGuide-activeBackground5)", "editorBracketPairGuide-activeBackground6": "var(--vscode-editorBracketPairGuide-activeBackground6)", "editorBracketPairGuide-background1": "var(--vscode-editorBracketPairGuide-background1)", "editorBracketPairGuide-background2": "var(--vscode-editorBracketPairGuide-background2)", "editorBracketPairGuide-background3": "var(--vscode-editorBracketPairGuide-background3)", "editorBracketPairGuide-background4": "var(--vscode-editorBracketPairGuide-background4)", "editorBracketPairGuide-background5": "var(--vscode-editorBracketPairGuide-background5)", "editorBracketPairGuide-background6": "var(--vscode-editorBracketPairGuide-background6)", "editorCodeLens-foreground": "var(--vscode-editorCodeLens-foreground)", "editorCommentsWidget-rangeActiveBackground": "var(--vscode-editorCommentsWidget-rangeActiveBackground)", "editorCommentsWidget-rangeBackground": "var(--vscode-editorCommentsWidget-rangeBackground)", "editorCommentsWidget-replyInputBackground": "var(--vscode-editorCommentsWidget-replyInputBackground)", "editorCommentsWidget-resolvedBorder": "var(--vscode-editorCommentsWidget-resolvedBorder)", "editorCommentsWidget-unresolvedBorder": "var(--vscode-editorCommentsWidget-unresolvedBorder)", "editorCursor-background": "var(--vscode-editorCursor-background)", "editorCursor-foreground": "var(--vscode-editorCursor-foreground)", "editorError-background": "var(--vscode-editorError-background)", "editorError-border": "var(--vscode-editorError-border)", "editorError-foreground": "var(--vscode-editorError-foreground)", "editorGhostText-background": "var(--vscode-editorGhostText-background)", "editorGhostText-border": "var(--vscode-editorGhostText-border)", "editorGhostText-foreground": "var(--vscode-editorGhostText-foreground)", "editorGroup-border": "var(--vscode-editorGroup-border)", "editorGroup-dropBackground": "var(--vscode-editorGroup-dropBackground)", "editorGroup-dropIntoPromptBackground": "var(--vscode-editorGroup-dropIntoPromptBackground)", "editorGroup-dropIntoPromptBorder": "var(--vscode-editorGroup-dropIntoPromptBorder)", "editorGroup-dropIntoPromptForeground": "var(--vscode-editorGroup-dropIntoPromptForeground)", "editorGroup-emptyBackground": "var(--vscode-editorGroup-emptyBackground)", "editorGroup-focusedEmptyBorder": "var(--vscode-editorGroup-focusedEmptyBorder)", "editorGroupHeader-border": "var(--vscode-editorGroupHeader-border)", "editorGroupHeader-noTabsBackground": "var(--vscode-editorGroupHeader-noTabsBackground)", "editorGroupHeader-tabsBackground": "var(--vscode-editorGroupHeader-tabsBackground)", "editorGroupHeader-tabsBorder": "var(--vscode-editorGroupHeader-tabsBorder)", "editorGutter-addedBackground": "var(--vscode-editorGutter-addedBackground)", "editorGutter-background": "var(--vscode-editor<PERSON><PERSON>-background)", "editorGutter-commentGlyphForeground": "var(--vscode-editorGutter-commentGlyphForeground)", "editorGutter-commentRangeForeground": "var(--vscode-editor<PERSON><PERSON>-commentRangeForeground)", "editorGutter-commentUnresolvedGlyphForeground": "var(--vscode-editorGutter-commentUnresolvedGlyphForeground)", "editorGutter-deletedBackground": "var(--vscode-editor<PERSON><PERSON>-deletedBackground)", "editorGutter-foldingControlForeground": "var(--vscode-editor<PERSON><PERSON>-foldingControlForeground)", "editorGutter-itemBackground": "var(--vscode-editorGutter-itemBackground)", "editorGutter-itemGlyphForeground": "var(--vscode-editorGutter-itemGlyphForeground)", "editorGutter-modifiedBackground": "var(--vscode-editor<PERSON><PERSON>-modifiedBackground)", "editorHint-border": "var(--vscode-editorHint-border)", "editorHint-foreground": "var(--vscode-editorHint-foreground)", "editorHoverWidget-background": "var(--vscode-editorHoverWidget-background)", "editorHoverWidget-border": "var(--vscode-editorHoverWidget-border)", "editorHoverWidget-foreground": "var(--vscode-editorHoverWidget-foreground)", "editorHoverWidget-highlightForeground": "var(--vscode-editorHoverWidget-highlightForeground)", "editorHoverWidget-statusBarBackground": "var(--vscode-editorHoverWidget-statusBarBackground)", "editorIndentGuide-activeBackground": "var(--vscode-editorIndentGuide-activeBackground)", "editorIndentGuide-activeBackground1": "var(--vscode-editorIndentGuide-activeBackground1)", "editorIndentGuide-activeBackground2": "var(--vscode-editorIndentGuide-activeBackground2)", "editorIndentGuide-activeBackground3": "var(--vscode-editorIndentGuide-activeBackground3)", "editorIndentGuide-activeBackground4": "var(--vscode-editorIndentGuide-activeBackground4)", "editorIndentGuide-activeBackground5": "var(--vscode-editorIndentGuide-activeBackground5)", "editorIndentGuide-activeBackground6": "var(--vscode-editorIndentGuide-activeBackground6)", "editorIndentGuide-background": "var(--vscode-editorIndentGuide-background)", "editorIndentGuide-background1": "var(--vscode-editorIndentGuide-background1)", "editorIndentGuide-background2": "var(--vscode-editorIndentGuide-background2)", "editorIndentGuide-background3": "var(--vscode-editorIndentGuide-background3)", "editorIndentGuide-background4": "var(--vscode-editorIndentGuide-background4)", "editorIndentGuide-background5": "var(--vscode-editorIndentGuide-background5)", "editorIndentGuide-background6": "var(--vscode-editorIndentGuide-background6)", "editorInfo-background": "var(--vscode-editorInfo-background)", "editorInfo-border": "var(--vscode-editorInfo-border)", "editorInfo-foreground": "var(--vscode-editorInfo-foreground)", "editorInlayHint-background": "var(--vscode-editorInlayHint-background)", "editorInlayHint-foreground": "var(--vscode-editorInlayHint-foreground)", "editorInlayHint-parameterBackground": "var(--vscode-editorInlayHint-parameterBackground)", "editorInlayHint-parameterForeground": "var(--vscode-editorInlayHint-parameterForeground)", "editorInlayHint-typeBackground": "var(--vscode-editorInlayHint-typeBackground)", "editorInlayHint-typeForeground": "var(--vscode-editorInlayHint-typeForeground)", "editorLightBulb-foreground": "var(--vscode-editorLightBulb-foreground)", "editorLightBulbAi-foreground": "var(--vscode-editorLightBulbAi-foreground)", "editorLightBulbAutoFix-foreground": "var(--vscode-editorLightBulbAutoFix-foreground)", "editorLineNumber-activeForeground": "var(--vscode-editorLineNumber-activeForeground)", "editorLineNumber-dimmedForeground": "var(--vscode-editorLineNumber-dimmedForeground)", "editorLineNumber-foreground": "var(--vscode-editorLineNumber-foreground)", "editorLink-activeForeground": "var(--vscode-editorLink-activeForeground)", "editorMarkerNavigation-background": "var(--vscode-editor<PERSON><PERSON>er<PERSON><PERSON>-background)", "editorMarkerNavigationError-background": "var(--vscode-editor<PERSON>arkerNavigationError-background)", "editorMarkerNavigationError-headerBackground": "var(--vscode-editorMarkerNavigationError-headerBackground)", "editorMarkerNavigationInfo-background": "var(--vscode-editorMarkerNavigationInfo-background)", "editorMarkerNavigationInfo-headerBackground": "var(--vscode-editorMarkerNavigationInfo-headerBackground)", "editorMarkerNavigationWarning-background": "var(--vscode-editor<PERSON>arkerNavigationWarning-background)", "editorMarkerNavigationWarning-headerBackground": "var(--vscode-editorMarkerNavigationWarning-headerBackground)", "editorMinimap-inlineChatInserted": "var(--vscode-editorMinimap-inlineChatInserted)", "editorMultiCursor-primary-background": "var(--vscode-editor<PERSON>ultiCursor-primary-background)", "editorMultiCursor-primary-foreground": "var(--vscode-editorMultiCursor-primary-foreground)", "editorMultiCursor-secondary-background": "var(--vscode-editor<PERSON>ultiCursor-secondary-background)", "editorMultiCursor-secondary-foreground": "var(--vscode-editorMultiCursor-secondary-foreground)", "editorOverviewRuler-addedForeground": "var(--vscode-editorOverviewRuler-addedForeground)", "editorOverviewRuler-background": "var(--vscode-editorOverviewRuler-background)", "editorOverviewRuler-border": "var(--vscode-editorOverviewRuler-border)", "editorOverviewRuler-bracketMatchForeground": "var(--vscode-editorOverviewRuler-bracketMatchForeground)", "editorOverviewRuler-commentForeground": "var(--vscode-editorOverviewRuler-commentForeground)", "editorOverviewRuler-commentUnresolvedForeground": "var(--vscode-editorOverviewRuler-commentUnresolvedForeground)", "editorOverviewRuler-commonContentForeground": "var(--vscode-editorOverviewRuler-commonContentForeground)", "editorOverviewRuler-currentContentForeground": "var(--vscode-editorOverviewRuler-currentContentForeground)", "editorOverviewRuler-deletedForeground": "var(--vscode-editorOverviewRuler-deletedForeground)", "editorOverviewRuler-errorForeground": "var(--vscode-editorOverviewRuler-errorForeground)", "editorOverviewRuler-findMatchForeground": "var(--vscode-editorOverviewRuler-findMatchForeground)", "editorOverviewRuler-incomingContentForeground": "var(--vscode-editorOverviewRuler-incomingContentForeground)", "editorOverviewRuler-infoForeground": "var(--vscode-editorOverviewRuler-infoForeground)", "editorOverviewRuler-inlineChatInserted": "var(--vscode-editorOverviewRuler-inlineChatInserted)", "editorOverviewRuler-inlineChatRemoved": "var(--vscode-editorOverviewRuler-inlineChatRemoved)", "editorOverviewRuler-modifiedForeground": "var(--vscode-editorOverviewRuler-modifiedForeground)", "editorOverviewRuler-rangeHighlightForeground": "var(--vscode-editorOverviewRuler-rangeHighlightForeground)", "editorOverviewRuler-selectionHighlightForeground": "var(--vscode-editorOverviewRuler-selectionHighlightForeground)", "editorOverviewRuler-warningForeground": "var(--vscode-editorOverviewRuler-warningForeground)", "editorOverviewRuler-wordHighlightForeground": "var(--vscode-editorOverviewRuler-wordHighlightForeground)", "editorOverviewRuler-wordHighlightStrongForeground": "var(--vscode-editorOverviewRuler-wordHighlightStrongForeground)", "editorOverviewRuler-wordHighlightTextForeground": "var(--vscode-editorOverviewRuler-wordHighlightTextForeground)", "editorPane-background": "var(--vscode-editorPane-background)", "editorRuler-foreground": "var(--vscode-editorRuler-foreground)", "editorStickyScroll-background": "var(--vscode-editorStickyScroll-background)", "editorStickyScroll-border": "var(--vscode-editorStickyScroll-border)", "editorStickyScroll-shadow": "var(--vscode-editorStickyScroll-shadow)", "editorStickyScrollHover-background": "var(--vscode-editorStickyScrollHover-background)", "editorSuggestWidget-background": "var(--vscode-editorSuggestWidget-background)", "editorSuggestWidget-border": "var(--vscode-editorSuggestWidget-border)", "editorSuggestWidget-focusHighlightForeground": "var(--vscode-editorSuggestWidget-focusHighlightForeground)", "editorSuggestWidget-foreground": "var(--vscode-editorSuggestWidget-foreground)", "editorSuggestWidget-highlightForeground": "var(--vscode-editorSuggestWidget-highlightForeground)", "editorSuggestWidget-selectedBackground": "var(--vscode-editorSuggestWidget-selectedBackground)", "editorSuggestWidget-selectedForeground": "var(--vscode-editorSuggestWidget-selectedForeground)", "editorSuggestWidget-selectedIconForeground": "var(--vscode-editorSuggestWidget-selectedIconForeground)", "editorSuggestWidgetStatus-foreground": "var(--vscode-editorSuggestWidgetStatus-foreground)", "editorUnicodeHighlight-background": "var(--vscode-editorUnicodeHighlight-background)", "editorUnicodeHighlight-border": "var(--vscode-editorUnicodeHighlight-border)", "editorUnnecessaryCode-border": "var(--vscode-editorUnnecessaryCode-border)", "editorUnnecessaryCode-opacity": "var(--vscode-editorUnnecessaryCode-opacity)", "editorWarning-background": "var(--vscode-editorWarning-background)", "editorWarning-border": "var(--vscode-editorWarning-border)", "editorWarning-foreground": "var(--vscode-editorW<PERSON>ning-foreground)", "editorWatermark-foreground": "var(--vscode-editorWatermark-foreground)", "editorWhitespace-foreground": "var(--vscode-editorWhitespace-foreground)", "editorWidget-background": "var(--vscode-editorWidget-background)", "editorWidget-border": "var(--vscode-editorWidget-border)", "editorWidget-foreground": "var(--vscode-editorWidget-foreground)", "editorWidget-resizeBorder": "var(--vscode-editorWidget-resizeBorder)", "errorForeground": "var(--vscode-errorForeground)", "extensionBadge-remoteBackground": "var(--vscode-extensionBadge-remoteBackground)", "extensionBadge-remoteForeground": "var(--vscode-extensionBadge-remoteForeground)", "extensionButton-background": "var(--vscode-extensionButton-background)", "extensionButton-foreground": "var(--vscode-extensionButton-foreground)", "extensionButton-hoverBackground": "var(--vscode-extensionButton-hoverBackground)", "extensionButton-prominentBackground": "var(--vscode-extensionButton-prominentBackground)", "extensionButton-prominentForeground": "var(--vscode-extensionButton-prominentForeground)", "extensionButton-prominentHoverBackground": "var(--vscode-extensionButton-prominentHoverBackground)", "extensionButton-separator": "var(--vscode-extensionButton-separator)", "extensionIcon-preReleaseForeground": "var(--vscode-extensionIcon-preReleaseForeground)", "extensionIcon-privateForeground": "var(--vscode-extensionIcon-privateForeground)", "extensionIcon-sponsorForeground": "var(--vscode-extensionIcon-sponsorForeground)", "extensionIcon-starForeground": "var(--vscode-extensionIcon-starForeground)", "extensionIcon-verifiedForeground": "var(--vscode-extensionIcon-verifiedForeground)", "focusBorder": "var(--vscode-focusBorder)", "foreground": "var(--vscode-foreground)", "gauge-background": "var(--vscode-gauge-background)", "gauge-border": "var(--vscode-gauge-border)", "gauge-errorBackground": "var(--vscode-gauge-errorBackground)", "gauge-errorForeground": "var(--vscode-gauge-errorForeground)", "gauge-foreground": "var(--vscode-gauge-foreground)", "gauge-warningBackground": "var(--vscode-gauge-warningBackground)", "gauge-warningForeground": "var(--vscode-gauge-warningForeground)", "icon-foreground": "var(--vscode-icon-foreground)", "inlineChat-background": "var(--vscode-inlineChat-background)", "inlineChat-border": "var(--vscode-inlineChat-border)", "inlineChat-foreground": "var(--vscode-inlineChat-foreground)", "inlineChat-shadow": "var(--vscode-inlineChat-shadow)", "inlineChatDiff-inserted": "var(--vscode-inlineChatDiff-inserted)", "inlineChatDiff-removed": "var(--vscode-inlineChatDiff-removed)", "inlineChatInput-background": "var(--vscode-inlineChatInput-background)", "inlineChatInput-border": "var(--vscode-inlineChatInput-border)", "inlineChatInput-focusBorder": "var(--vscode-inlineChatInput-focusBorder)", "inlineChatInput-placeholderForeground": "var(--vscode-inlineChatInput-placeholderForeground)", "inlineEdit-gutterIndicator-background": "var(--vscode-inlineEdit-gutterIndicator-background)", "inlineEdit-gutterIndicator-primaryBackground": "var(--vscode-inlineEdit-gutterIndicator-primaryBackground)", "inlineEdit-gutterIndicator-primaryBorder": "var(--vscode-inlineEdit-gutterIndicator-primaryBorder)", "inlineEdit-gutterIndicator-primaryForeground": "var(--vscode-inlineEdit-gutterIndicator-primaryForeground)", "inlineEdit-gutterIndicator-secondaryBackground": "var(--vscode-inlineEdit-gutterIndicator-secondaryBackground)", "inlineEdit-gutterIndicator-secondaryBorder": "var(--vscode-inlineEdit-gutterIndicator-secondaryBorder)", "inlineEdit-gutterIndicator-secondaryForeground": "var(--vscode-inlineEdit-gutterIndicator-secondaryForeground)", "inlineEdit-gutterIndicator-successfulBackground": "var(--vscode-inlineEdit-gutterIndicator-successfulBackground)", "inlineEdit-gutterIndicator-successfulBorder": "var(--vscode-inlineEdit-gutterIndicator-successfulBorder)", "inlineEdit-gutterIndicator-successfulForeground": "var(--vscode-inlineEdit-gutterIndicator-successfulForeground)", "inlineEdit-modifiedBackground": "var(--vscode-inlineEdit-modifiedBackground)", "inlineEdit-modifiedBorder": "var(--vscode-inlineEdit-modifiedBorder)", "inlineEdit-modifiedChangedLineBackground": "var(--vscode-inlineEdit-modifiedChangedLineBackground)", "inlineEdit-modifiedChangedTextBackground": "var(--vscode-inlineEdit-modifiedChangedTextBackground)", "inlineEdit-originalBackground": "var(--vscode-inlineEdit-originalBackground)", "inlineEdit-originalBorder": "var(--vscode-inlineEdit-originalBorder)", "inlineEdit-originalChangedLineBackground": "var(--vscode-inlineEdit-originalChangedLineBackground)", "inlineEdit-originalChangedTextBackground": "var(--vscode-inlineEdit-originalChangedTextBackground)", "inlineEdit-tabWillAcceptModifiedBorder": "var(--vscode-inlineEdit-tabWillAcceptModifiedBorder)", "inlineEdit-tabWillAcceptOriginalBorder": "var(--vscode-inlineEdit-tabWillAcceptOriginalBorder)", "input-background": "var(--vscode-input-background)", "input-border": "var(--vscode-input-border)", "input-foreground": "var(--vscode-input-foreground)", "input-placeholderForeground": "var(--vscode-input-placeholderForeground)", "inputOption-activeBackground": "var(--vscode-inputOption-activeBackground)", "inputOption-activeBorder": "var(--vscode-inputOption-activeBorder)", "inputOption-activeForeground": "var(--vscode-inputOption-activeForeground)", "inputOption-hoverBackground": "var(--vscode-inputOption-hoverBackground)", "inputValidation-errorBackground": "var(--vscode-inputValidation-errorBackground)", "inputValidation-errorBorder": "var(--vscode-inputValidation-errorBorder)", "inputValidation-errorForeground": "var(--vscode-inputValidation-errorForeground)", "inputValidation-infoBackground": "var(--vscode-inputValidation-infoBackground)", "inputValidation-infoBorder": "var(--vscode-inputValidation-infoBorder)", "inputValidation-infoForeground": "var(--vscode-inputValidation-infoForeground)", "inputValidation-warningBackground": "var(--vscode-inputValidation-warningBackground)", "inputValidation-warningBorder": "var(--vscode-inputValidation-warningBorder)", "inputValidation-warningForeground": "var(--vscode-inputValidation-warningForeground)", "interactive-activeCodeBorder": "var(--vscode-interactive-activeCodeBorder)", "interactive-inactiveCodeBorder": "var(--vscode-interactive-inactiveCodeBorder)", "keybindingLabel-background": "var(--vscode-keybindingLabel-background)", "keybindingLabel-border": "var(--vscode-keybindingLabel-border)", "keybindingLabel-bottomBorder": "var(--vscode-keybindingLabel-bottomBorder)", "keybindingLabel-foreground": "var(--vscode-keybindingLabel-foreground)", "keybindingTable-headerBackground": "var(--vscode-keybindingTable-headerBackground)", "keybindingTable-rowsBackground": "var(--vscode-keybindingTable-rowsBackground)", "list-activeSelectionBackground": "var(--vscode-list-activeSelectionBackground)", "list-activeSelectionForeground": "var(--vscode-list-activeSelectionForeground)", "list-activeSelectionIconForeground": "var(--vscode-list-activeSelectionIconForeground)", "list-deemphasizedForeground": "var(--vscode-list-deemphasizedForeground)", "list-dropBackground": "var(--vscode-list-dropBackground)", "list-dropBetweenBackground": "var(--vscode-list-dropBetweenBackground)", "list-errorForeground": "var(--vscode-list-errorForeground)", "list-filterMatchBackground": "var(--vscode-list-filterMatchBackground)", "list-filterMatchBorder": "var(--vscode-list-filterMatchBorder)", "list-focusAndSelectionOutline": "var(--vscode-list-focusAndSelectionOutline)", "list-focusBackground": "var(--vscode-list-focusBackground)", "list-focusForeground": "var(--vscode-list-focusForeground)", "list-focusHighlightForeground": "var(--vscode-list-focusHighlightForeground)", "list-focusOutline": "var(--vscode-list-focusOutline)", "list-highlightForeground": "var(--vscode-list-highlightForeground)", "list-hoverBackground": "var(--vscode-list-hoverBackground)", "list-hoverForeground": "var(--vscode-list-hoverForeground)", "list-inactiveFocusBackground": "var(--vscode-list-inactiveFocusBackground)", "list-inactiveFocusOutline": "var(--vscode-list-inactiveFocusOutline)", "list-inactiveSelectionBackground": "var(--vscode-list-inactiveSelectionBackground)", "list-inactiveSelectionForeground": "var(--vscode-list-inactiveSelectionForeground)", "list-inactiveSelectionIconForeground": "var(--vscode-list-inactiveSelectionIconForeground)", "list-invalidItemForeground": "var(--vscode-list-invalidItemForeground)", "list-warningForeground": "var(--vscode-list-warningForeground)", "listFilterWidget-background": "var(--vscode-listFilterWidget-background)", "listFilterWidget-noMatchesOutline": "var(--vscode-listFilterWidget-noMatchesOutline)", "listFilterWidget-outline": "var(--vscode-listFilterWidget-outline)", "listFilterWidget-shadow": "var(--vscode-listFilterWidget-shadow)", "menu-background": "var(--vscode-menu-background)", "menu-border": "var(--vscode-menu-border)", "menu-foreground": "var(--vscode-menu-foreground)", "menu-selectionBackground": "var(--vscode-menu-selectionBackground)", "menu-selectionBorder": "var(--vscode-menu-selectionBorder)", "menu-selectionForeground": "var(--vscode-menu-selectionForeground)", "menu-separatorBackground": "var(--vscode-menu-separatorBackground)", "menubar-selectionBackground": "var(--vscode-menubar-selectionBackground)", "menubar-selectionBorder": "var(--vscode-menubar-selectionBorder)", "menubar-selectionForeground": "var(--vscode-menubar-selectionForeground)", "merge-border": "var(--vscode-merge-border)", "merge-commonContentBackground": "var(--vscode-merge-commonContentBackground)", "merge-commonHeaderBackground": "var(--vscode-merge-commonHeaderBackground)", "merge-currentContentBackground": "var(--vscode-merge-currentContentBackground)", "merge-currentHeaderBackground": "var(--vscode-merge-currentHeaderBackground)", "merge-incomingContentBackground": "var(--vscode-merge-incomingContentBackground)", "merge-incomingHeaderBackground": "var(--vscode-merge-incomingHeaderBackground)", "mergeEditor-change-background": "var(--vscode-mergeEditor-change-background)", "mergeEditor-change-word-background": "var(--vscode-mergeEditor-change-word-background)", "mergeEditor-changeBase-background": "var(--vscode-mergeEditor-changeBase-background)", "mergeEditor-changeBase-word-background": "var(--vscode-mergeEditor-changeBase-word-background)", "mergeEditor-conflict-handled-minimapOverViewRuler": "var(--vscode-mergeEditor-conflict-handled-minimapOverViewRuler)", "mergeEditor-conflict-handledFocused-border": "var(--vscode-mergeEditor-conflict-handledFocused-border)", "mergeEditor-conflict-handledUnfocused-border": "var(--vscode-mergeEditor-conflict-handledUnfocused-border)", "mergeEditor-conflict-input1-background": "var(--vscode-mergeEditor-conflict-input1-background)", "mergeEditor-conflict-input2-background": "var(--vscode-mergeEditor-conflict-input2-background)", "mergeEditor-conflict-unhandled-minimapOverViewRuler": "var(--vscode-mergeEditor-conflict-unhandled-minimapOverViewRuler)", "mergeEditor-conflict-unhandledFocused-border": "var(--vscode-mergeEditor-conflict-unhandledFocused-border)", "mergeEditor-conflict-unhandledUnfocused-border": "var(--vscode-mergeEditor-conflict-unhandledUnfocused-border)", "mergeEditor-conflictingLines-background": "var(--vscode-mergeEditor-conflictingLines-background)", "minimap-background": "var(--vscode-minimap-background)", "minimap-chatEditHighlight": "var(--vscode-minimap-chatEditHighlight)", "minimap-errorHighlight": "var(--vscode-minimap-errorHighlight)", "minimap-findMatchHighlight": "var(--vscode-minimap-findMatchHighlight)", "minimap-foregroundOpacity": "var(--vscode-minimap-foregroundOpacity)", "minimap-infoHighlight": "var(--vscode-minimap-infoHighlight)", "minimap-selectionHighlight": "var(--vscode-minimap-selectionHighlight)", "minimap-selectionOccurrenceHighlight": "var(--vscode-minimap-selectionOccurrenceHighlight)", "minimap-warningHighlight": "var(--vscode-minimap-warningHighlight)", "minimapGutter-addedBackground": "var(--vscode-minimapGutter-addedBackground)", "minimapGutter-deletedBackground": "var(--vscode-minimapGutter-deletedBackground)", "minimapGutter-modifiedBackground": "var(--vscode-minimapGutter-modifiedBackground)", "minimapSlider-activeBackground": "var(--vscode-minimapSlider-activeBackground)", "minimapSlider-background": "var(--vscode-minimapSlider-background)", "minimapSlider-hoverBackground": "var(--vscode-minimapSlider-hoverBackground)", "multiDiffEditor-background": "var(--vscode-multiDiffEditor-background)", "multiDiffEditor-border": "var(--vscode-multiDiffEditor-border)", "multiDiffEditor-headerBackground": "var(--vscode-multiDiffEditor-headerBackground)", "notebook-cellBorderColor": "var(--vscode-notebook-cellBorderColor)", "notebook-cellEditorBackground": "var(--vscode-notebook-cellEditorBackground)", "notebook-cellHoverBackground": "var(--vscode-notebook-cellHoverBackground)", "notebook-cellInsertionIndicator": "var(--vscode-notebook-cellInsertionIndicator)", "notebook-cellStatusBarItemHoverBackground": "var(--vscode-notebook-cellStatusBarItemHoverBackground)", "notebook-cellToolbarSeparator": "var(--vscode-notebook-cellToolbarSeparator)", "notebook-editorBackground": "var(--vscode-notebook-editorBackground)", "notebook-focusedCellBackground": "var(--vscode-notebook-focusedCellBackground)", "notebook-focusedCellBorder": "var(--vscode-notebook-focusedCellBorder)", "notebook-focusedEditorBorder": "var(--vscode-notebook-focusedEditorBorder)", "notebook-inactiveFocusedCellBorder": "var(--vscode-notebook-inactiveFocusedCellBorder)", "notebook-inactiveSelectedCellBorder": "var(--vscode-notebook-inactiveSelectedCellBorder)", "notebook-outputContainerBackgroundColor": "var(--vscode-notebook-outputContainerBackgroundColor)", "notebook-outputContainerBorderColor": "var(--vscode-notebook-outputContainerBorderColor)", "notebook-selectedCellBackground": "var(--vscode-notebook-selectedCellBackground)", "notebook-selectedCellBorder": "var(--vscode-notebook-selectedCellBorder)", "notebook-symbolHighlightBackground": "var(--vscode-notebook-symbolHighlightBackground)", "notebookEditorOverviewRuler-runningCellForeground": "var(--vscode-notebookEditorOverviewRuler-runningCellForeground)", "notebookScrollbarSlider-activeBackground": "var(--vscode-notebookScrollbarSlider-activeBackground)", "notebookScrollbarSlider-background": "var(--vscode-notebookScrollbarSlider-background)", "notebookScrollbarSlider-hoverBackground": "var(--vscode-notebookScrollbarSlider-hoverBackground)", "notebookStatusErrorIcon-foreground": "var(--vscode-notebookStatusErrorIcon-foreground)", "notebookStatusRunningIcon-foreground": "var(--vscode-notebookStatusRunningIcon-foreground)", "notebookStatusSuccessIcon-foreground": "var(--vscode-notebookStatusSuccessIcon-foreground)", "notificationCenter-border": "var(--vscode-notificationCenter-border)", "notificationCenterHeader-background": "var(--vscode-notificationCenterHeader-background)", "notificationCenterHeader-foreground": "var(--vscode-notificationCenterHeader-foreground)", "notificationLink-foreground": "var(--vscode-notificationLink-foreground)", "notificationToast-border": "var(--vscode-notificationToast-border)", "notifications-background": "var(--vscode-notifications-background)", "notifications-border": "var(--vscode-notifications-border)", "notifications-foreground": "var(--vscode-notifications-foreground)", "notificationsErrorIcon-foreground": "var(--vscode-notificationsErrorIcon-foreground)", "notificationsInfoIcon-foreground": "var(--vscode-notificationsInfoIcon-foreground)", "notificationsWarningIcon-foreground": "var(--vscode-notificationsWarningIcon-foreground)", "outputView-background": "var(--vscode-outputView-background)", "outputViewStickyScroll-background": "var(--vscode-outputViewStickyScroll-background)", "panel-background": "var(--vscode-panel-background)", "panel-border": "var(--vscode-panel-border)", "panel-dropBorder": "var(--vscode-panel-dropBorder)", "panelInput-border": "var(--vscode-panelInput-border)", "panelSection-border": "var(--vscode-panelSection-border)", "panelSection-dropBackground": "var(--vscode-panelSection-dropBackground)", "panelSectionHeader-background": "var(--vscode-panelSectionHeader-background)", "panelSectionHeader-border": "var(--vscode-panelSectionHeader-border)", "panelSectionHeader-foreground": "var(--vscode-panelSectionHeader-foreground)", "panelStickyScroll-background": "var(--vscode-panelStickyScroll-background)", "panelStickyScroll-border": "var(--vscode-panelStickyScroll-border)", "panelStickyScroll-shadow": "var(--vscode-panelStickyScroll-shadow)", "panelTitle-activeBorder": "var(--vscode-panelTitle-activeBorder)", "panelTitle-activeForeground": "var(--vscode-panelTitle-activeForeground)", "panelTitle-border": "var(--vscode-panelTitle-border)", "panelTitle-inactiveForeground": "var(--vscode-panelTitle-inactiveForeground)", "panelTitleBadge-background": "var(--vscode-panelTitleBadge-background)", "panelTitleBadge-foreground": "var(--vscode-panelTitleBadge-foreground)", "peekView-border": "var(--vscode-peekView-border)", "peekViewEditor-background": "var(--vscode-peekViewEditor-background)", "peekViewEditor-matchHighlightBackground": "var(--vscode-peekViewEditor-matchHighlightBackground)", "peekViewEditor-matchHighlightBorder": "var(--vscode-peekViewEditor-matchHighlightBorder)", "peekViewEditorGutter-background": "var(--vscode-peekViewEditor<PERSON>utter-background)", "peekViewEditorStickyScroll-background": "var(--vscode-peekViewEditorStickyScroll-background)", "peekViewResult-background": "var(--vscode-peek<PERSON>iewResult-background)", "peekViewResult-fileForeground": "var(--vscode-peekViewResult-fileForeground)", "peekViewResult-lineForeground": "var(--vscode-peekViewResult-lineForeground)", "peekViewResult-matchHighlightBackground": "var(--vscode-peekViewResult-matchHighlightBackground)", "peekViewResult-selectionBackground": "var(--vscode-peekViewResult-selectionBackground)", "peekViewResult-selectionForeground": "var(--vscode-peekViewResult-selectionForeground)", "peekViewTitle-background": "var(--vscode-peekViewTitle-background)", "peekViewTitleDescription-foreground": "var(--vscode-peekViewTitleDescription-foreground)", "peekViewTitleLabel-foreground": "var(--vscode-peekViewTitleLabel-foreground)", "pickerGroup-border": "var(--vscode-pickerGroup-border)", "pickerGroup-foreground": "var(--vscode-pickerGroup-foreground)", "ports-iconRunningProcessForeground": "var(--vscode-ports-iconRunningProcessForeground)", "problemsErrorIcon-foreground": "var(--vscode-problemsErrorIcon-foreground)", "problemsInfoIcon-foreground": "var(--vscode-problemsInfoIcon-foreground)", "problemsWarningIcon-foreground": "var(--vscode-problemsWarningIcon-foreground)", "profileBadge-background": "var(--vscode-profileBadge-background)", "profileBadge-foreground": "var(--vscode-profileBadge-foreground)", "profiles-sashBorder": "var(--vscode-profiles-sashBorder)", "progressBar-background": "var(--vscode-progressBar-background)", "quickInput-background": "var(--vscode-quickInput-background)", "quickInput-foreground": "var(--vscode-quickInput-foreground)", "quickInputList-focusBackground": "var(--vscode-quickInputList-focusBackground)", "quickInputList-focusForeground": "var(--vscode-quickInputList-focusForeground)", "quickInputList-focusIconForeground": "var(--vscode-quickInputList-focusIconForeground)", "quickInputTitle-background": "var(--vscode-quickInputTitle-background)", "radio-activeBackground": "var(--vscode-radio-activeBackground)", "radio-activeBorder": "var(--vscode-radio-activeBorder)", "radio-activeForeground": "var(--vscode-radio-activeForeground)", "radio-inactiveBackground": "var(--vscode-radio-inactiveBackground)", "radio-inactiveBorder": "var(--vscode-radio-inactiveBorder)", "radio-inactiveForeground": "var(--vscode-radio-inactiveForeground)", "radio-inactiveHoverBackground": "var(--vscode-radio-inactiveHoverBackground)", "sash-hoverBorder": "var(--vscode-sash-hoverBorder)", "scmGraph-foreground1": "var(--vscode-scmGraph-foreground1)", "scmGraph-foreground2": "var(--vscode-scmGraph-foreground2)", "scmGraph-foreground3": "var(--vscode-scmGraph-foreground3)", "scmGraph-foreground4": "var(--vscode-scmGraph-foreground4)", "scmGraph-foreground5": "var(--vscode-scmGraph-foreground5)", "scmGraph-historyItemBaseRefColor": "var(--vscode-scmGraph-historyItemBaseRefColor)", "scmGraph-historyItemHoverAdditionsForeground": "var(--vscode-scmGraph-historyItemHoverAdditionsForeground)", "scmGraph-historyItemHoverDefaultLabelBackground": "var(--vscode-scmGraph-historyItemHoverDefaultLabelBackground)", "scmGraph-historyItemHoverDefaultLabelForeground": "var(--vscode-scmGraph-historyItemHoverDefaultLabelForeground)", "scmGraph-historyItemHoverDeletionsForeground": "var(--vscode-scmGraph-historyItemHoverDeletionsForeground)", "scmGraph-historyItemHoverLabelForeground": "var(--vscode-scmGraph-historyItemHoverLabelForeground)", "scmGraph-historyItemRefColor": "var(--vscode-scmGraph-historyItemRefColor)", "scmGraph-historyItemRemoteRefColor": "var(--vscode-scmGraph-historyItemRemoteRefColor)", "scrollbar-shadow": "var(--vscode-scrollbar-shadow)", "scrollbarSlider-activeBackground": "var(--vscode-scrollbarSlider-activeBackground)", "scrollbarSlider-background": "var(--vscode-scrollbarSlider-background)", "scrollbarSlider-hoverBackground": "var(--vscode-scrollbarSlider-hoverBackground)", "search-resultsInfoForeground": "var(--vscode-search-resultsInfoForeground)", "searchEditor-findMatchBackground": "var(--vscode-searchEditor-findMatchBackground)", "searchEditor-findMatchBorder": "var(--vscode-searchEditor-findMatchBorder)", "searchEditor-textInputBorder": "var(--vscode-searchEditor-textInputBorder)", "selection-background": "var(--vscode-selection-background)", "settings-checkboxBackground": "var(--vscode-settings-checkboxBackground)", "settings-checkboxBorder": "var(--vscode-settings-checkboxBorder)", "settings-checkboxForeground": "var(--vscode-settings-checkboxForeground)", "settings-dropdownBackground": "var(--vscode-settings-dropdownBackground)", "settings-dropdownBorder": "var(--vscode-settings-dropdownBorder)", "settings-dropdownForeground": "var(--vscode-settings-dropdownForeground)", "settings-dropdownListBorder": "var(--vscode-settings-dropdownListBorder)", "settings-focusedRowBackground": "var(--vscode-settings-focusedRowBackground)", "settings-focusedRowBorder": "var(--vscode-settings-focusedRowBorder)", "settings-headerBorder": "var(--vscode-settings-headerBorder)", "settings-headerForeground": "var(--vscode-settings-headerForeground)", "settings-modifiedItemIndicator": "var(--vscode-settings-modifiedItemIndicator)", "settings-numberInputBackground": "var(--vscode-settings-numberInputBackground)", "settings-numberInputBorder": "var(--vscode-settings-numberInputBorder)", "settings-numberInputForeground": "var(--vscode-settings-numberInputForeground)", "settings-rowHoverBackground": "var(--vscode-settings-rowHoverBackground)", "settings-sashBorder": "var(--vscode-settings-sashBorder)", "settings-settingsHeaderHoverForeground": "var(--vscode-settings-settingsHeaderHoverForeground)", "settings-textInputBackground": "var(--vscode-settings-textInputBackground)", "settings-textInputBorder": "var(--vscode-settings-textInputBorder)", "settings-textInputForeground": "var(--vscode-settings-textInputForeground)", "sideBar-background": "var(--vscode-sideBar-background)", "sideBar-border": "var(--vscode-sideBar-border)", "sideBar-dropBackground": "var(--vscode-sideBar-dropBackground)", "sideBar-foreground": "var(--vscode-sideBar-foreground)", "sideBarActivityBarTop-border": "var(--vscode-sideBarActivityBarTop-border)", "sideBarSectionHeader-background": "var(--vscode-sideBarSectionHeader-background)", "sideBarSectionHeader-border": "var(--vscode-sideBarSectionHeader-border)", "sideBarSectionHeader-foreground": "var(--vscode-sideBarSectionHeader-foreground)", "sideBarStickyScroll-background": "var(--vscode-sideBarStickyScroll-background)", "sideBarStickyScroll-border": "var(--vscode-sideBarStickyScroll-border)", "sideBarStickyScroll-shadow": "var(--vscode-sideBarStickyScroll-shadow)", "sideBarTitle-background": "var(--vscode-sideBarTitle-background)", "sideBarTitle-border": "var(--vscode-sideBarTitle-border)", "sideBarTitle-foreground": "var(--vscode-sideBarTitle-foreground)", "sideBySideEditor-horizontalBorder": "var(--vscode-sideBySideEditor-horizontalBorder)", "sideBySideEditor-verticalBorder": "var(--vscode-sideBySideEditor-verticalBorder)", "simpleFindWidget-sashBorder": "var(--vscode-simpleFindWidget-sashBorder)", "statusBar-background": "var(--vscode-statusBar-background)", "statusBar-border": "var(--vscode-statusBar-border)", "statusBar-debuggingBackground": "var(--vscode-statusBar-debuggingBackground)", "statusBar-debuggingBorder": "var(--vscode-statusBar-debuggingBorder)", "statusBar-debuggingForeground": "var(--vscode-statusBar-debuggingForeground)", "statusBar-focusBorder": "var(--vscode-statusBar-focusBorder)", "statusBar-foreground": "var(--vscode-statusBar-foreground)", "statusBar-noFolderBackground": "var(--vscode-statusBar-noFolderBackground)", "statusBar-noFolderBorder": "var(--vscode-statusBar-noFolderBorder)", "statusBar-noFolderForeground": "var(--vscode-statusBar-noFolderForeground)", "statusBarItem-activeBackground": "var(--vscode-statusBarItem-activeBackground)", "statusBarItem-compactHoverBackground": "var(--vscode-statusBarItem-compactHoverBackground)", "statusBarItem-errorBackground": "var(--vscode-statusBarItem-errorBackground)", "statusBarItem-errorForeground": "var(--vscode-statusBarItem-errorForeground)", "statusBarItem-errorHoverBackground": "var(--vscode-statusBarItem-errorHoverBackground)", "statusBarItem-errorHoverForeground": "var(--vscode-statusBarItem-errorHoverForeground)", "statusBarItem-focusBorder": "var(--vscode-statusBarItem-focusBorder)", "statusBarItem-hoverBackground": "var(--vscode-statusBarItem-hoverBackground)", "statusBarItem-hoverForeground": "var(--vscode-statusBarItem-hoverForeground)", "statusBarItem-offlineBackground": "var(--vscode-statusBarItem-offlineBackground)", "statusBarItem-offlineForeground": "var(--vscode-statusBarItem-offlineForeground)", "statusBarItem-offlineHoverBackground": "var(--vscode-statusBarItem-offlineHoverBackground)", "statusBarItem-offlineHoverForeground": "var(--vscode-statusBarItem-offlineHoverForeground)", "statusBarItem-prominentBackground": "var(--vscode-statusBarItem-prominentBackground)", "statusBarItem-prominentForeground": "var(--vscode-statusBarItem-prominentForeground)", "statusBarItem-prominentHoverBackground": "var(--vscode-statusBarItem-prominentHoverBackground)", "statusBarItem-prominentHoverForeground": "var(--vscode-statusBarItem-prominentHoverForeground)", "statusBarItem-remoteBackground": "var(--vscode-statusBarItem-remoteBackground)", "statusBarItem-remoteForeground": "var(--vscode-statusBarItem-remoteForeground)", "statusBarItem-remoteHoverBackground": "var(--vscode-statusBarItem-remoteHoverBackground)", "statusBarItem-remoteHoverForeground": "var(--vscode-statusBarItem-remoteHoverForeground)", "statusBarItem-warningBackground": "var(--vscode-statusBarItem-warningBackground)", "statusBarItem-warningForeground": "var(--vscode-statusBarItem-warningForeground)", "statusBarItem-warningHoverBackground": "var(--vscode-statusBarItem-warningHoverBackground)", "statusBarItem-warningHoverForeground": "var(--vscode-statusBarItem-warningHoverForeground)", "symbolIcon-arrayForeground": "var(--vscode-symbolIcon-arrayForeground)", "symbolIcon-booleanForeground": "var(--vscode-symbolIcon-booleanForeground)", "symbolIcon-classForeground": "var(--vscode-symbolIcon-classForeground)", "symbolIcon-colorForeground": "var(--vscode-symbolIcon-colorForeground)", "symbolIcon-constantForeground": "var(--vscode-symbolIcon-constantForeground)", "symbolIcon-constructorForeground": "var(--vscode-symbolIcon-constructorForeground)", "symbolIcon-enumeratorForeground": "var(--vscode-symbolIcon-enumeratorForeground)", "symbolIcon-enumeratorMemberForeground": "var(--vscode-symbolIcon-enumeratorMemberForeground)", "symbolIcon-eventForeground": "var(--vscode-symbolIcon-eventForeground)", "symbolIcon-fieldForeground": "var(--vscode-symbolIcon-fieldForeground)", "symbolIcon-fileForeground": "var(--vscode-symbolIcon-fileForeground)", "symbolIcon-folderForeground": "var(--vscode-symbolIcon-folderForeground)", "symbolIcon-functionForeground": "var(--vscode-symbolIcon-functionForeground)", "symbolIcon-interfaceForeground": "var(--vscode-symbolIcon-interfaceForeground)", "symbolIcon-keyForeground": "var(--vscode-symbolIcon-keyForeground)", "symbolIcon-keywordForeground": "var(--vscode-symbolIcon-keywordForeground)", "symbolIcon-methodForeground": "var(--vscode-symbolIcon-methodForeground)", "symbolIcon-moduleForeground": "var(--vscode-symbolIcon-moduleForeground)", "symbolIcon-namespaceForeground": "var(--vscode-symbolIcon-namespaceForeground)", "symbolIcon-nullForeground": "var(--vscode-symbolIcon-nullForeground)", "symbolIcon-numberForeground": "var(--vscode-symbolIcon-numberForeground)", "symbolIcon-objectForeground": "var(--vscode-symbolIcon-objectForeground)", "symbolIcon-operatorForeground": "var(--vscode-symbolIcon-operatorForeground)", "symbolIcon-packageForeground": "var(--vscode-symbolIcon-packageForeground)", "symbolIcon-propertyForeground": "var(--vscode-symbolIcon-propertyForeground)", "symbolIcon-referenceForeground": "var(--vscode-symbolIcon-referenceForeground)", "symbolIcon-snippetForeground": "var(--vscode-symbolIcon-snippetForeground)", "symbolIcon-stringForeground": "var(--vscode-symbolIcon-stringForeground)", "symbolIcon-structForeground": "var(--vscode-symbolIcon-structForeground)", "symbolIcon-textForeground": "var(--vscode-symbolIcon-textForeground)", "symbolIcon-typeParameterForeground": "var(--vscode-symbolIcon-typeParameterForeground)", "symbolIcon-unitForeground": "var(--vscode-symbolIcon-unitForeground)", "symbolIcon-variableForeground": "var(--vscode-symbolIcon-variableForeground)", "tab-activeBackground": "var(--vscode-tab-activeBackground)", "tab-activeBorder": "var(--vscode-tab-activeBorder)", "tab-activeBorderTop": "var(--vscode-tab-activeBorderTop)", "tab-activeForeground": "var(--vscode-tab-activeForeground)", "tab-activeModifiedBorder": "var(--vscode-tab-activeModifiedBorder)", "tab-border": "var(--vscode-tab-border)", "tab-dragAndDropBorder": "var(--vscode-tab-dragAndDropBorder)", "tab-hoverBackground": "var(--vscode-tab-hoverBackground)", "tab-hoverBorder": "var(--vscode-tab-hoverBorder)", "tab-hoverForeground": "var(--vscode-tab-hoverForeground)", "tab-inactiveBackground": "var(--vscode-tab-inactiveBackground)", "tab-inactiveForeground": "var(--vscode-tab-inactiveForeground)", "tab-inactiveModifiedBorder": "var(--vscode-tab-inactiveModifiedBorder)", "tab-lastPinnedBorder": "var(--vscode-tab-lastPinnedBorder)", "tab-selectedBackground": "var(--vscode-tab-selectedBackground)", "tab-selectedBorderTop": "var(--vscode-tab-selectedBorderTop)", "tab-selectedForeground": "var(--vscode-tab-selectedForeground)", "tab-unfocusedActiveBackground": "var(--vscode-tab-unfocusedActiveBackground)", "tab-unfocusedActiveBorder": "var(--vscode-tab-unfocusedActiveBorder)", "tab-unfocusedActiveBorderTop": "var(--vscode-tab-unfocusedActiveBorderTop)", "tab-unfocusedActiveForeground": "var(--vscode-tab-unfocusedActiveForeground)", "tab-unfocusedActiveModifiedBorder": "var(--vscode-tab-unfocusedActiveModifiedBorder)", "tab-unfocusedHoverBackground": "var(--vscode-tab-unfocusedHoverBackground)", "tab-unfocusedHoverBorder": "var(--vscode-tab-unfocusedHoverBorder)", "tab-unfocusedHoverForeground": "var(--vscode-tab-unfocusedHoverForeground)", "tab-unfocusedInactiveBackground": "var(--vscode-tab-unfocusedInactiveBackground)", "tab-unfocusedInactiveForeground": "var(--vscode-tab-unfocusedInactiveForeground)", "tab-unfocusedInactiveModifiedBorder": "var(--vscode-tab-unfocusedInactiveModifiedBorder)", "terminal-ansiBlack": "var(--vscode-terminal-ansiBlack)", "terminal-ansiBlue": "var(--vscode-terminal-ansiBlue)", "terminal-ansiBrightBlack": "var(--vscode-terminal-ansiBrightBlack)", "terminal-ansiBrightBlue": "var(--vscode-terminal-ansiBrightBlue)", "terminal-ansiBrightCyan": "var(--vscode-terminal-ansiBrightCyan)", "terminal-ansiBrightGreen": "var(--vscode-terminal-ansiBrightGreen)", "terminal-ansiBrightMagenta": "var(--vscode-terminal-ansiBrightMagenta)", "terminal-ansiBrightRed": "var(--vscode-terminal-ansiBrightRed)", "terminal-ansiBrightWhite": "var(--vscode-terminal-ansiBrightWhite)", "terminal-ansiBrightYellow": "var(--vscode-terminal-ansiBrightYellow)", "terminal-ansiCyan": "var(--vscode-terminal-ansiCyan)", "terminal-ansiGreen": "var(--vscode-terminal-ansiGreen)", "terminal-ansiMagenta": "var(--vscode-terminal-ansiMagenta)", "terminal-ansiRed": "var(--vscode-terminal-ansiRed)", "terminal-ansiWhite": "var(--vscode-terminal-ansiWhite)", "terminal-ansiYellow": "var(--vscode-terminal-an<PERSON><PERSON><PERSON><PERSON>)", "terminal-background": "var(--vscode-terminal-background)", "terminal-border": "var(--vscode-terminal-border)", "terminal-dropBackground": "var(--vscode-terminal-dropBackground)", "terminal-findMatchBackground": "var(--vscode-terminal-findMatchBackground)", "terminal-findMatchBorder": "var(--vscode-terminal-findMatchBorder)", "terminal-findMatchHighlightBackground": "var(--vscode-terminal-findMatchHighlightBackground)", "terminal-findMatchHighlightBorder": "var(--vscode-terminal-findMatchHighlightBorder)", "terminal-foreground": "var(--vscode-terminal-foreground)", "terminal-hoverHighlightBackground": "var(--vscode-terminal-hoverHighlightBackground)", "terminal-inactiveSelectionBackground": "var(--vscode-terminal-inactiveSelectionBackground)", "terminal-initialHintForeground": "var(--vscode-terminal-initialHintForeground)", "terminal-selectionBackground": "var(--vscode-terminal-selectionBackground)", "terminal-selectionForeground": "var(--vscode-terminal-selectionForeground)", "terminal-tab-activeBorder": "var(--vscode-terminal-tab-activeBorder)", "terminalCommandDecoration-defaultBackground": "var(--vscode-terminalCommandDecoration-defaultBackground)", "terminalCommandDecoration-errorBackground": "var(--vscode-terminalCommandDecoration-errorBackground)", "terminalCommandDecoration-successBackground": "var(--vscode-terminalCommandDecoration-successBackground)", "terminalCommandGuide-foreground": "var(--vscode-terminalCommandGuide-foreground)", "terminalCursor-background": "var(--vscode-terminalCursor-background)", "terminalCursor-foreground": "var(--vscode-terminalCursor-foreground)", "terminalOverviewRuler-border": "var(--vscode-terminalOverviewRuler-border)", "terminalOverviewRuler-cursorForeground": "var(--vscode-terminalOverviewRuler-cursorForeground)", "terminalOverviewRuler-findMatchForeground": "var(--vscode-terminalOverviewRuler-findMatchForeground)", "terminalStickyScroll-background": "var(--vscode-terminalStickyScroll-background)", "terminalStickyScroll-border": "var(--vscode-terminalStickyScroll-border)", "terminalStickyScrollHover-background": "var(--vscode-terminalStickyScrollHover-background)", "terminalSymbolIcon-aliasForeground": "var(--vscode-terminalSymbolIcon-aliasForeground)", "terminalSymbolIcon-argumentForeground": "var(--vscode-terminalSymbolIcon-argumentForeground)", "terminalSymbolIcon-fileForeground": "var(--vscode-terminalSymbolIcon-fileForeground)", "terminalSymbolIcon-flagForeground": "var(--vscode-terminalSymbolIcon-flagForeground)", "terminalSymbolIcon-folderForeground": "var(--vscode-terminalSymbolIcon-folderForeground)", "terminalSymbolIcon-inlineSuggestionForeground": "var(--vscode-terminalSymbolIcon-inlineSuggestionForeground)", "terminalSymbolIcon-methodForeground": "var(--vscode-terminalSymbolIcon-methodForeground)", "terminalSymbolIcon-optionForeground": "var(--vscode-terminalSymbolIcon-optionForeground)", "terminalSymbolIcon-optionValueForeground": "var(--vscode-terminalSymbolIcon-optionValueForeground)", "testing-coverCountBadgeBackground": "var(--vscode-testing-coverCountBadgeBackground)", "testing-coverCountBadgeForeground": "var(--vscode-testing-coverCountBadgeForeground)", "testing-coveredBackground": "var(--vscode-testing-coveredBackground)", "testing-coveredBorder": "var(--vscode-testing-coveredBorder)", "testing-coveredGutterBackground": "var(--vscode-testing-coveredGutterBackground)", "testing-iconErrored": "var(--vscode-testing-iconErrored)", "testing-iconErrored-retired": "var(--vscode-testing-iconErrored-retired)", "testing-iconFailed": "var(--vscode-testing-iconFailed)", "testing-iconFailed-retired": "var(--vscode-testing-iconFailed-retired)", "testing-iconPassed": "var(--vscode-testing-iconPassed)", "testing-iconPassed-retired": "var(--vscode-testing-iconPassed-retired)", "testing-iconQueued": "var(--vscode-testing-iconQueued)", "testing-iconQueued-retired": "var(--vscode-testing-iconQueued-retired)", "testing-iconSkipped": "var(--vscode-testing-iconSkipped)", "testing-iconSkipped-retired": "var(--vscode-testing-iconSkipped-retired)", "testing-iconUnset": "var(--vscode-testing-iconUnset)", "testing-iconUnset-retired": "var(--vscode-testing-iconUnset-retired)", "testing-message-error-badgeBackground": "var(--vscode-testing-message-error-badgeBackground)", "testing-message-error-badgeBorder": "var(--vscode-testing-message-error-badgeBorder)", "testing-message-error-badgeForeground": "var(--vscode-testing-message-error-badgeForeground)", "testing-message-error-lineBackground": "var(--vscode-testing-message-error-lineBackground)", "testing-message-info-decorationForeground": "var(--vscode-testing-message-info-decorationForeground)", "testing-message-info-lineBackground": "var(--vscode-testing-message-info-lineBackground)", "testing-messagePeekBorder": "var(--vscode-testing-messagePeekBorder)", "testing-messagePeekHeaderBackground": "var(--vscode-testing-messagePeekHeaderBackground)", "testing-peekBorder": "var(--vscode-testing-peekBorder)", "testing-peekHeaderBackground": "var(--vscode-testing-peekHeaderBackground)", "testing-runAction": "var(--vscode-testing-runAction)", "testing-uncoveredBackground": "var(--vscode-testing-uncoveredBackground)", "testing-uncoveredBorder": "var(--vscode-testing-uncoveredBorder)", "testing-uncoveredBranchBackground": "var(--vscode-testing-uncoveredBranchBackground)", "testing-uncoveredGutterBackground": "var(--vscode-testing-uncoveredGutterBackground)", "textBlockQuote-background": "var(--vscode-textBlockQuote-background)", "textBlockQuote-border": "var(--vscode-textBlockQuote-border)", "textCodeBlock-background": "var(--vscode-textCodeBlock-background)", "textLink-activeForeground": "var(--vscode-textLink-activeForeground)", "textLink-foreground": "var(--vscode-textLink-foreground)", "textPreformat-background": "var(--vscode-textPreformat-background)", "textPreformat-foreground": "var(--vscode-textPreformat-foreground)", "textSeparator-foreground": "var(--vscode-textSeparator-foreground)", "titleBar-activeBackground": "var(--vscode-titleBar-activeBackground)", "titleBar-activeForeground": "var(--vscode-titleBar-activeForeground)", "titleBar-border": "var(--vscode-titleBar-border)", "titleBar-inactiveBackground": "var(--vscode-titleBar-inactiveBackground)", "titleBar-inactiveForeground": "var(--vscode-titleBar-inactiveForeground)", "toolbar-activeBackground": "var(--vscode-toolbar-activeBackground)", "toolbar-hoverBackground": "var(--vscode-toolbar-hoverBackground)", "toolbar-hoverOutline": "var(--vscode-toolbar-hoverOutline)", "tree-inactiveIndentGuidesStroke": "var(--vscode-tree-inactiveIndentGuidesStroke)", "tree-indentGuidesStroke": "var(--vscode-tree-indentGuidesStroke)", "tree-tableColumnsBorder": "var(--vscode-tree-tableColumnsBorder)", "tree-tableOddRowsBackground": "var(--vscode-tree-tableOddRowsBackground)", "walkThrough-embeddedEditorBackground": "var(--vscode-walkThrough-embeddedEditorBackground)", "walkthrough-stepTitle-foreground": "var(--vscode-walkthrough-stepTitle-foreground)", "welcomePage-background": "var(--vscode-welcomePage-background)", "welcomePage-progress-background": "var(--vscode-welcomePage-progress-background)", "welcomePage-progress-foreground": "var(--vscode-welcomePage-progress-foreground)", "welcomePage-tileBackground": "var(--vscode-welcomePage-tileBackground)", "welcomePage-tileBorder": "var(--vscode-welcomePage-tileBorder)", "welcomePage-tileHoverBackground": "var(--vscode-welcomePage-tileHoverBackground)", "widget-border": "var(--vscode-widget-border)", "widget-shadow": "var(--vscode-widget-shadow)", "window-activeBorder": "var(--vscode-window-activeBorder)", "window-inactiveBorder": "var(--vscode-window-inactiveBorder)"}