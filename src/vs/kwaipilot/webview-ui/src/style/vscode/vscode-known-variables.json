{"colors": ["--vscode-actionBar-toggledBackground", "--vscode-activityBar-activeBackground", "--vscode-activityBar-activeBorder", "--vscode-activityBar-activeFocusBorder", "--vscode-activityBar-background", "--vscode-activityBar-border", "--vscode-activityBar-dropBorder", "--vscode-activityBar-foreground", "--vscode-activityBar-inactiveForeground", "--vscode-activityBarBadge-background", "--vscode-activityBarBadge-foreground", "--vscode-activityBarTop-activeBackground", "--vscode-activityBarTop-activeBorder", "--vscode-activityBarTop-background", "--vscode-activityBarTop-dropBorder", "--vscode-activityBarTop-foreground", "--vscode-activityBarTop-inactiveForeground", "--vscode-activityErrorBadge-background", "--vscode-activityErrorBadge-foreground", "--vscode-activityWarningBadge-background", "--vscode-activityWarningBadge-foreground", "--vscode-badge-background", "--vscode-badge-foreground", "--vscode-banner-background", "--vscode-banner-foreground", "--vscode-banner-iconForeground", "--vscode-breadcrumb-activeSelectionForeground", "--vscode-breadcrumb-background", "--vscode-breadcrumb-focusForeground", "--vscode-breadcrumb-foreground", "--vscode-breadcrumbPicker-background", "--vscode-button-background", "--vscode-button-border", "--vscode-button-foreground", "--vscode-button-hoverBackground", "--vscode-button-secondaryBackground", "--vscode-button-secondaryForeground", "--vscode-button-secondaryHoverBackground", "--vscode-button-separator", "--vscode-chart-axis", "--vscode-chart-guide", "--vscode-chart-line", "--vscode-charts-blue", "--vscode-charts-foreground", "--vscode-charts-green", "--vscode-charts-lines", "--vscode-charts-orange", "--vscode-charts-purple", "--vscode-charts-red", "--vscode-charts-yellow", "--vscode-chat-avatarBackground", "--vscode-chat-avatarForeground", "--vscode-chat-editedFileForeground", "--vscode-chat-requestBackground", "--vscode-chat-requestBorder", "--vscode-chat-slashCommandBackground", "--vscode-chat-slashCommandForeground", "--vscode-checkbox-background", "--vscode-checkbox-border", "--vscode-checkbox-foreground", "--vscode-checkbox-selectBackground", "--vscode-checkbox-selectBorder", "--vscode-commandCenter-activeBackground", "--vscode-commandCenter-activeBorder", "--vscode-commandCenter-activeForeground", "--vscode-commandCenter-background", "--vscode-commandCenter-border", "--vscode-commandCenter-debuggingBackground", "--vscode-commandCenter-foreground", "--vscode-commandCenter-inactiveBorder", "--vscode-commandCenter-inactiveForeground", "--vscode-commentsView-resolvedIcon", "--vscode-commentsView-unresolvedIcon", "--vscode-contrastActiveBorder", "--vscode-contrastBorder", "--vscode-debugConsole-errorForeground", "--vscode-debugConsole-infoForeground", "--vscode-debugConsole-sourceForeground", "--vscode-debugConsole-warningForeground", "--vscode-debugConsoleInputIcon-foreground", "--vscode-debugExceptionWidget-background", "--vscode-debugExceptionWidget-border", "--vscode-debugIcon-breakpointCurrentStackframeForeground", "--vscode-debugIcon-breakpointDisabledForeground", "--vscode-debugIcon-breakpointForeground", "--vscode-debugIcon-breakpointStackframeForeground", "--vscode-debugIcon-breakpointUnverifiedForeground", "--vscode-debugIcon-continueForeground", "--vscode-debugIcon-disconnectForeground", "--vscode-debugIcon-pauseForeground", "--vscode-debugIcon-restartForeground", "--vscode-debugIcon-startForeground", "--vscode-debugIcon-stepBackForeground", "--vscode-debugIcon-stepIntoForeground", "--vscode-debugIcon-stepOutForeground", "--vscode-debugIcon-stepOverForeground", "--vscode-debugIcon-stopForeground", "--vscode-debugTokenExpression-boolean", "--vscode-debugTokenExpression-error", "--vscode-debugTokenExpression-name", "--vscode-debugTokenExpression-number", "--vscode-debugTokenExpression-string", "--vscode-debugTokenExpression-type", "--vscode-debugTokenExpression-value", "--vscode-debugToolBar-background", "--vscode-debugToolBar-border", "--vscode-debugView-exceptionLabelBackground", "--vscode-debugView-exceptionLabelForeground", "--vscode-debugView-stateLabelBackground", "--vscode-debugView-stateLabelForeground", "--vscode-debugView-valueChangedHighlight", "--vscode-descriptionForeground", "--vscode-diffEditor-border", "--vscode-diffEditor-diagonalFill", "--vscode-diffEditor-insertedLineBackground", "--vscode-diffEditor-insertedTextBackground", "--vscode-diffEditor-insertedTextBorder", "--vscode-diffEditor-move-border", "--vscode-diffEditor-moveActive-border", "--vscode-diffEditor-removedLineBackground", "--vscode-diffEditor-removedTextBackground", "--vscode-diffEditor-removedTextBorder", "--vscode-diffEditor-unchangedCodeBackground", "--vscode-diffEditor-unchangedRegionBackground", "--vscode-diffEditor-unchangedRegionForeground", "--vscode-diffEditor-unchangedRegionShadow", "--vscode-diffEditorGutter-insertedLineBackground", "--vscode-diffEditorGutter-removedLineBackground", "--vscode-diffEditorOverview-insertedForeground", "--vscode-diffEditorOverview-removedForeground", "--vscode-disabledForeground", "--vscode-dropdown-background", "--vscode-dropdown-border", "--vscode-dropdown-foreground", "--vscode-dropdown-listBackground", "--vscode-editor-background", "--vscode-editor-compositionBorder", "--vscode-editor-findMatchBackground", "--vscode-editor-findMatchBorder", "--vscode-editor-findMatchForeground", "--vscode-editor-findMatchHighlightBackground", "--vscode-editor-findMatchHighlightBorder", "--vscode-editor-findMatchHighlightForeground", "--vscode-editor-findRangeHighlightBackground", "--vscode-editor-findRangeHighlightBorder", "--vscode-editor-focusedStackFrameHighlightBackground", "--vscode-editor-foldBack<PERSON>", "--vscode-editor-foldPlaceholderForeground", "--vscode-editor-foreground", "--vscode-editor-hoverHighlightBackground", "--vscode-editor-inactiveSelectionBackground", "--vscode-editor-inlineValuesBackground", "--vscode-editor-inlineValuesForeground", "--vscode-editor-lineHighlightBackground", "--vscode-editor-lineHighlightBorder", "--vscode-editor-linkedEditingBackground", "--vscode-editor-placeholder-foreground", "--vscode-editor-rangeHighlightBackground", "--vscode-editor-rangeHighlightBorder", "--vscode-editor-selection<PERSON><PERSON><PERSON>", "--vscode-editor-selection<PERSON><PERSON><PERSON>", "--vscode-editor-selectionHighlightBackground", "--vscode-editor-selection<PERSON>ighlightBorder", "--vscode-editor-snippetFinalTabstopHighlightBackground", "--vscode-editor-snippetFinalTabstopHighlightBorder", "--vscode-editor-snippetTabstopHighlightBackground", "--vscode-editor-snippetTabstopHighlightBorder", "--vscode-editor-stackFrameHighlightBackground", "--vscode-editor-symbolHighlightBackground", "--vscode-editor-symbol<PERSON>ighlightBorder", "--vscode-editor-wordHighlightBackground", "--vscode-editor-wordHighlightBorder", "--vscode-editor-wordHighlightStrongBackground", "--vscode-editor-wordHighlightStrongBorder", "--vscode-editor-wordHighlightTextBackground", "--vscode-editor-wordHighlightTextBorder", "--vscode-editorActionList-background", "--vscode-editorActionList-focusBackground", "--vscode-editorActionList-focusForeground", "--vscode-editorActionList-foreground", "--vscode-editorActiveLineNumber-foreground", "--vscode-editorBracketHighlight-foreground1", "--vscode-editorBracketHighlight-foreground2", "--vscode-editorBracketHighlight-foreground3", "--vscode-editorBracketHighlight-foreground4", "--vscode-editorBracketHighlight-foreground5", "--vscode-editorBracketHighlight-foreground6", "--vscode-editorBracketHighlight-unexpectedBracket-foreground", "--vscode-editorBracketMatch-background", "--vscode-editorBracketMatch-border", "--vscode-editorBracketPairGuide-activeBackground1", "--vscode-editorBracketPairGuide-activeBackground2", "--vscode-editorBracketPairGuide-activeBackground3", "--vscode-editorBracketPairGuide-activeBackground4", "--vscode-editorBracketPairGuide-activeBackground5", "--vscode-editorBracketPairGuide-activeBackground6", "--vscode-editorBracketPairGuide-background1", "--vscode-editorBracketPairGuide-background2", "--vscode-editorBracketPairGuide-background3", "--vscode-editorBracketPairGuide-background4", "--vscode-editorBracketPairGuide-background5", "--vscode-editorBracketPairGuide-background6", "--vscode-editorCode<PERSON>ens-foreground", "--vscode-editorCommentsWidget-rangeActiveBackground", "--vscode-editorCommentsWidget-rangeBackground", "--vscode-editorCommentsWidget-replyInputBackground", "--vscode-editorCommentsWidget-resolvedBorder", "--vscode-editorCommentsWidget-unresolvedBorder", "--vscode-editor<PERSON><PERSON><PERSON>-background", "--vscode-editor<PERSON><PERSON><PERSON>-foreground", "--vscode-editor<PERSON>rror-background", "--vscode-editorError-border", "--vscode-editor<PERSON><PERSON>r-foreground", "--vscode-editorGhostText-background", "--vscode-editorGhostText-border", "--vscode-editorGhostText-foreground", "--vscode-editorGroup-border", "--vscode-editorGroup-dropBackground", "--vscode-editorGroup-dropIntoPromptBackground", "--vscode-editorGroup-dropIntoPromptBorder", "--vscode-editorGroup-dropIntoPromptForeground", "--vscode-editorGroup-emptyBackground", "--vscode-editorGroup-focusedEmptyBorder", "--vscode-editorGroupHeader-border", "--vscode-editorGroupHeader-noTabsBackground", "--vscode-editor<PERSON>roupHeader-tabsBackground", "--vscode-editorGroupHeader-tabsBorder", "--vscode-editor<PERSON><PERSON>-addedBackground", "--vscode-editor<PERSON><PERSON>-background", "--vscode-editor<PERSON><PERSON>-commentGlyphForeground", "--vscode-editor<PERSON><PERSON>-commentRangeForeground", "--vscode-editor<PERSON><PERSON>-commentUnresolvedGlyphForeground", "--vscode-editor<PERSON><PERSON>-deletedBackground", "--vscode-editor<PERSON><PERSON>-foldingControlForeground", "--vscode-editor<PERSON><PERSON>-itemBackground", "--vscode-editorGutter-itemGlyphForeground", "--vscode-editor<PERSON><PERSON>-modifiedBackground", "--vscode-editorHint-border", "--vscode-editorHint-foreground", "--vscode-editorHoverWidget-background", "--vscode-editorHoverWidget-border", "--vscode-editorHoverWidget-foreground", "--vscode-editorHoverWidget-highlightForeground", "--vscode-editorHoverWidget-statusBarBackground", "--vscode-editorIndentGuide-activeBackground", "--vscode-editorIndentGuide-activeBackground1", "--vscode-editorIndentGuide-activeBackground2", "--vscode-editorIndentGuide-activeBackground3", "--vscode-editorIndentGuide-activeBackground4", "--vscode-editorIndentGuide-activeBackground5", "--vscode-editorIndentGuide-activeBackground6", "--vscode-editor<PERSON>ndentGuide-background", "--vscode-editorIndentGuide-background1", "--vscode-editorIndentGuide-background2", "--vscode-editorIndentGuide-background3", "--vscode-editorIndentGuide-background4", "--vscode-editorIndentGuide-background5", "--vscode-editorIndentGuide-background6", "--vscode-editorInfo-background", "--vscode-editorInfo-border", "--vscode-editorInfo-foreground", "--vscode-editorIn<PERSON><PERSON><PERSON>-background", "--vscode-editorInlayHint-foreground", "--vscode-editorInlayHint-parameterBackground", "--vscode-editorInlayHint-parameterForeground", "--vscode-editorInlayHint-typeBackground", "--vscode-editorInlayHint-typeForeground", "--vscode-editorLightBulb-foreground", "--vscode-editorLightBulbAi-foreground", "--vscode-editorLightBulbAutoFix-foreground", "--vscode-editorLineNumber-activeForeground", "--vscode-editorLineNumber-dimmedForeground", "--vscode-editorLineNumber-foreground", "--vscode-editorLink-activeForeground", "--vscode-editor<PERSON><PERSON><PERSON><PERSON><PERSON>-background", "--vscode-editor<PERSON><PERSON><PERSON><PERSON><PERSON>Error-background", "--vscode-editorMarkerNavigationError-headerBackground", "--vscode-editor<PERSON><PERSON>erNavigationInfo-background", "--vscode-editorMarkerNavigationInfo-headerBackground", "--vscode-editor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-background", "--vscode-editorMarkerNavigationWarning-headerBackground", "--vscode-editorMinimap-inlineChatInserted", "--vscode-editor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-primary-background", "--vscode-editor<PERSON><PERSON>iCursor-primary-foreground", "--vscode-editor<PERSON><PERSON><PERSON><PERSON>ursor-secondary-background", "--vscode-editorMultiCursor-secondary-foreground", "--vscode-editorOverviewRuler-addedForeground", "--vscode-editorOverviewRuler-background", "--vscode-editorOverviewRuler-border", "--vscode-editorOverviewRuler-bracketMatchForeground", "--vscode-editorOverviewRuler-commentForeground", "--vscode-editorOverviewRuler-commentUnresolvedForeground", "--vscode-editorOverviewRuler-commonContentForeground", "--vscode-editorOverviewRuler-currentContentForeground", "--vscode-editorOverviewRuler-deletedForeground", "--vscode-editorOverviewRuler-errorForeground", "--vscode-editorOverviewRuler-findMatchForeground", "--vscode-editorOverviewRuler-incomingContentForeground", "--vscode-editorOverviewRuler-infoForeground", "--vscode-editorOverviewRuler-inlineChatInserted", "--vscode-editorOverviewRuler-inlineChatRemoved", "--vscode-editorOverviewRuler-modifiedForeground", "--vscode-editorOverviewRuler-rangeHighlightForeground", "--vscode-editorOverviewRuler-selectionHighlightForeground", "--vscode-editorOverviewRuler-warningForeground", "--vscode-editorOverviewRuler-wordHighlightForeground", "--vscode-editorOverviewRuler-wordHighlightStrongForeground", "--vscode-editorOverviewRuler-wordHighlightTextForeground", "--vscode-editor<PERSON><PERSON>-background", "--vscode-editor<PERSON><PERSON>r-foreground", "--vscode-editor<PERSON><PERSON><PERSON><PERSON>c<PERSON>-background", "--vscode-editorStickyScroll-border", "--vscode-editor<PERSON><PERSON>yScroll-shadow", "--vscode-editor<PERSON><PERSON>yScroll<PERSON>over-background", "--vscode-editorSuggestWidget-background", "--vscode-editorSuggestWidget-border", "--vscode-editorSuggestWidget-focusHighlightForeground", "--vscode-editorSuggestWidget-foreground", "--vscode-editorSuggestWidget-highlightForeground", "--vscode-editorSuggestWidget-selectedBackground", "--vscode-editorSuggestWidget-selectedForeground", "--vscode-editorSuggestWidget-selectedIconForeground", "--vscode-editorSuggestWidgetStatus-foreground", "--vscode-editor<PERSON><PERSON><PERSON><PERSON>igh<PERSON>-background", "--vscode-editorUnicodeHighlight-border", "--vscode-editorUnnecessaryCode-border", "--vscode-editorUnnecessaryCode-opacity", "--vscode-editor<PERSON><PERSON>ning-background", "--vscode-editor<PERSON><PERSON>ning-border", "--vscode-editor<PERSON><PERSON>ning-foreground", "--vscode-editorWatermark-foreground", "--vscode-editorWhitespace-foreground", "--vscode-editorWidget-background", "--vscode-editorWidget-border", "--vscode-editorWidget-foreground", "--vscode-editorWidget-resizeBorder", "--vscode-errorForeground", "--vscode-extensionBadge-remoteBackground", "--vscode-extensionBadge-remoteForeground", "--vscode-<PERSON><PERSON><PERSON><PERSON>-background", "--vscode-extension<PERSON>utton-foreground", "--vscode-extensionButton-hoverBackground", "--vscode-extension<PERSON>utton-prominentBackground", "--vscode-extensionButton-prominentForeground", "--vscode-extensionButton-prominentHoverBackground", "--vscode-extensionButton-separator", "--vscode-extensionIcon-preReleaseForeground", "--vscode-extensionIcon-privateForeground", "--vscode-extensionIcon-sponsorForeground", "--vscode-extensionIcon-starForeground", "--vscode-extensionIcon-verifiedForeground", "--vscode-focusBorder", "--vscode-foreground", "--vscode-gauge-background", "--vscode-gauge-border", "--vscode-gauge-errorBackground", "--vscode-gauge-errorForeground", "--vscode-gauge-foreground", "--vscode-gauge-warningBackground", "--vscode-gauge-warningForeground", "--vscode-icon-foreground", "--vscode-inlineChat-background", "--vscode-inlineChat-border", "--vscode-inlineChat-foreground", "--vscode-inlineChat-shadow", "--vscode-inlineChatDiff-inserted", "--vscode-inlineChatDiff-removed", "--vscode-inlineChatInput-background", "--vscode-inlineChatInput-border", "--vscode-inlineChatInput-focusBorder", "--vscode-inlineChatInput-placeholderForeground", "--vscode-inlineEdit-gutterIndicator-background", "--vscode-inlineEdit-gutterIndicator-primaryBackground", "--vscode-inlineEdit-gutterIndicator-primaryBorder", "--vscode-inlineEdit-gutterIndicator-primaryForeground", "--vscode-inlineEdit-gutterIndicator-secondaryBackground", "--vscode-inlineEdit-gutterIndicator-secondaryBorder", "--vscode-inlineEdit-gutterIndicator-secondaryForeground", "--vscode-inlineEdit-gutterIndicator-successfulBackground", "--vscode-inlineEdit-gutterIndicator-successfulBorder", "--vscode-inlineEdit-gutterIndicator-successfulForeground", "--vscode-inlineEdit-modifiedBackground", "--vscode-inlineEdit-modifiedBorder", "--vscode-inlineEdit-modifiedChangedLineBackground", "--vscode-inlineEdit-modifiedChangedTextBackground", "--vscode-inlineEdit-originalBackground", "--vscode-inlineEdit-originalBorder", "--vscode-inlineEdit-originalChangedLineBackground", "--vscode-inlineEdit-originalChangedTextBackground", "--vscode-inlineEdit-tabWillAcceptModifiedBorder", "--vscode-inlineEdit-tabWillAcceptOriginalBorder", "--vscode-input-background", "--vscode-input-border", "--vscode-input-foreground", "--vscode-input-placeholderForeground", "--vscode-inputOption-activeBackground", "--vscode-inputOption-activeBorder", "--vscode-inputOption-activeForeground", "--vscode-inputOption-hoverBackground", "--vscode-inputValidation-errorBackground", "--vscode-inputValidation-errorBorder", "--vscode-inputValidation-errorForeground", "--vscode-inputValidation-infoBackground", "--vscode-inputValidation-infoBorder", "--vscode-inputValidation-infoForeground", "--vscode-inputValidation-warningBackground", "--vscode-inputValidation-warningBorder", "--vscode-inputValidation-warningForeground", "--vscode-interactive-activeCodeBorder", "--vscode-interactive-inactiveCodeBorder", "--vscode-keybindingLabel-background", "--vscode-keybindingLabel-border", "--vscode-keybindingLabel-bottomBorder", "--vscode-keybindingLabel-foreground", "--vscode-keybindingTable-headerBackground", "--vscode-keybindingTable-rowsBackground", "--vscode-list-activeSelectionBackground", "--vscode-list-activeSelectionForeground", "--vscode-list-activeSelectionIconForeground", "--vscode-list-deemphasizedForeground", "--vscode-list-dropBackground", "--vscode-list-dropBetweenBackground", "--vscode-list-errorForeground", "--vscode-list-filterMatchBackground", "--vscode-list-filterMatchBorder", "--vscode-list-focusAndSelectionOutline", "--vscode-list-focusBackground", "--vscode-list-focusForeground", "--vscode-list-focusHighlightForeground", "--vscode-list-focusOutline", "--vscode-list-highlightForeground", "--vscode-list-hoverBackground", "--vscode-list-hoverForeground", "--vscode-list-inactiveFocusBackground", "--vscode-list-inactiveFocusOutline", "--vscode-list-inactiveSelectionBackground", "--vscode-list-inactiveSelectionForeground", "--vscode-list-inactiveSelectionIconForeground", "--vscode-list-invalidItemForeground", "--vscode-list-warningForeground", "--vscode-listFilterWidget-background", "--vscode-listFilterWidget-noMatchesOutline", "--vscode-listFilterWidget-outline", "--vscode-listFilterWidget-shadow", "--vscode-menu-background", "--vscode-menu-border", "--vscode-menu-foreground", "--vscode-menu-selectionBackground", "--vscode-menu-selectionBorder", "--vscode-menu-selectionForeground", "--vscode-menu-separatorBackground", "--vscode-menubar-selectionBackground", "--vscode-menubar-selectionBorder", "--vscode-menubar-selectionForeground", "--vscode-merge-border", "--vscode-merge-commonContentBackground", "--vscode-merge-commonHeaderBackground", "--vscode-merge-currentContentBackground", "--vscode-merge-currentHeaderBackground", "--vscode-merge-incomingContentBackground", "--vscode-merge-incomingHeaderBackground", "--vscode-mergeEditor-change-background", "--vscode-mergeEditor-change-word-background", "--vscode-mergeEditor-changeBase-background", "--vscode-mergeEditor-changeBase-word-background", "--vscode-mergeEditor-conflict-handled-minimapOverViewRuler", "--vscode-mergeEditor-conflict-handledFocused-border", "--vscode-mergeEditor-conflict-handledUnfocused-border", "--vscode-mergeEditor-conflict-input1-background", "--vscode-mergeEditor-conflict-input2-background", "--vscode-mergeEditor-conflict-unhandled-minimapOverViewRuler", "--vscode-mergeEditor-conflict-unhandledFocused-border", "--vscode-mergeEditor-conflict-unhandledUnfocused-border", "--vscode-mergeEditor-conflictingLines-background", "--vscode-minimap-background", "--vscode-minimap-chatEditHighlight", "--vscode-minimap-errorHighlight", "--vscode-minimap-findMatchHighlight", "--vscode-minimap-foregroundOpacity", "--vscode-minimap-infoHighlight", "--vscode-minimap-selection<PERSON>ighlight", "--vscode-minimap-selectionOccurrenceHighlight", "--vscode-minimap-warningHighlight", "--vscode-minimapGutter-addedBackground", "--vscode-minimapGutter-deletedBackground", "--vscode-minimapGutter-modifiedBackground", "--vscode-minimapSlider-activeBackground", "--vscode-minimapSlider-background", "--vscode-minimapSlider-hoverBackground", "--vscode-multiDiffEditor-background", "--vscode-multiDiffEditor-border", "--vscode-multiDiffEditor-headerBackground", "--vscode-notebook-cellBorderColor", "--vscode-notebook-cellEditorBackground", "--vscode-notebook-cellHoverBackground", "--vscode-notebook-cellInsertionIndicator", "--vscode-notebook-cellStatusBarItemHoverBackground", "--vscode-notebook-cellToolbarSeparator", "--vscode-notebook-editorBackground", "--vscode-notebook-focusedCellBackground", "--vscode-notebook-focusedCellBorder", "--vscode-notebook-focusedEditorBorder", "--vscode-notebook-inactiveFocusedCellBorder", "--vscode-notebook-inactiveSelectedCellBorder", "--vscode-notebook-outputContainerBackgroundColor", "--vscode-notebook-outputContainerBorderColor", "--vscode-notebook-selectedCellBackground", "--vscode-notebook-selectedCellBorder", "--vscode-notebook-symbolHighlightBackground", "--vscode-notebookEditorOverviewRuler-runningCellForeground", "--vscode-notebookScrollbarSlider-activeBackground", "--vscode-notebookScrollbarSlider-background", "--vscode-notebookScrollbarSlider-hoverBackground", "--vscode-notebookStatusErrorIcon-foreground", "--vscode-notebookStatusRunningIcon-foreground", "--vscode-notebookStatusSuccessIcon-foreground", "--vscode-notificationCenter-border", "--vscode-notificationCenterHeader-background", "--vscode-notificationCenterHeader-foreground", "--vscode-notificationLink-foreground", "--vscode-notificationToast-border", "--vscode-notifications-background", "--vscode-notifications-border", "--vscode-notifications-foreground", "--vscode-notificationsErrorIcon-foreground", "--vscode-notificationsInfoIcon-foreground", "--vscode-notificationsWarningIcon-foreground", "--vscode-outputView-background", "--vscode-outputViewStickyScroll-background", "--vscode-panel-background", "--vscode-panel-border", "--vscode-panel-dropBorder", "--vscode-panelInput-border", "--vscode-panelSection-border", "--vscode-panelSection-dropBackground", "--vscode-panelSec<PERSON><PERSON><PERSON>er-background", "--vscode-panelSectionHeader-border", "--vscode-panelSectionHeader-foreground", "--vscode-panelStickyScroll-background", "--vscode-panelStickyScroll-border", "--vscode-panelStickyScroll-shadow", "--vscode-panelTitle-activeBorder", "--vscode-panelTitle-activeForeground", "--vscode-panelTitle-border", "--vscode-panelTitle-inactiveForeground", "--vscode-panelTitleBadge-background", "--vscode-panelTitleBadge-foreground", "--vscode-peekView-border", "--vscode-peek<PERSON><PERSON><PERSON><PERSON><PERSON>or-background", "--vscode-peekViewEditor-matchHighlightBackground", "--vscode-peekViewEditor-matchHighlightBorder", "--vscode-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-background", "--vscode-peekViewEditorStickyScroll-background", "--vscode-<PERSON><PERSON><PERSON><PERSON><PERSON>esult-background", "--vscode-peekViewResult-fileForeground", "--vscode-peekViewResult-lineForeground", "--vscode-peekViewResult-matchHighlightBackground", "--vscode-peekViewResult-selectionBackground", "--vscode-peekViewResult-selectionForeground", "--vscode-peek<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-background", "--vscode-peekViewTitleDescription-foreground", "--vscode-peekViewTitleLabel-foreground", "--vscode-pickerGroup-border", "--vscode-pickerGroup-foreground", "--vscode-ports-iconRunningProcessForeground", "--vscode-problemsErrorIcon-foreground", "--vscode-problemsInfoIcon-foreground", "--vscode-problemsWarningIcon-foreground", "--vscode-profileBadge-background", "--vscode-profileBadge-foreground", "--vscode-profiles-sashBorder", "--vscode-progressBar-background", "--vscode-quickInput-background", "--vscode-quickInput-foreground", "--vscode-quickInput-list-focusBackground", "--vscode-quickInputList-focusBackground", "--vscode-quickInputList-focusForeground", "--vscode-quickInputList-focusIconForeground", "--vscode-quickInputTitle-background", "--vscode-radio-activeBackground", "--vscode-radio-activeBorder", "--vscode-radio-activeForeground", "--vscode-radio-inactiveBackground", "--vscode-radio-inactiveBorder", "--vscode-radio-inactiveForeground", "--vscode-radio-inactiveHoverBackground", "--vscode-sash-hoverBorder", "--vscode-scmGraph-foreground1", "--vscode-scmGraph-foreground2", "--vscode-scmGraph-foreground3", "--vscode-scmGraph-foreground4", "--vscode-scmGraph-foreground5", "--vscode-scmGraph-historyItemBaseRefColor", "--vscode-scmGraph-historyItemHoverAdditionsForeground", "--vscode-scmGraph-historyItemHoverDefaultLabelBackground", "--vscode-scmGraph-historyItemHoverDefaultLabelForeground", "--vscode-scmGraph-historyItemHoverDeletionsForeground", "--vscode-scmGraph-historyItemHoverLabelForeground", "--vscode-scmGraph-historyItemRefColor", "--vscode-scmGraph-historyItemRemoteRefColor", "--vscode-scrollbar-shadow", "--vscode-scrollbarSlider-activeBackground", "--vscode-scrollbarSlider-background", "--vscode-scrollbarSlider-hoverBackground", "--vscode-search-resultsInfoForeground", "--vscode-searchEditor-findMatchBackground", "--vscode-searchEditor-findMatchBorder", "--vscode-searchEditor-textInputBorder", "--vscode-selection-background", "--vscode-settings-checkboxBackground", "--vscode-settings-checkboxBorder", "--vscode-settings-checkboxForeground", "--vscode-settings-dropdownBackground", "--vscode-settings-dropdownBorder", "--vscode-settings-dropdownForeground", "--vscode-settings-dropdownListBorder", "--vscode-settings-focusedRowBackground", "--vscode-settings-focusedRowBorder", "--vscode-settings-headerBorder", "--vscode-settings-headerForeground", "--vscode-settings-modifiedItemIndicator", "--vscode-settings-numberInputBackground", "--vscode-settings-numberInputBorder", "--vscode-settings-numberInputForeground", "--vscode-settings-rowHoverBackground", "--vscode-settings-sashBorder", "--vscode-settings-settingsHeaderHoverForeground", "--vscode-settings-textInputBackground", "--vscode-settings-textInputBorder", "--vscode-settings-textInputForeground", "--vscode-sideBar-background", "--vscode-sideBar-border", "--vscode-sideBar-dropBackground", "--vscode-sideBar-foreground", "--vscode-sideBarActivityBarTop-border", "--vscode-sideBarSectionHeader-background", "--vscode-sideBarSectionHeader-border", "--vscode-sideBarSectionHeader-foreground", "--vscode-sideBarStickyScroll-background", "--vscode-sideBarStickyScroll-border", "--vscode-sideBarStickyScroll-shadow", "--vscode-sideBarTitle-background", "--vscode-sideBarTitle-border", "--vscode-sideBarTitle-foreground", "--vscode-sideBySideEditor-horizontalBorder", "--vscode-sideBySideEditor-verticalBorder", "--vscode-simpleFindWidget-sashBorder", "--vscode-statusBar-background", "--vscode-statusBar-border", "--vscode-statusBar-debuggingBackground", "--vscode-statusBar-debuggingBorder", "--vscode-statusBar-debuggingForeground", "--vscode-statusBar-focusBorder", "--vscode-statusBar-foreground", "--vscode-statusBar-noFolderBackground", "--vscode-statusBar-noFolderBorder", "--vscode-statusBar-noFolderForeground", "--vscode-statusBarItem-activeBackground", "--vscode-statusBarItem-compactHoverBackground", "--vscode-statusBarItem-errorBackground", "--vscode-statusBarItem-errorForeground", "--vscode-statusBarItem-errorHoverBackground", "--vscode-statusBarItem-errorHoverForeground", "--vscode-statusBarItem-focusBorder", "--vscode-statusBarItem-hoverBackground", "--vscode-statusBarItem-hoverForeground", "--vscode-statusBarItem-offlineBackground", "--vscode-statusBarItem-offlineForeground", "--vscode-statusBarItem-offlineHoverBackground", "--vscode-statusBarItem-offlineHoverForeground", "--vscode-statusBarItem-prominentBackground", "--vscode-statusBarItem-prominentForeground", "--vscode-statusBarItem-prominentHoverBackground", "--vscode-statusBarItem-prominentHoverForeground", "--vscode-statusBarItem-remoteBackground", "--vscode-statusBarItem-remoteForeground", "--vscode-statusBarItem-remoteHoverBackground", "--vscode-statusBarItem-remoteHoverForeground", "--vscode-statusBarItem-warningBackground", "--vscode-statusBarItem-warningForeground", "--vscode-statusBarItem-warningHoverBackground", "--vscode-statusBarItem-warningHoverForeground", "--vscode-symbolIcon-arrayForeground", "--vscode-symbolIcon-booleanForeground", "--vscode-symbolIcon-classForeground", "--vscode-symbolIcon-colorForeground", "--vscode-symbolIcon-constantForeground", "--vscode-symbolIcon-constructorForeground", "--vscode-symbolIcon-enumeratorForeground", "--vscode-symbolIcon-enumeratorMemberForeground", "--vscode-symbolIcon-eventForeground", "--vscode-symbolIcon-fieldForeground", "--vscode-symbolIcon-fileForeground", "--vscode-symbolIcon-folderForeground", "--vscode-symbolIcon-functionForeground", "--vscode-symbolIcon-interfaceForeground", "--vscode-symbolIcon-keyForeground", "--vscode-symbolIcon-keywordForeground", "--vscode-symbolIcon-methodForeground", "--vscode-symbolIcon-moduleForeground", "--vscode-symbolIcon-namespaceForeground", "--vscode-symbolIcon-nullForeground", "--vscode-symbolIcon-numberForeground", "--vscode-symbolIcon-objectForeground", "--vscode-symbolIcon-operatorForeground", "--vscode-symbolIcon-packageForeground", "--vscode-symbolIcon-propertyForeground", "--vscode-symbolIcon-referenceForeground", "--vscode-symbolIcon-snippetForeground", "--vscode-symbolIcon-stringForeground", "--vscode-symbolIcon-structForeground", "--vscode-symbolIcon-textForeground", "--vscode-symbolIcon-typeParameterForeground", "--vscode-symbolIcon-unitForeground", "--vscode-symbolIcon-variableForeground", "--vscode-tab-activeBackground", "--vscode-tab-activeBorder", "--vscode-tab-activeBorderTop", "--vscode-tab-activeForeground", "--vscode-tab-activeModifiedBorder", "--vscode-tab-border", "--vscode-tab-dragAndDropBorder", "--vscode-tab-hoverBackground", "--vscode-tab-hoverBorder", "--vscode-tab-hoverForeground", "--vscode-tab-inactiveBackground", "--vscode-tab-inactiveForeground", "--vscode-tab-inactiveModifiedBorder", "--vscode-tab-lastPinnedBorder", "--vscode-tab-selectedBackground", "--vscode-tab-selectedBorderTop", "--vscode-tab-selectedForeground", "--vscode-tab-unfocusedActiveBackground", "--vscode-tab-unfocusedActiveBorder", "--vscode-tab-unfocusedActiveBorderTop", "--vscode-tab-unfocusedActiveForeground", "--vscode-tab-unfocusedActiveModifiedBorder", "--vscode-tab-unfocusedHoverBackground", "--vscode-tab-unfocusedHoverBorder", "--vscode-tab-unfocusedHoverForeground", "--vscode-tab-unfocusedInactiveBackground", "--vscode-tab-unfocusedInactiveForeground", "--vscode-tab-unfocusedInactiveModifiedBorder", "--vscode-terminal-ansiBlack", "--vscode-terminal-ansiBlue", "--vscode-terminal-ansiBrightBlack", "--vscode-terminal-ansiBrightBlue", "--vscode-terminal-ansiBrightCyan", "--vscode-terminal-ansiBrightGreen", "--vscode-terminal-ansiBrightMagenta", "--vscode-terminal-ansiBrightRed", "--vscode-terminal-ansiBrightWhite", "--vscode-terminal-ansiBrightYellow", "--vscode-terminal-ansi<PERSON>yan", "--vscode-terminal-ansiGreen", "--vscode-terminal-ansiMagenta", "--vscode-terminal-ansiRed", "--vscode-terminal-an<PERSON><PERSON><PERSON>e", "--vscode-terminal-an<PERSON><PERSON><PERSON><PERSON>", "--vscode-terminal-background", "--vscode-terminal-border", "--vscode-terminal-dropBackground", "--vscode-terminal-findMatchBackground", "--vscode-terminal-findMatchBorder", "--vscode-terminal-findMatchHighlightBackground", "--vscode-terminal-findMatchHighlightBorder", "--vscode-terminal-foreground", "--vscode-terminal-hoverHighlightBackground", "--vscode-terminal-inactiveSelectionBackground", "--vscode-terminal-initialHintForeground", "--vscode-terminal-selectionBackground", "--vscode-terminal-selectionForeground", "--vscode-terminal-tab-activeBorder", "--vscode-terminalCommandDecoration-defaultBackground", "--vscode-terminalCommandDecoration-errorBackground", "--vscode-terminalCommandDecoration-successBackground", "--vscode-terminalCommandGuide-foreground", "--vscode-terminalCursor-background", "--vscode-terminalCursor-foreground", "--vscode-terminalOverviewRuler-border", "--vscode-terminalOverviewRuler-cursorForeground", "--vscode-terminalOverviewRuler-findMatchForeground", "--vscode-terminalStickyScroll-background", "--vscode-terminalStickyScroll-border", "--vscode-terminalStickyScrollHover-background", "--vscode-terminalSymbolIcon-aliasForeground", "--vscode-terminalSymbolIcon-argumentForeground", "--vscode-terminalSymbolIcon-fileForeground", "--vscode-terminalSymbolIcon-flagForeground", "--vscode-terminalSymbolIcon-folderForeground", "--vscode-terminalSymbolIcon-inlineSuggestionForeground", "--vscode-terminalSymbolIcon-methodForeground", "--vscode-terminalSymbolIcon-optionForeground", "--vscode-terminalSymbolIcon-optionValueForeground", "--vscode-testing-coverCountBadgeBackground", "--vscode-testing-coverCountBadgeForeground", "--vscode-testing-coveredBackground", "--vscode-testing-coveredBorder", "--vscode-testing-coveredGutterBackground", "--vscode-testing-iconErrored", "--vscode-testing-iconErrored-retired", "--vscode-testing-iconFailed", "--vscode-testing-iconFailed-retired", "--vscode-testing-iconPassed", "--vscode-testing-iconPassed-retired", "--vscode-testing-iconQueued", "--vscode-testing-iconQueued-retired", "--vscode-testing-iconSkipped", "--vscode-testing-iconSkipped-retired", "--vscode-testing-iconUnset", "--vscode-testing-icon<PERSON><PERSON>t-retired", "--vscode-testing-message-error-badgeBackground", "--vscode-testing-message-error-badgeBorder", "--vscode-testing-message-error-badgeForeground", "--vscode-testing-message-error-lineBackground", "--vscode-testing-message-info-decorationForeground", "--vscode-testing-message-info-lineBackground", "--vscode-testing-messagePeekBorder", "--vscode-testing-messagePeekHeaderBackground", "--vscode-testing-peekBorder", "--vscode-testing-peekHeaderBackground", "--vscode-testing-runAction", "--vscode-testing-uncoveredBackground", "--vscode-testing-uncoveredBorder", "--vscode-testing-uncoveredBranchBackground", "--vscode-testing-uncoveredGutterBackground", "--vscode-textBlockQuote-background", "--vscode-textBlockQuote-border", "--vscode-textCodeBlock-background", "--vscode-textLink-activeForeground", "--vscode-textLink-foreground", "--vscode-textPreformat-background", "--vscode-textPreformat-foreground", "--vscode-textSeparator-foreground", "--vscode-titleBar-activeBackground", "--vscode-titleBar-activeForeground", "--vscode-titleBar-border", "--vscode-titleBar-inactiveBackground", "--vscode-titleBar-inactiveForeground", "--vscode-toolbar-activeBackground", "--vscode-toolbar-hoverBackground", "--vscode-toolbar-hoverOutline", "--vscode-tree-inactiveIndentGuidesStroke", "--vscode-tree-indentGuidesStroke", "--vscode-tree-tableColumnsBorder", "--vscode-tree-tableOddRowsBackground", "--vscode-walkThrough-embeddedEditorBackground", "--vscode-walkthrough-stepTitle-foreground", "--vscode-welcomePage-background", "--vscode-welcomePage-progress-background", "--vscode-welcomePage-progress-foreground", "--vscode-welcomePage-tileBackground", "--vscode-welcomePage-tileBorder", "--vscode-welcomePage-tileHoverBackground", "--vscode-widget-border", "--vscode-widget-shadow", "--vscode-window-activeBorder", "--vscode-window-inactiveBorder"], "others": ["--background-dark", "--background-light", "--chat-editing-last-edit-shift", "--chat-current-response-min-height", "--dropdown-padding-bottom", "--dropdown-padding-top", "--inline-chat-frame-progress", "--inline-chat-hint-progress", "--insert-border-color", "--last-tab-margin-right", "--monaco-monospace-font", "--monaco-monospace-font", "--notebook-cell-input-preview-font-family", "--notebook-cell-input-preview-font-size", "--notebook-cell-output-font-size", "--notebook-diff-view-viewport-slider", "--notebook-find-horizontal-padding", "--notebook-find-width", "--notebook-editor-font-family", "--notebook-editor-font-size", "--notebook-editor-font-weight", "--outline-element-color", "--separator-border", "--status-border-top-color", "--tab-border-bottom-color", "--tab-border-top-color", "--tab-dirty-border-top-color", "--tabs-border-bottom-color", "--tab-sizing-current-width", "--tab-sizing-fixed-min-width", "--tab-sizing-fixed-max-width", "--editor-group-tab-height", "--editor-group-tabs-height", "--testMessageDecorationFontFamily", "--testMessageDecorationFontSize", "--title-border-bottom-color", "--title-wco-width", "--vscode-chat-list-background", "--vscode-editor<PERSON>ode<PERSON><PERSON>-fontFamily", "--vscode-editor<PERSON>ode<PERSON>ens-fontFamilyDefault", "--vscode-editor<PERSON><PERSON><PERSON><PERSON>-fontFeatureSettings", "--vscode-editor<PERSON>ode<PERSON><PERSON>-fontSize", "--vscode-editorCodeLens-lineHeight", "--vscode-explorer-align-offset-margin-left", "--vscode-hover-max<PERSON>idth", "--vscode-hover-sourceWhiteSpace", "--vscode-hover-whiteSpace", "--vscode-editor-dictation-widget-height", "--vscode-editor-dictation-widget-width", "--vscode-interactive-session-foreground", "--vscode-interactive-result-editor-background-color", "--vscode-repl-font-family", "--vscode-repl-font-size-for-twistie", "--vscode-repl-font-size", "--vscode-repl-line-height", "--vscode-sash-hover-size", "--vscode-sash-size", "--vscode-testing-coverage-lineHeight", "--vscode-editorS<PERSON>yScroll-scrollableWidth", "--vscode-editorStickyScroll-foldingOpacityTransition", "--window-border-color", "--vscode-parameterHintsWidget-editorFontFamily", "--vscode-parameterHintsWidget-editorFontFamilyDefault", "--workspace-trust-check-color", "--workspace-trust-selected-color", "--workspace-trust-unselected-color", "--workspace-trust-x-color", "--z-index-notebook-cell-bottom-toolbar-container", "--z-index-notebook-cell-editor-outline", "--z-index-notebook-cell-expand-part-button", "--z-index-notebook-cell-output-toolbar", "--z-index-notebook-cell-status", "--z-index-notebook-cell-toolbar-dropdown-active", "--z-index-notebook-cell-toolbar", "--z-index-notebook-folding-indicator", "--z-index-notebook-input-collapse-condicon", "--z-index-notebook-list-insertion-indicator", "--z-index-notebook-output", "--z-index-notebook-progress-bar", "--z-index-notebook-scrollbar", "--z-index-run-button-container", "--zoom-factor", "--test-bar-width", "--widget-color", "--text-link-decoration", "--vscode-action-item-auto-timeout", "--monaco-editor-warning-decoration", "--animation-opacity"]}