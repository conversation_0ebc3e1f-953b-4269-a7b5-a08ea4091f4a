.default-theme.light {
  --vscode-actionBar-toggledBackground: #dddddd;
--vscode-activityBar-activeBorder: #005fb8;
--vscode-activityBar-background: #f8f8f8;
--vscode-activityBar-border: #e5e5e5;
--vscode-activityBar-foreground: #1f1f1f;
--vscode-activityBar-inactiveForeground: #616161;
--vscode-activityBarBadge-background: #005fb8;
--vscode-activityBarBadge-foreground: #ffffff;
--vscode-badge-background: #cccccc;
--vscode-badge-foreground: #3b3b3b;
--vscode-button-background: #005fb8;
--vscode-button-border: #0000001a;
--vscode-button-foreground: #ffffff;
--vscode-button-hoverBackground: #0258a8;
--vscode-button-secondaryBackground: #e5e5e5;
--vscode-button-secondaryForeground: #3b3b3b;
--vscode-button-secondaryHoverBackground: #cccccc;
--vscode-chat-editedFileForeground: #895503;
--vscode-chat-slashCommandBackground: #d2ecff;
--vscode-chat-slashCommandForeground: #306ca2;
--vscode-checkbox-background: #f8f8f8;
--vscode-checkbox-border: #cecece;
--vscode-descriptionForeground: #3b3b3b;
--vscode-diffEditor-unchangedRegionBackground: #f8f8f8;
--vscode-dropdown-background: #ffffff;
--vscode-dropdown-border: #cecece;
--vscode-dropdown-foreground: #3b3b3b;
--vscode-dropdown-listBackground: #ffffff;
--vscode-editor-background: #ffffff;
--vscode-editor-foreground: #3b3b3b;
--vscode-editor-inactiveSelectionBackground: #e5ebf1;
--vscode-editor-selectionHighlightBackground: #add6ff80;
--vscode-editorGroup-border: #e5e5e5;
--vscode-editorGroupHeader-tabsBackground: #f8f8f8;
--vscode-editorGroupHeader-tabsBorder: #e5e5e5;
--vscode-editorGutter-addedBackground: #2ea043;
--vscode-editorGutter-deletedBackground: #f85149;
--vscode-editorGutter-modifiedBackground: #005fb8;
--vscode-editorIndentGuide-activeBackground1: #939393;
--vscode-editorIndentGuide-background1: #d3d3d3;
--vscode-editorLineNumber-activeForeground: #171184;
--vscode-editorLineNumber-foreground: #6e7681;
--vscode-editorOverviewRuler-border: #e5e5e5;
--vscode-editorSuggestWidget-background: #f8f8f8;
--vscode-editorWidget-background: #f8f8f8;
--vscode-errorForeground: #f85149;
--vscode-focusBorder: #005fb8;
--vscode-foreground: #3b3b3b;
--vscode-icon-foreground: #3b3b3b;
--vscode-input-background: #ffffff;
--vscode-input-border: #cecece;
--vscode-input-foreground: #3b3b3b;
--vscode-input-placeholderForeground: #767676;
--vscode-inputOption-activeBackground: #bed6ed;
--vscode-inputOption-activeBorder: #005fb8;
--vscode-inputOption-activeForeground: #000000;
--vscode-keybindingLabel-foreground: #3b3b3b;
--vscode-list-activeSelectionBackground: #e8e8e8;
--vscode-list-activeSelectionForeground: #000000;
--vscode-list-activeSelectionIconForeground: #000000;
--vscode-list-focusAndSelectionOutline: #005fb8;
--vscode-list-hoverBackground: #f2f2f2;
--vscode-menu-border: #cecece;
--vscode-menu-selectionBackground: #005fb8;
--vscode-menu-selectionForeground: #ffffff;
--vscode-notebook-cellBorderColor: #e5e5e5;
--vscode-notebook-selectedCellBackground: #c8ddf150;
--vscode-notificationCenterHeader-background: #ffffff;
--vscode-notificationCenterHeader-foreground: #3b3b3b;
--vscode-notifications-background: #ffffff;
--vscode-notifications-border: #e5e5e5;
--vscode-notifications-foreground: #3b3b3b;
--vscode-panel-background: #f8f8f8;
--vscode-panel-border: #e5e5e5;
--vscode-panelInput-border: #e5e5e5;
--vscode-panelTitle-activeBorder: #005fb8;
--vscode-panelTitle-activeForeground: #3b3b3b;
--vscode-panelTitle-inactiveForeground: #3b3b3b;
--vscode-peekViewEditor-matchHighlightBackground: #bb800966;
--vscode-peekViewResult-background: #ffffff;
--vscode-peekViewResult-matchHighlightBackground: #bb800966;
--vscode-pickerGroup-border: #e5e5e5;
--vscode-pickerGroup-foreground: #8b949e;
--vscode-ports-iconRunningProcessForeground: #369432;
--vscode-progressBar-background: #005fb8;
--vscode-quickInput-background: #f8f8f8;
--vscode-quickInput-foreground: #3b3b3b;
--vscode-searchEditor-textInputBorder: #cecece;
--vscode-settings-dropdownBackground: #ffffff;
--vscode-settings-dropdownBorder: #cecece;
--vscode-settings-headerForeground: #1f1f1f;
--vscode-settings-modifiedItemIndicator: #bb800966;
--vscode-settings-numberInputBorder: #cecece;
--vscode-settings-textInputBorder: #cecece;
--vscode-sideBar-background: #f8f8f8;
--vscode-sideBar-border: #e5e5e5;
--vscode-sideBar-foreground: #3b3b3b;
--vscode-sideBarSectionHeader-background: #f8f8f8;
--vscode-sideBarSectionHeader-border: #e5e5e5;
--vscode-sideBarSectionHeader-foreground: #3b3b3b;
--vscode-sideBarTitle-foreground: #3b3b3b;
--vscode-statusBar-background: #f8f8f8;
--vscode-statusBar-border: #e5e5e5;
--vscode-statusBar-debuggingBackground: #fd716c;
--vscode-statusBar-debuggingForeground: #000000;
--vscode-statusBar-focusBorder: #005fb8;
--vscode-statusBar-foreground: #3b3b3b;
--vscode-statusBar-noFolderBackground: #f8f8f8;
--vscode-statusBarItem-compactHoverBackground: #cccccc;
--vscode-statusBarItem-errorBackground: #c72e0f;
--vscode-statusBarItem-focusBorder: #005fb8;
--vscode-statusBarItem-hoverBackground: #b8b8b850;
--vscode-statusBarItem-prominentBackground: #6e768166;
--vscode-statusBarItem-remoteBackground: #005fb8;
--vscode-statusBarItem-remoteForeground: #ffffff;
--vscode-tab-activeBackground: #ffffff;
--vscode-tab-activeBorder: #f8f8f8;
--vscode-tab-activeBorderTop: #005fb8;
--vscode-tab-activeForeground: #3b3b3b;
--vscode-tab-border: #e5e5e5;
--vscode-tab-hoverBackground: #ffffff;
--vscode-tab-inactiveBackground: #f8f8f8;
--vscode-tab-inactiveForeground: #868686;
--vscode-tab-lastPinnedBorder: #d4d4d4;
--vscode-tab-selectedBackground: #ffffffa5;
--vscode-tab-selectedBorderTop: #68a3da;
--vscode-tab-selectedForeground: #333333b3;
--vscode-tab-unfocusedActiveBorder: #f8f8f8;
--vscode-tab-unfocusedActiveBorderTop: #e5e5e5;
--vscode-tab-unfocusedHoverBackground: #f8f8f8;
--vscode-terminal-foreground: #3b3b3b;
--vscode-terminal-inactiveSelectionBackground: #e5ebf1;
--vscode-terminal-tab-activeBorder: #005fb8;
--vscode-terminalCursor-foreground: #005fb8;
--vscode-textBlockQuote-background: #f8f8f8;
--vscode-textBlockQuote-border: #e5e5e5;
--vscode-textCodeBlock-background: #f8f8f8;
--vscode-textLink-activeForeground: #005fb8;
--vscode-textLink-foreground: #005fb8;
--vscode-textPreformat-background: #0000001f;
--vscode-textPreformat-foreground: #3b3b3b;
--vscode-textSeparator-foreground: #21262d;
--vscode-titleBar-activeBackground: #f8f8f8;
--vscode-titleBar-activeForeground: #1e1e1e;
--vscode-titleBar-border: #e5e5e5;
--vscode-titleBar-inactiveBackground: #f8f8f8;
--vscode-titleBar-inactiveForeground: #8b949e;
--vscode-welcomePage-tileBackground: #f3f3f3;
--vscode-widget-border: #e5e5e5;
--vscode-activityBar-dropBorder: #1f1f1f;
--vscode-activityBarTop-activeBorder: #424242;
--vscode-activityBarTop-dropBorder: #424242;
--vscode-activityBarTop-foreground: #424242;
--vscode-activityBarTop-inactiveForeground: #424242bf;
--vscode-activityErrorBadge-background: #e51400;
--vscode-activityErrorBadge-foreground: #ffffff;
--vscode-activityWarningBadge-background: #bf8803;
--vscode-activityWarningBadge-foreground: #ffffff;
--vscode-banner-background: #a2a2a2;
--vscode-banner-foreground: #000000;
--vscode-banner-iconForeground: #1a85ff;
--vscode-breadcrumb-activeSelectionForeground: #2f2f2f;
--vscode-breadcrumb-background: #ffffff;
--vscode-breadcrumb-focusForeground: #2f2f2f;
--vscode-breadcrumb-foreground: #3b3b3bcc;
--vscode-breadcrumbPicker-background: #f8f8f8;
--vscode-button-separator: #ffffff66;
--vscode-chart-axis: #00000099;
--vscode-chart-guide: #00000033;
--vscode-chart-line: #236b8e;
--vscode-charts-blue: #1a85ff;
--vscode-charts-foreground: #3b3b3b;
--vscode-charts-green: #388a34;
--vscode-charts-lines: #3b3b3b80;
--vscode-charts-orange: #d18616;
--vscode-charts-purple: #652d90;
--vscode-charts-red: #e51400;
--vscode-charts-yellow: #bf8803;
--vscode-chat-avatarBackground: #f2f2f2;
--vscode-chat-avatarForeground: #3b3b3b;
--vscode-chat-requestBackground: #ffffff9e;
--vscode-chat-requestBorder: #0000001a;
--vscode-checkbox-disabled-background: #b9b9b9;
--vscode-checkbox-disabled-foreground: #797979;
--vscode-checkbox-foreground: #3b3b3b;
--vscode-checkbox-selectBackground: #f8f8f8;
--vscode-checkbox-selectBorder: #3b3b3b;
--vscode-commandCenter-activeBackground: #00000014;
--vscode-commandCenter-activeBorder: #1e1e1e4d;
--vscode-commandCenter-activeForeground: #1e1e1e;
--vscode-commandCenter-background: #0000000d;
--vscode-commandCenter-border: #1e1e1e33;
--vscode-commandCenter-debuggingBackground: #fd716c42;
--vscode-commandCenter-foreground: #1e1e1e;
--vscode-commandCenter-inactiveBorder: #8b949e40;
--vscode-commandCenter-inactiveForeground: #8b949e;
--vscode-commentsView-resolvedIcon: #61616180;
--vscode-commentsView-unresolvedIcon: #005fb8;
--vscode-debugConsole-errorForeground: #f85149;
--vscode-debugConsole-infoForeground: #1a85ff;
--vscode-debugConsole-sourceForeground: #3b3b3b;
--vscode-debugConsole-warningForeground: #bf8803;
--vscode-debugConsoleInputIcon-foreground: #3b3b3b;
--vscode-debugExceptionWidget-background: #f1dfde;
--vscode-debugExceptionWidget-border: #a31515;
--vscode-debugIcon-breakpointCurrentStackframeForeground: #be8700;
--vscode-debugIcon-breakpointDisabledForeground: #848484;
--vscode-debugIcon-breakpointForeground: #e51400;
--vscode-debugIcon-breakpointStackframeForeground: #89d185;
--vscode-debugIcon-breakpointUnverifiedForeground: #848484;
--vscode-debugIcon-continueForeground: #007acc;
--vscode-debugIcon-disconnectForeground: #a1260d;
--vscode-debugIcon-pauseForeground: #007acc;
--vscode-debugIcon-restartForeground: #388a34;
--vscode-debugIcon-startForeground: #388a34;
--vscode-debugIcon-stepBackForeground: #007acc;
--vscode-debugIcon-stepIntoForeground: #007acc;
--vscode-debugIcon-stepOutForeground: #007acc;
--vscode-debugIcon-stepOverForeground: #007acc;
--vscode-debugIcon-stopForeground: #a1260d;
--vscode-debugTokenExpression-boolean: #0000ff;
--vscode-debugTokenExpression-error: #e51400;
--vscode-debugTokenExpression-name: #9b46b0;
--vscode-debugTokenExpression-number: #098658;
--vscode-debugTokenExpression-string: #a31515;
--vscode-debugTokenExpression-type: #4a90e2;
--vscode-debugTokenExpression-value: #6c6c6ccc;
--vscode-debugToolBar-background: #f3f3f3;
--vscode-debugView-exceptionLabelBackground: #a31515;
--vscode-debugView-exceptionLabelForeground: #ffffff;
--vscode-debugView-stateLabelBackground: #88888844;
--vscode-debugView-stateLabelForeground: #3b3b3b;
--vscode-debugView-valueChangedHighlight: #569cd6;
--vscode-diffEditor-diagonalFill: #22222233;
--vscode-diffEditor-insertedLineBackground: #9bb95533;
--vscode-diffEditor-insertedTextBackground: #9ccc2c40;
--vscode-diffEditor-move-border: #8b8b8b9c;
--vscode-diffEditor-moveActive-border: #ffa500;
--vscode-diffEditor-removedLineBackground: #ff000033;
--vscode-diffEditor-removedTextBackground: #ff000033;
--vscode-diffEditor-unchangedCodeBackground: #b8b8b829;
--vscode-diffEditor-unchangedRegionForeground: #3b3b3b;
--vscode-diffEditor-unchangedRegionShadow: #737373bf;
--vscode-disabledForeground: #61616180;
--vscode-editor-compositionBorder: #000000;
--vscode-editor-findMatchBackground: #a8ac94;
--vscode-editor-findMatchHighlightBackground: #ea5c0055;
--vscode-editor-findRangeHighlightBackground: #b4b4b44d;
--vscode-editor-focusedStackFrameHighlightBackground: #cee7ce73;
--vscode-editor-foldBackground: #add6ff4d;
--vscode-editor-foldPlaceholderForeground: #808080;
--vscode-editor-hoverHighlightBackground: #add6ff26;
--vscode-editor-inlineValuesBackground: #ffc80033;
--vscode-editor-inlineValuesForeground: #00000080;
--vscode-editor-lineHighlightBorder: #eeeeee;
--vscode-editor-linkedEditingBackground: #ff00004d;
--vscode-editor-placeholder-foreground: #00000077;
--vscode-editor-rangeHighlightBackground: #fdff0033;
--vscode-editor-selectionBackground: #add6ff;
--vscode-editor-snippetFinalTabstopHighlightBorder: #0a326480;
--vscode-editor-snippetTabstopHighlightBackground: #0a326433;
--vscode-editor-stackFrameHighlightBackground: #ffff6673;
--vscode-editor-symbolHighlightBackground: #ea5c0055;
--vscode-editor-wordHighlightBackground: #57575740;
--vscode-editor-wordHighlightStrongBackground: #0e639c40;
--vscode-editor-wordHighlightTextBackground: #57575740;
--vscode-editorActionList-background: #f8f8f8;
--vscode-editorActionList-focusBackground: #e8e8e8;
--vscode-editorActionList-focusForeground: #000000;
--vscode-editorActionList-foreground: #3b3b3b;
--vscode-editorBracketHighlight-foreground1: #0431fa;
--vscode-editorBracketHighlight-foreground2: #319331;
--vscode-editorBracketHighlight-foreground3: #7b3814;
--vscode-editorBracketHighlight-foreground4: #00000000;
--vscode-editorBracketHighlight-foreground5: #00000000;
--vscode-editorBracketHighlight-foreground6: #00000000;
--vscode-editorBracketHighlight-unexpectedBracket-foreground: #ff1212cc;
--vscode-editorBracketMatch-background: #0064001a;
--vscode-editorBracketMatch-border: #b9b9b9;
--vscode-editorBracketPairGuide-activeBackground1: #00000000;
--vscode-editorBracketPairGuide-activeBackground2: #00000000;
--vscode-editorBracketPairGuide-activeBackground3: #00000000;
--vscode-editorBracketPairGuide-activeBackground4: #00000000;
--vscode-editorBracketPairGuide-activeBackground5: #00000000;
--vscode-editorBracketPairGuide-activeBackground6: #00000000;
--vscode-editorBracketPairGuide-background1: #00000000;
--vscode-editorBracketPairGuide-background2: #00000000;
--vscode-editorBracketPairGuide-background3: #00000000;
--vscode-editorBracketPairGuide-background4: #00000000;
--vscode-editorBracketPairGuide-background5: #00000000;
--vscode-editorBracketPairGuide-background6: #00000000;
--vscode-editorCodeLens-foreground: #919191;
--vscode-editorCommentsWidget-rangeActiveBackground: #005fb81a;
--vscode-editorCommentsWidget-rangeBackground: #005fb81a;
--vscode-editorCommentsWidget-replyInputBackground: #f3f3f3;
--vscode-editorCommentsWidget-resolvedBorder: #61616180;
--vscode-editorCommentsWidget-unresolvedBorder: #005fb8;
--vscode-editorCursor-foreground: #000000;
--vscode-editorError-foreground: #e51400;
--vscode-editorGhostText-foreground: #00000077;
--vscode-editorGroup-dropBackground: #2677cb2e;
--vscode-editorGroup-dropIntoPromptBackground: #f8f8f8;
--vscode-editorGroup-dropIntoPromptForeground: #3b3b3b;
--vscode-editorGroupHeader-noTabsBackground: #ffffff;
--vscode-editorGutter-addedSecondaryBackground: #83db93;
--vscode-editorGutter-background: #ffffff;
--vscode-editorGutter-commentGlyphForeground: #3b3b3b;
--vscode-editorGutter-commentRangeForeground: #d5d8e9;
--vscode-editorGutter-commentUnresolvedGlyphForeground: #3b3b3b;
--vscode-editorGutter-deletedSecondaryBackground: #fcaaa6;
--vscode-editorGutter-foldingControlForeground: #3b3b3b;
--vscode-editorGutter-itemBackground: #d5d8e9;
--vscode-editorGutter-itemGlyphForeground: #3b3b3b;
--vscode-editorGutter-modifiedSecondaryBackground: #3aa0ff;
--vscode-editorHint-foreground: #6c6c6c;
--vscode-editorHoverWidget-background: #f8f8f8;
--vscode-editorHoverWidget-border: #c8c8c8;
--vscode-editorHoverWidget-foreground: #3b3b3b;
--vscode-editorHoverWidget-highlightForeground: #0066bf;
--vscode-editorHoverWidget-statusBarBackground: #ececec;
--vscode-editorIndentGuide-activeBackground2: #00000000;
--vscode-editorIndentGuide-activeBackground3: #00000000;
--vscode-editorIndentGuide-activeBackground4: #00000000;
--vscode-editorIndentGuide-activeBackground5: #00000000;
--vscode-editorIndentGuide-activeBackground6: #00000000;
--vscode-editorIndentGuide-background2: #00000000;
--vscode-editorIndentGuide-background3: #00000000;
--vscode-editorIndentGuide-background4: #00000000;
--vscode-editorIndentGuide-background5: #00000000;
--vscode-editorIndentGuide-background6: #00000000;
--vscode-editorInfo-foreground: #1a85ff;
--vscode-editorInlayHint-background: #cccccc1a;
--vscode-editorInlayHint-foreground: #969696;
--vscode-editorInlayHint-parameterBackground: #cccccc1a;
--vscode-editorInlayHint-parameterForeground: #969696;
--vscode-editorInlayHint-typeBackground: #cccccc1a;
--vscode-editorInlayHint-typeForeground: #969696;
--vscode-editorLightBulb-foreground: #ddb100;
--vscode-editorLightBulbAi-foreground: #ddb100;
--vscode-editorLightBulbAutoFix-foreground: #007acc;
--vscode-editorLink-activeForeground: #0000ff;
--vscode-editorMarkerNavigation-background: #ffffff;
--vscode-editorMarkerNavigationError-background: #e51400;
--vscode-editorMarkerNavigationError-headerBackground: #e514001a;
--vscode-editorMarkerNavigationInfo-background: #1a85ff;
--vscode-editorMarkerNavigationInfo-headerBackground: #1a85ff1a;
--vscode-editorMarkerNavigationWarning-background: #bf8803;
--vscode-editorMarkerNavigationWarning-headerBackground: #bf88031a;
--vscode-editorMinimap-inlineChatInserted: #9ccc2c33;
--vscode-editorMultiCursor-primary-foreground: #000000;
--vscode-editorMultiCursor-secondary-foreground: #000000;
--vscode-editorOverviewRuler-addedForeground: #2ea04399;
--vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;
--vscode-editorOverviewRuler-commentForeground: #d5d8e9;
--vscode-editorOverviewRuler-commentUnresolvedForeground: #d5d8e9;
--vscode-editorOverviewRuler-commonContentForeground: #60606066;
--vscode-editorOverviewRuler-currentContentForeground: #40c8ae80;
--vscode-editorOverviewRuler-deletedForeground: #f8514999;
--vscode-editorOverviewRuler-errorForeground: #ff1212b3;
--vscode-editorOverviewRuler-findMatchForeground: #d186167e;
--vscode-editorOverviewRuler-incomingContentForeground: #40a6ff80;
--vscode-editorOverviewRuler-infoForeground: #1a85ff;
--vscode-editorOverviewRuler-inlineChatInserted: #9ccc2c33;
--vscode-editorOverviewRuler-inlineChatRemoved: #ff000029;
--vscode-editorOverviewRuler-modifiedForeground: #005fb899;
--vscode-editorOverviewRuler-rangeHighlightForeground: #007acc99;
--vscode-editorOverviewRuler-selectionHighlightForeground: #a0a0a0cc;
--vscode-editorOverviewRuler-warningForeground: #bf8803;
--vscode-editorOverviewRuler-wordHighlightForeground: #a0a0a0cc;
--vscode-editorOverviewRuler-wordHighlightStrongForeground: #c0a0c0cc;
--vscode-editorOverviewRuler-wordHighlightTextForeground: #a0a0a0cc;
--vscode-editorPane-background: #ffffff;
--vscode-editorRuler-foreground: #d3d3d3;
--vscode-editorStickyScroll-background: #ffffff;
--vscode-editorStickyScroll-shadow: #dddddd;
--vscode-editorStickyScrollHover-background: #f0f0f0;
--vscode-editorSuggestWidget-border: #c8c8c8;
--vscode-editorSuggestWidget-focusHighlightForeground: #0066bf;
--vscode-editorSuggestWidget-foreground: #3b3b3b;
--vscode-editorSuggestWidget-highlightForeground: #0066bf;
--vscode-editorSuggestWidget-selectedBackground: #e8e8e8;
--vscode-editorSuggestWidget-selectedForeground: #000000;
--vscode-editorSuggestWidget-selectedIconForeground: #000000;
--vscode-editorSuggestWidgetStatus-foreground: #3b3b3b80;
--vscode-editorUnicodeHighlight-border: #bf8803;
--vscode-editorUnnecessaryCode-opacity: #00000077;
--vscode-editorWarning-foreground: #bf8803;
--vscode-editorWatermark-foreground: #3b3b3bad;
--vscode-editorWhitespace-foreground: #33333333;
--vscode-editorWidget-border: #c8c8c8;
--vscode-editorWidget-foreground: #3b3b3b;
--vscode-extensionBadge-remoteBackground: #005fb8;
--vscode-extensionBadge-remoteForeground: #ffffff;
--vscode-extensionButton-background: #005fb8;
--vscode-extensionButton-foreground: #ffffff;
--vscode-extensionButton-hoverBackground: #0258a8;
--vscode-extensionButton-prominentBackground: #005fb8;
--vscode-extensionButton-prominentForeground: #ffffff;
--vscode-extensionButton-prominentHoverBackground: #0258a8;
--vscode-extensionButton-separator: #ffffff66;
--vscode-extensionIcon-preReleaseForeground: #1d9271;
--vscode-extensionIcon-privateForeground: #00000060;
--vscode-extensionIcon-sponsorForeground: #b51e78;
--vscode-extensionIcon-starForeground: #df6100;
--vscode-extensionIcon-verifiedForeground: #005fb8;
--vscode-gauge-background: #007acc;
--vscode-gauge-errorBackground: #be1100;
--vscode-gauge-errorForeground: #be11004d;
--vscode-gauge-foreground: #007acc4d;
--vscode-gauge-warningBackground: #b89500;
--vscode-gauge-warningForeground: #b895004d;
--vscode-git-blame-editorDecorationForeground: #969696;
--vscode-gitDecoration-addedResourceForeground: #587c0c;
--vscode-gitDecoration-conflictingResourceForeground: #ad0707;
--vscode-gitDecoration-deletedResourceForeground: #ad0707;
--vscode-gitDecoration-ignoredResourceForeground: #8e8e90;
--vscode-gitDecoration-modifiedResourceForeground: #895503;
--vscode-gitDecoration-renamedResourceForeground: #007100;
--vscode-gitDecoration-stageDeletedResourceForeground: #ad0707;
--vscode-gitDecoration-stageModifiedResourceForeground: #895503;
--vscode-gitDecoration-submoduleResourceForeground: #1258a7;
--vscode-gitDecoration-untrackedResourceForeground: #007100;
--vscode-githd-blameView-info: #237893;
--vscode-githd-historyView-author: #001080;
--vscode-githd-historyView-branch: #af00db;
--vscode-githd-historyView-email: #795e26;
--vscode-githd-historyView-filePath: #811f3f;
--vscode-githd-historyView-hash: #a31515;
--vscode-githd-historyView-more: #001080;
--vscode-githd-historyView-ref: #008000;
--vscode-githd-historyView-subject: #0000ff;
--vscode-githd-historyView-title: #267f99;
--vscode-githd-infoView-content: #008000;
--vscode-githd-infoView-new: #09885a;
--vscode-githd-infoView-old: #a31515;
--vscode-githd-infoView-path: #000080;
--vscode-gitlens-closedAutolinkedIssueIconColor: #8250df;
--vscode-gitlens-closedPullRequestIconColor: #cf222e;
--vscode-gitlens-decorations-addedForegroundColor: #587c0c;
--vscode-gitlens-decorations-branchAheadForegroundColor: #35b15e;
--vscode-gitlens-decorations-branchBehindForegroundColor: #b15e35;
--vscode-gitlens-decorations-branchDivergedForegroundColor: #d8af1b;
--vscode-gitlens-decorations-branchMissingUpstreamForegroundColor: #ad0707;
--vscode-gitlens-decorations-branchUnpublishedForegroundColor: #3b3b3b;
--vscode-gitlens-decorations-branchUpToDateForegroundColor: #3b3b3b;
--vscode-gitlens-decorations-copiedForegroundColor: #007100;
--vscode-gitlens-decorations-deletedForegroundColor: #ad0707;
--vscode-gitlens-decorations-ignoredForegroundColor: #8e8e90;
--vscode-gitlens-decorations-modifiedForegroundColor: #895503;
--vscode-gitlens-decorations-renamedForegroundColor: #007100;
--vscode-gitlens-decorations-statusMergingOrRebasingConflictForegroundColor: #ad0707;
--vscode-gitlens-decorations-statusMergingOrRebasingForegroundColor: #d8af1b;
--vscode-gitlens-decorations-untrackedForegroundColor: #007100;
--vscode-gitlens-decorations-workspaceCurrentForegroundColor: #35b15e;
--vscode-gitlens-decorations-workspaceRepoMissingForegroundColor: #949494;
--vscode-gitlens-decorations-workspaceRepoOpenForegroundColor: #35b15e;
--vscode-gitlens-decorations-worktreeHasUncommittedChangesForegroundColor: #895503;
--vscode-gitlens-decorations-worktreeMissingForegroundColor: #ad0707;
--vscode-gitlens-graphChangesColumnAddedColor: #2da44e;
--vscode-gitlens-graphChangesColumnDeletedColor: #cf222e;
--vscode-gitlens-graphLane10Color: #2ece9d;
--vscode-gitlens-graphLane1Color: #15a0bf;
--vscode-gitlens-graphLane2Color: #0669f7;
--vscode-gitlens-graphLane3Color: #8e00c2;
--vscode-gitlens-graphLane4Color: #c517b6;
--vscode-gitlens-graphLane5Color: #d90171;
--vscode-gitlens-graphLane6Color: #cd0101;
--vscode-gitlens-graphLane7Color: #f25d2e;
--vscode-gitlens-graphLane8Color: #f2ca33;
--vscode-gitlens-graphLane9Color: #7bd938;
--vscode-gitlens-graphMinimapMarkerHeadColor: #04c814;
--vscode-gitlens-graphMinimapMarkerHighlightsColor: #f5cc00;
--vscode-gitlens-graphMinimapMarkerLocalBranchesColor: #3095e8;
--vscode-gitlens-graphMinimapMarkerPullRequestsColor: #ff8f18;
--vscode-gitlens-graphMinimapMarkerRemoteBranchesColor: #67ace4;
--vscode-gitlens-graphMinimapMarkerStashesColor: #e467e4;
--vscode-gitlens-graphMinimapMarkerTagsColor: #d2a379;
--vscode-gitlens-graphMinimapMarkerUpstreamColor: #8cd993;
--vscode-gitlens-graphScrollMarkerHeadColor: #04c814;
--vscode-gitlens-graphScrollMarkerHighlightsColor: #f5cc00;
--vscode-gitlens-graphScrollMarkerLocalBranchesColor: #3095e8;
--vscode-gitlens-graphScrollMarkerPullRequestsColor: #ff8f18;
--vscode-gitlens-graphScrollMarkerRemoteBranchesColor: #67ace4;
--vscode-gitlens-graphScrollMarkerStashesColor: #e467e4;
--vscode-gitlens-graphScrollMarkerTagsColor: #d2a379;
--vscode-gitlens-graphScrollMarkerUpstreamColor: #8cd993;
--vscode-gitlens-gutterBackgroundColor: #0000000c;
--vscode-gitlens-gutterForegroundColor: #747474;
--vscode-gitlens-gutterUncommittedForegroundColor: #00bcf299;
--vscode-gitlens-launchpadIndicatorAttentionColor: #cc9b15;
--vscode-gitlens-launchpadIndicatorAttentionHoverColor: #cc9b15;
--vscode-gitlens-launchpadIndicatorBlockedColor: #ad0707;
--vscode-gitlens-launchpadIndicatorBlockedHoverColor: #ad0707;
--vscode-gitlens-launchpadIndicatorMergeableColor: #42c954;
--vscode-gitlens-launchpadIndicatorMergeableHoverColor: #42c954;
--vscode-gitlens-lineHighlightBackgroundColor: #00bcf233;
--vscode-gitlens-lineHighlightOverviewRulerColor: #00bcf299;
--vscode-gitlens-mergedPullRequestIconColor: #8250df;
--vscode-gitlens-openAutolinkedIssueIconColor: #1a7f37;
--vscode-gitlens-openPullRequestIconColor: #1a7f37;
--vscode-gitlens-trailingLineBackgroundColor: #00000000;
--vscode-gitlens-trailingLineForegroundColor: #99999959;
--vscode-gitlens-unpublishedChangesIconColor: #35b15e;
--vscode-gitlens-unpublishedCommitIconColor: #35b15e;
--vscode-gitlens-unpulledChangesIconColor: #b15e35;
--vscode-inlineChat-background: #f8f8f8;
--vscode-inlineChat-border: #c8c8c8;
--vscode-inlineChat-foreground: #3b3b3b;
--vscode-inlineChat-shadow: #00000029;
--vscode-inlineChatDiff-inserted: #9ccc2c20;
--vscode-inlineChatDiff-removed: #ff00001a;
--vscode-inlineChatInput-background: #ffffff;
--vscode-inlineChatInput-border: #c8c8c8;
--vscode-inlineChatInput-focusBorder: #005fb8;
--vscode-inlineChatInput-placeholderForeground: #767676;
--vscode-inlineEdit-gutterIndicator-background: #5f5f5f18;
--vscode-inlineEdit-gutterIndicator-primaryBackground: #005fb880;
--vscode-inlineEdit-gutterIndicator-primaryBorder: #005fb8;
--vscode-inlineEdit-gutterIndicator-primaryForeground: #ffffff;
--vscode-inlineEdit-gutterIndicator-secondaryBackground: #e5e5e5;
--vscode-inlineEdit-gutterIndicator-secondaryBorder: #e5e5e5;
--vscode-inlineEdit-gutterIndicator-secondaryForeground: #3b3b3b;
--vscode-inlineEdit-gutterIndicator-successfulBackground: #005fb8;
--vscode-inlineEdit-gutterIndicator-successfulBorder: #005fb8;
--vscode-inlineEdit-gutterIndicator-successfulForeground: #ffffff;
--vscode-inlineEdit-modifiedBackground: #9ccc2c13;
--vscode-inlineEdit-modifiedBorder: #3e511240;
--vscode-inlineEdit-modifiedChangedLineBackground: #9bb95524;
--vscode-inlineEdit-modifiedChangedTextBackground: #9ccc2c2d;
--vscode-inlineEdit-originalBackground: #ff00000a;
--vscode-inlineEdit-originalBorder: #ff000033;
--vscode-inlineEdit-originalChangedLineBackground: #ff000029;
--vscode-inlineEdit-originalChangedTextBackground: #ff000029;
--vscode-inlineEdit-tabWillAcceptModifiedBorder: #3e511240;
--vscode-inlineEdit-tabWillAcceptOriginalBorder: #ff000033;
--vscode-inputOption-hoverBackground: #b8b8b850;
--vscode-inputValidation-errorBackground: #f2dede;
--vscode-inputValidation-errorBorder: #be1100;
--vscode-inputValidation-infoBackground: #d6ecf2;
--vscode-inputValidation-infoBorder: #007acc;
--vscode-inputValidation-warningBackground: #f6f5d2;
--vscode-inputValidation-warningBorder: #b89500;
--vscode-interactive-activeCodeBorder: #007acc;
--vscode-interactive-inactiveCodeBorder: #e4e6f1;
--vscode-keybindingLabel-background: #dddddd66;
--vscode-keybindingLabel-border: #cccccc66;
--vscode-keybindingLabel-bottomBorder: #bbbbbb66;
--vscode-keybindingTable-headerBackground: #3b3b3b0a;
--vscode-keybindingTable-rowsBackground: #3b3b3b0a;
--vscode-list-deemphasizedForeground: #8e8e90;
--vscode-list-dropBackground: #d6ebff;
--vscode-list-dropBetweenBackground: #3b3b3b;
--vscode-list-errorForeground: #b01011;
--vscode-list-filterMatchBackground: #ea5c0055;
--vscode-list-focusHighlightForeground: #0066bf;
--vscode-list-focusOutline: #005fb8;
--vscode-list-highlightForeground: #0066bf;
--vscode-list-inactiveSelectionBackground: #e4e6f1;
--vscode-list-invalidItemForeground: #b89500;
--vscode-list-warningForeground: #855f00;
--vscode-listFilterWidget-background: #f8f8f8;
--vscode-listFilterWidget-noMatchesOutline: #be1100;
--vscode-listFilterWidget-outline: #00000000;
--vscode-listFilterWidget-shadow: #00000029;
--vscode-menu-background: #ffffff;
--vscode-menu-foreground: #3b3b3b;
--vscode-menu-separatorBackground: #d4d4d4;
--vscode-menubar-selectionBackground: #b8b8b850;
--vscode-menubar-selectionForeground: #1e1e1e;
--vscode-merge-commonContentBackground: #60606029;
--vscode-merge-commonHeaderBackground: #60606066;
--vscode-merge-currentContentBackground: #40c8ae33;
--vscode-merge-currentHeaderBackground: #40c8ae80;
--vscode-merge-incomingContentBackground: #40a6ff33;
--vscode-merge-incomingHeaderBackground: #40a6ff80;
--vscode-mergeEditor-change-background: #9bb95533;
--vscode-mergeEditor-change-word-background: #9ccc2c66;
--vscode-mergeEditor-changeBase-background: #ffcccc;
--vscode-mergeEditor-changeBase-word-background: #ffa3a3;
--vscode-mergeEditor-conflict-handled-minimapOverViewRuler: #adaca8ee;
--vscode-mergeEditor-conflict-handledFocused-border: #c1c1c1cc;
--vscode-mergeEditor-conflict-handledUnfocused-border: #86868649;
--vscode-mergeEditor-conflict-input1-background: #40c8ae33;
--vscode-mergeEditor-conflict-input2-background: #40a6ff33;
--vscode-mergeEditor-conflict-unhandled-minimapOverViewRuler: #fcba03;
--vscode-mergeEditor-conflict-unhandledFocused-border: #ffa600;
--vscode-mergeEditor-conflict-unhandledUnfocused-border: #ffa600;
--vscode-mergeEditor-conflictingLines-background: #ffea0047;
--vscode-minimap-chatEditHighlight: #ffffff99;
--vscode-minimap-errorHighlight: #ff1212b3;
--vscode-minimap-findMatchHighlight: #d18616;
--vscode-minimap-foregroundOpacity: #000000;
--vscode-minimap-infoHighlight: #1a85ff;
--vscode-minimap-selectionHighlight: #add6ff;
--vscode-minimap-selectionOccurrenceHighlight: #c9c9c9;
--vscode-minimap-warningHighlight: #bf8803;
--vscode-minimapGutter-addedBackground: #2ea043;
--vscode-minimapGutter-deletedBackground: #f85149;
--vscode-minimapGutter-modifiedBackground: #005fb8;
--vscode-minimapSlider-activeBackground: #0000004d;
--vscode-minimapSlider-background: #64646433;
--vscode-minimapSlider-hoverBackground: #64646459;
--vscode-multiDiffEditor-background: #ffffff;
--vscode-multiDiffEditor-border: #cccccc;
--vscode-multiDiffEditor-headerBackground: #f8f8f8;
--vscode-notebook-cellEditorBackground: #f8f8f8;
--vscode-notebook-cellInsertionIndicator: #005fb8;
--vscode-notebook-cellStatusBarItemHoverBackground: #00000014;
--vscode-notebook-cellToolbarSeparator: #80808059;
--vscode-notebook-editorBackground: #ffffff;
--vscode-notebook-focusedCellBorder: #005fb8;
--vscode-notebook-focusedEditorBorder: #005fb8;
--vscode-notebook-inactiveFocusedCellBorder: #e5e5e5;
--vscode-notebook-selectedCellBorder: #e5e5e5;
--vscode-notebook-symbolHighlightBackground: #fdff0033;
--vscode-notebookEditorOverviewRuler-runningCellForeground: #388a34;
--vscode-notebookScrollbarSlider-activeBackground: #00000099;
--vscode-notebookScrollbarSlider-background: #64646466;
--vscode-notebookScrollbarSlider-hoverBackground: #646464b3;
--vscode-notebookStatusErrorIcon-foreground: #f85149;
--vscode-notebookStatusRunningIcon-foreground: #3b3b3b;
--vscode-notebookStatusSuccessIcon-foreground: #388a34;
--vscode-notificationCenter-border: #e5e5e5;
--vscode-notificationLink-foreground: #005fb8;
--vscode-notificationToast-border: #e5e5e5;
--vscode-notificationsErrorIcon-foreground: #e51400;
--vscode-notificationsInfoIcon-foreground: #1a85ff;
--vscode-notificationsWarningIcon-foreground: #bf8803;
--vscode-panel-dropBorder: #3b3b3b;
--vscode-panelSection-border: #e5e5e5;
--vscode-panelSection-dropBackground: #2677cb2e;
--vscode-panelSectionHeader-background: #80808033;
--vscode-panelStickyScroll-background: #f8f8f8;
--vscode-panelStickyScroll-shadow: #dddddd;
--vscode-panelTitleBadge-background: #005fb8;
--vscode-panelTitleBadge-foreground: #ffffff;
--vscode-peekView-border: #1a85ff;
--vscode-peekViewEditor-background: #f2f8fc;
--vscode-peekViewEditorGutter-background: #f2f8fc;
--vscode-peekViewEditorStickyScroll-background: #f2f8fc;
--vscode-peekViewResult-fileForeground: #1e1e1e;
--vscode-peekViewResult-lineForeground: #646465;
--vscode-peekViewResult-selectionBackground: #3399ff33;
--vscode-peekViewResult-selectionForeground: #6c6c6c;
--vscode-peekViewTitle-background: #f3f3f3;
--vscode-peekViewTitleDescription-foreground: #616161;
--vscode-peekViewTitleLabel-foreground: #000000;
--vscode-problemsErrorIcon-foreground: #e51400;
--vscode-problemsInfoIcon-foreground: #1a85ff;
--vscode-problemsWarningIcon-foreground: #bf8803;
--vscode-profileBadge-background: #c4c4c4;
--vscode-profileBadge-foreground: #333333;
--vscode-profiles-sashBorder: #e5e5e5;
--vscode-prompt-frontMatter-background: #f2f2f2;
--vscode-prompt-frontMatter-inactiveBackground: #f9f9f9;
--vscode-quickInputList-focusBackground: #e8e8e8;
--vscode-quickInputList-focusForeground: #000000;
--vscode-quickInputList-focusIconForeground: #000000;
--vscode-quickInputTitle-background: #0000000f;
--vscode-radio-activeBackground: #bed6ed;
--vscode-radio-activeBorder: #005fb8;
--vscode-radio-activeForeground: #000000;
--vscode-radio-inactiveBorder: #00000033;
--vscode-radio-inactiveHoverBackground: #b8b8b850;
--vscode-sash-hoverBorder: #005fb8;
--vscode-scmGraph-foreground1: #ffb000;
--vscode-scmGraph-foreground2: #dc267f;
--vscode-scmGraph-foreground3: #994f00;
--vscode-scmGraph-foreground4: #40b0a6;
--vscode-scmGraph-foreground5: #b66dff;
--vscode-scmGraph-historyItemBaseRefColor: #ea5c00;
--vscode-scmGraph-historyItemHoverAdditionsForeground: #587c0c;
--vscode-scmGraph-historyItemHoverDefaultLabelBackground: #cccccc;
--vscode-scmGraph-historyItemHoverDefaultLabelForeground: #3b3b3b;
--vscode-scmGraph-historyItemHoverDeletionsForeground: #ad0707;
--vscode-scmGraph-historyItemHoverLabelForeground: #ffffff;
--vscode-scmGraph-historyItemRefColor: #1a85ff;
--vscode-scmGraph-historyItemRemoteRefColor: #652d90;
--vscode-scrollbar-shadow: #dddddd;
--vscode-scrollbarSlider-activeBackground: #00000099;
--vscode-scrollbarSlider-background: #64646466;
--vscode-scrollbarSlider-hoverBackground: #646464b3;
--vscode-search-resultsInfoForeground: #3b3b3b;
--vscode-searchEditor-findMatchBackground: #ea5c0038;
--vscode-settings-checkboxBackground: #f8f8f8;
--vscode-settings-checkboxBorder: #cecece;
--vscode-settings-checkboxForeground: #3b3b3b;
--vscode-settings-dropdownForeground: #3b3b3b;
--vscode-settings-dropdownListBorder: #c8c8c8;
--vscode-settings-focusedRowBackground: #f2f2f299;
--vscode-settings-focusedRowBorder: #005fb8;
--vscode-settings-headerBorder: #e5e5e5;
--vscode-settings-numberInputBackground: #ffffff;
--vscode-settings-numberInputForeground: #3b3b3b;
--vscode-settings-rowHoverBackground: #f2f2f24d;
--vscode-settings-sashBorder: #e5e5e5;
--vscode-settings-settingsHeaderHoverForeground: #1f1f1fb3;
--vscode-settings-textInputBackground: #ffffff;
--vscode-settings-textInputForeground: #3b3b3b;
--vscode-sideBar-dropBackground: #2677cb2e;
--vscode-sideBarActivityBarTop-border: #e5e5e5;
--vscode-sideBarStickyScroll-background: #f8f8f8;
--vscode-sideBarStickyScroll-shadow: #dddddd;
--vscode-sideBarTitle-background: #f8f8f8;
--vscode-sideBySideEditor-horizontalBorder: #e5e5e5;
--vscode-sideBySideEditor-verticalBorder: #e5e5e5;
--vscode-simpleFindWidget-sashBorder: #c8c8c8;
--vscode-statusBar-debuggingBorder: #e5e5e5;
--vscode-statusBar-noFolderBorder: #e5e5e5;
--vscode-statusBar-noFolderForeground: #3b3b3b;
--vscode-statusBarItem-activeBackground: #ffffff2e;
--vscode-statusBarItem-errorForeground: #ffffff;
--vscode-statusBarItem-errorHoverBackground: #b8b8b850;
--vscode-statusBarItem-errorHoverForeground: #3b3b3b;
--vscode-statusBarItem-hoverForeground: #3b3b3b;
--vscode-statusBarItem-offlineBackground: #6c1717;
--vscode-statusBarItem-offlineForeground: #ffffff;
--vscode-statusBarItem-offlineHoverBackground: #b8b8b850;
--vscode-statusBarItem-offlineHoverForeground: #3b3b3b;
--vscode-statusBarItem-prominentForeground: #3b3b3b;
--vscode-statusBarItem-prominentHoverBackground: #b8b8b850;
--vscode-statusBarItem-prominentHoverForeground: #3b3b3b;
--vscode-statusBarItem-remoteHoverBackground: #b8b8b850;
--vscode-statusBarItem-remoteHoverForeground: #3b3b3b;
--vscode-statusBarItem-warningBackground: #725102;
--vscode-statusBarItem-warningForeground: #ffffff;
--vscode-statusBarItem-warningHoverBackground: #b8b8b850;
--vscode-statusBarItem-warningHoverForeground: #3b3b3b;
--vscode-symbolIcon-arrayForeground: #3b3b3b;
--vscode-symbolIcon-booleanForeground: #3b3b3b;
--vscode-symbolIcon-classForeground: #d67e00;
--vscode-symbolIcon-colorForeground: #3b3b3b;
--vscode-symbolIcon-constantForeground: #3b3b3b;
--vscode-symbolIcon-constructorForeground: #652d90;
--vscode-symbolIcon-enumeratorForeground: #d67e00;
--vscode-symbolIcon-enumeratorMemberForeground: #007acc;
--vscode-symbolIcon-eventForeground: #d67e00;
--vscode-symbolIcon-fieldForeground: #007acc;
--vscode-symbolIcon-fileForeground: #3b3b3b;
--vscode-symbolIcon-folderForeground: #3b3b3b;
--vscode-symbolIcon-functionForeground: #652d90;
--vscode-symbolIcon-interfaceForeground: #007acc;
--vscode-symbolIcon-keyForeground: #3b3b3b;
--vscode-symbolIcon-keywordForeground: #3b3b3b;
--vscode-symbolIcon-methodForeground: #652d90;
--vscode-symbolIcon-moduleForeground: #3b3b3b;
--vscode-symbolIcon-namespaceForeground: #3b3b3b;
--vscode-symbolIcon-nullForeground: #3b3b3b;
--vscode-symbolIcon-numberForeground: #3b3b3b;
--vscode-symbolIcon-objectForeground: #3b3b3b;
--vscode-symbolIcon-operatorForeground: #3b3b3b;
--vscode-symbolIcon-packageForeground: #3b3b3b;
--vscode-symbolIcon-propertyForeground: #3b3b3b;
--vscode-symbolIcon-referenceForeground: #3b3b3b;
--vscode-symbolIcon-snippetForeground: #3b3b3b;
--vscode-symbolIcon-stringForeground: #3b3b3b;
--vscode-symbolIcon-structForeground: #3b3b3b;
--vscode-symbolIcon-textForeground: #3b3b3b;
--vscode-symbolIcon-typeParameterForeground: #3b3b3b;
--vscode-symbolIcon-unitForeground: #3b3b3b;
--vscode-symbolIcon-variableForeground: #007acc;
--vscode-tab-activeModifiedBorder: #33aaee;
--vscode-tab-dragAndDropBorder: #3b3b3b;
--vscode-tab-inactiveModifiedBorder: #33aaee80;
--vscode-tab-unfocusedActiveBackground: #ffffff;
--vscode-tab-unfocusedActiveForeground: #3b3b3bb3;
--vscode-tab-unfocusedActiveModifiedBorder: #33aaeeb3;
--vscode-tab-unfocusedInactiveBackground: #f8f8f8;
--vscode-tab-unfocusedInactiveForeground: #86868680;
--vscode-tab-unfocusedInactiveModifiedBorder: #33aaee40;
--vscode-terminal-ansiBlack: #000000;
--vscode-terminal-ansiBlue: #0451a5;
--vscode-terminal-ansiBrightBlack: #666666;
--vscode-terminal-ansiBrightBlue: #0451a5;
--vscode-terminal-ansiBrightCyan: #0598bc;
--vscode-terminal-ansiBrightGreen: #14ce14;
--vscode-terminal-ansiBrightMagenta: #bc05bc;
--vscode-terminal-ansiBrightRed: #cd3131;
--vscode-terminal-ansiBrightWhite: #a5a5a5;
--vscode-terminal-ansiBrightYellow: #b5ba00;
--vscode-terminal-ansiCyan: #0598bc;
--vscode-terminal-ansiGreen: #107c10;
--vscode-terminal-ansiMagenta: #bc05bc;
--vscode-terminal-ansiRed: #cd3131;
--vscode-terminal-ansiWhite: #555555;
--vscode-terminal-ansiYellow: #949800;
--vscode-terminal-border: #e5e5e5;
--vscode-terminal-dropBackground: #2677cb2e;
--vscode-terminal-findMatchBackground: #a8ac94;
--vscode-terminal-findMatchHighlightBackground: #ea5c0055;
--vscode-terminal-hoverHighlightBackground: #add6ff13;
--vscode-terminal-initialHintForeground: #00000077;
--vscode-terminal-selectionBackground: #add6ff;
--vscode-terminalCommandDecoration-defaultBackground: #00000040;
--vscode-terminalCommandDecoration-errorBackground: #e51400;
--vscode-terminalCommandDecoration-successBackground: #2090d3;
--vscode-terminalCommandGuide-foreground: #e4e6f1;
--vscode-terminalOverviewRuler-border: #e5e5e5;
--vscode-terminalOverviewRuler-cursorForeground: #a0a0a0cc;
--vscode-terminalOverviewRuler-findMatchForeground: #d186167e;
--vscode-terminalStickyScrollHover-background: #f0f0f0;
--vscode-terminalSymbolIcon-aliasForeground: #652d90;
--vscode-terminalSymbolIcon-argumentForeground: #007acc;
--vscode-terminalSymbolIcon-fileForeground: #3b3b3b;
--vscode-terminalSymbolIcon-flagForeground: #d67e00;
--vscode-terminalSymbolIcon-folderForeground: #3b3b3b;
--vscode-terminalSymbolIcon-methodForeground: #652d90;
--vscode-terminalSymbolIcon-optionForeground: #d67e00;
--vscode-terminalSymbolIcon-optionValueForeground: #007acc;
--vscode-testing-coverCountBadgeBackground: #cccccc;
--vscode-testing-coverCountBadgeForeground: #3b3b3b;
--vscode-testing-coveredBackground: #9ccc2c40;
--vscode-testing-coveredBorder: #9ccc2c30;
--vscode-testing-coveredGutterBackground: #9ccc2c27;
--vscode-testing-iconErrored: #f14c4c;
--vscode-testing-iconErrored-retired: #f14c4cb3;
--vscode-testing-iconFailed: #f14c4c;
--vscode-testing-iconFailed-retired: #f14c4cb3;
--vscode-testing-iconPassed: #73c991;
--vscode-testing-iconPassed-retired: #73c991b3;
--vscode-testing-iconQueued: #cca700;
--vscode-testing-iconQueued-retired: #cca700b3;
--vscode-testing-iconSkipped: #848484;
--vscode-testing-iconSkipped-retired: #848484b3;
--vscode-testing-iconUnset: #848484;
--vscode-testing-iconUnset-retired: #848484b3;
--vscode-testing-message-error-badgeBackground: #e51400;
--vscode-testing-message-error-badgeBorder: #e51400;
--vscode-testing-message-error-badgeForeground: #ffffff;
--vscode-testing-message-info-decorationForeground: #3b3b3b80;
--vscode-testing-messagePeekBorder: #1a85ff;
--vscode-testing-messagePeekHeaderBackground: #1a85ff1a;
--vscode-testing-peekBorder: #e51400;
--vscode-testing-peekHeaderBackground: #e514001a;
--vscode-testing-runAction: #73c991;
--vscode-testing-uncoveredBackground: #ff000033;
--vscode-testing-uncoveredBorder: #ff000026;
--vscode-testing-uncoveredBranchBackground: #ff9999;
--vscode-testing-uncoveredGutterBackground: #ff00004d;
--vscode-toolbar-activeBackground: #a6a6a650;
--vscode-toolbar-hoverBackground: #b8b8b850;
--vscode-tree-inactiveIndentGuidesStroke: #a9a9a966;
--vscode-tree-indentGuidesStroke: #a9a9a9;
--vscode-tree-tableColumnsBorder: #61616120;
--vscode-tree-tableOddRowsBackground: #3b3b3b0a;
--vscode-walkThrough-embeddedEditorBackground: #f4f4f4;
--vscode-walkthrough-stepTitle-foreground: #000000;
--vscode-welcomePage-progress-background: #ffffff;
--vscode-welcomePage-progress-foreground: #005fb8;
--vscode-welcomePage-tileBorder: #0000001a;
--vscode-welcomePage-tileHoverBackground: #dfdfdf;
--vscode-widget-shadow: #00000029;
}

.default-theme.dark {
   --vscode-actionBar-toggledBackground: #383a49;
--vscode-activityBar-activeBorder: #0078d4;
--vscode-activityBar-background: #181818;
--vscode-activityBar-border: #2b2b2b;
--vscode-activityBar-foreground: #d7d7d7;
--vscode-activityBar-inactiveForeground: #868686;
--vscode-activityBarBadge-background: #0078d4;
--vscode-activityBarBadge-foreground: #ffffff;
--vscode-badge-background: #616161;
--vscode-badge-foreground: #f8f8f8;
--vscode-button-background: #0078d4;
--vscode-button-border: #ffffff12;
--vscode-button-foreground: #ffffff;
--vscode-button-hoverBackground: #026ec1;
--vscode-button-secondaryBackground: #313131;
--vscode-button-secondaryForeground: #cccccc;
--vscode-button-secondaryHoverBackground: #3c3c3c;
--vscode-chat-editedFileForeground: #e2c08d;
--vscode-chat-slashCommandBackground: #34414b;
--vscode-chat-slashCommandForeground: #40a6ff;
--vscode-checkbox-background: #313131;
--vscode-checkbox-border: #3c3c3c;
--vscode-debugToolBar-background: #181818;
--vscode-descriptionForeground: #9d9d9d;
--vscode-dropdown-background: #313131;
--vscode-dropdown-border: #3c3c3c;
--vscode-dropdown-foreground: #cccccc;
--vscode-dropdown-listBackground: #1f1f1f;
--vscode-editor-background: #1f1f1f;
--vscode-editor-findMatchBackground: #9e6a03;
--vscode-editor-foreground: #cccccc;
--vscode-editor-inactiveSelectionBackground: #3a3d41;
--vscode-editor-selectionHighlightBackground: #add6ff26;
--vscode-editorGroup-border: #ffffff17;
--vscode-editorGroupHeader-tabsBackground: #181818;
--vscode-editorGroupHeader-tabsBorder: #2b2b2b;
--vscode-editorGutter-addedBackground: #2ea043;
--vscode-editorGutter-deletedBackground: #f85149;
--vscode-editorGutter-modifiedBackground: #0078d4;
--vscode-editorIndentGuide-activeBackground1: #707070;
--vscode-editorIndentGuide-background1: #404040;
--vscode-editorLineNumber-activeForeground: #cccccc;
--vscode-editorLineNumber-foreground: #6e7681;
--vscode-editorOverviewRuler-border: #010409;
--vscode-editorWidget-background: #202020;
--vscode-errorForeground: #f85149;
--vscode-focusBorder: #0078d4;
--vscode-foreground: #cccccc;
--vscode-icon-foreground: #cccccc;
--vscode-input-background: #313131;
--vscode-input-border: #3c3c3c;
--vscode-input-foreground: #cccccc;
--vscode-input-placeholderForeground: #989898;
--vscode-inputOption-activeBackground: #2489db82;
--vscode-inputOption-activeBorder: #2488db;
--vscode-keybindingLabel-foreground: #cccccc;
--vscode-list-activeSelectionIconForeground: #ffffff;
--vscode-list-dropBackground: #383b3d;
--vscode-menu-background: #1f1f1f;
--vscode-menu-border: #454545;
--vscode-menu-foreground: #cccccc;
--vscode-menu-selectionBackground: #0078d4;
--vscode-menu-separatorBackground: #454545;
--vscode-notificationCenterHeader-background: #1f1f1f;
--vscode-notificationCenterHeader-foreground: #cccccc;
--vscode-notifications-background: #1f1f1f;
--vscode-notifications-border: #2b2b2b;
--vscode-notifications-foreground: #cccccc;
--vscode-panel-background: #181818;
--vscode-panel-border: #2b2b2b;
--vscode-panelInput-border: #2b2b2b;
--vscode-panelTitle-activeBorder: #0078d4;
--vscode-panelTitle-activeForeground: #cccccc;
--vscode-panelTitle-inactiveForeground: #9d9d9d;
--vscode-peekViewEditor-background: #1f1f1f;
--vscode-peekViewEditor-matchHighlightBackground: #bb800966;
--vscode-peekViewResult-background: #1f1f1f;
--vscode-peekViewResult-matchHighlightBackground: #bb800966;
--vscode-pickerGroup-border: #3c3c3c;
--vscode-ports-iconRunningProcessForeground: #369432;
--vscode-progressBar-background: #0078d4;
--vscode-quickInput-background: #222222;
--vscode-quickInput-foreground: #cccccc;
--vscode-settings-dropdownBackground: #313131;
--vscode-settings-dropdownBorder: #3c3c3c;
--vscode-settings-headerForeground: #ffffff;
--vscode-settings-modifiedItemIndicator: #bb800966;
--vscode-sideBar-background: #181818;
--vscode-sideBar-border: #2b2b2b;
--vscode-sideBar-foreground: #cccccc;
--vscode-sideBarSectionHeader-background: #181818;
--vscode-sideBarSectionHeader-border: #2b2b2b;
--vscode-sideBarSectionHeader-foreground: #cccccc;
--vscode-sideBarTitle-foreground: #cccccc;
--vscode-statusBar-background: #181818;
--vscode-statusBar-border: #2b2b2b;
--vscode-statusBar-debuggingBackground: #0078d4;
--vscode-statusBar-debuggingForeground: #ffffff;
--vscode-statusBar-focusBorder: #0078d4;
--vscode-statusBar-foreground: #cccccc;
--vscode-statusBar-noFolderBackground: #1f1f1f;
--vscode-statusBarItem-focusBorder: #0078d4;
--vscode-statusBarItem-prominentBackground: #6e768166;
--vscode-statusBarItem-remoteBackground: #0078d4;
--vscode-statusBarItem-remoteForeground: #ffffff;
--vscode-tab-activeBackground: #1f1f1f;
--vscode-tab-activeBorder: #1f1f1f;
--vscode-tab-activeBorderTop: #0078d4;
--vscode-tab-activeForeground: #ffffff;
--vscode-tab-border: #2b2b2b;
--vscode-tab-hoverBackground: #1f1f1f;
--vscode-tab-inactiveBackground: #181818;
--vscode-tab-inactiveForeground: #9d9d9d;
--vscode-tab-lastPinnedBorder: #cccccc33;
--vscode-tab-selectedBackground: #222222;
--vscode-tab-selectedBorderTop: #6caddf;
--vscode-tab-selectedForeground: #ffffffa0;
--vscode-tab-unfocusedActiveBorder: #1f1f1f;
--vscode-tab-unfocusedActiveBorderTop: #2b2b2b;
--vscode-tab-unfocusedHoverBackground: #1f1f1f;
--vscode-terminal-foreground: #cccccc;
--vscode-terminal-inactiveSelectionBackground: #3a3d41;
--vscode-terminal-tab-activeBorder: #0078d4;
--vscode-textBlockQuote-background: #2b2b2b;
--vscode-textBlockQuote-border: #616161;
--vscode-textCodeBlock-background: #2b2b2b;
--vscode-textLink-activeForeground: #4daafc;
--vscode-textLink-foreground: #4daafc;
--vscode-textPreformat-background: #3c3c3c;
--vscode-textPreformat-foreground: #d0d0d0;
--vscode-textSeparator-foreground: #21262d;
--vscode-titleBar-activeBackground: #181818;
--vscode-titleBar-activeForeground: #cccccc;
--vscode-titleBar-border: #2b2b2b;
--vscode-titleBar-inactiveBackground: #1f1f1f;
--vscode-titleBar-inactiveForeground: #9d9d9d;
--vscode-welcomePage-progress-foreground: #0078d4;
--vscode-welcomePage-tileBackground: #2b2b2b;
--vscode-widget-border: #313131;
--vscode-activityBar-dropBorder: #d7d7d7;
--vscode-activityBarTop-activeBorder: #e7e7e7;
--vscode-activityBarTop-dropBorder: #e7e7e7;
--vscode-activityBarTop-foreground: #e7e7e7;
--vscode-activityBarTop-inactiveForeground: #e7e7e799;
--vscode-activityErrorBadge-background: #f14c4c;
--vscode-activityErrorBadge-foreground: #000000;
--vscode-activityWarningBadge-background: #cca700;
--vscode-activityWarningBadge-foreground: #000000;
--vscode-banner-background: #04395e;
--vscode-banner-foreground: #ffffff;
--vscode-banner-iconForeground: #3794ff;
--vscode-breadcrumb-activeSelectionForeground: #e0e0e0;
--vscode-breadcrumb-background: #1f1f1f;
--vscode-breadcrumb-focusForeground: #e0e0e0;
--vscode-breadcrumb-foreground: #cccccccc;
--vscode-breadcrumbPicker-background: #202020;
--vscode-button-separator: #ffffff66;
--vscode-chart-axis: #bfbfbf66;
--vscode-chart-guide: #bfbfbf33;
--vscode-chart-line: #236b8e;
--vscode-charts-blue: #3794ff;
--vscode-charts-foreground: #cccccc;
--vscode-charts-green: #89d185;
--vscode-charts-lines: #cccccc80;
--vscode-charts-orange: #d18616;
--vscode-charts-purple: #b180d7;
--vscode-charts-red: #f14c4c;
--vscode-charts-yellow: #cca700;
--vscode-chat-avatarBackground: #1f1f1f;
--vscode-chat-avatarForeground: #cccccc;
--vscode-chat-requestBackground: #1f1f1f9e;
--vscode-chat-requestBorder: #ffffff1a;
--vscode-checkbox-disabled-background: #646464;
--vscode-checkbox-disabled-foreground: #989898;
--vscode-checkbox-foreground: #cccccc;
--vscode-checkbox-selectBackground: #202020;
--vscode-checkbox-selectBorder: #cccccc;
--vscode-commandCenter-activeBackground: #ffffff14;
--vscode-commandCenter-activeBorder: #cccccc4d;
--vscode-commandCenter-activeForeground: #cccccc;
--vscode-commandCenter-background: #ffffff0d;
--vscode-commandCenter-border: #cccccc33;
--vscode-commandCenter-debuggingBackground: #0078d442;
--vscode-commandCenter-foreground: #cccccc;
--vscode-commandCenter-inactiveBorder: #9d9d9d40;
--vscode-commandCenter-inactiveForeground: #9d9d9d;
--vscode-commentsView-resolvedIcon: #cccccc80;
--vscode-commentsView-unresolvedIcon: #0078d4;
--vscode-debugConsole-errorForeground: #f85149;
--vscode-debugConsole-infoForeground: #3794ff;
--vscode-debugConsole-sourceForeground: #cccccc;
--vscode-debugConsole-warningForeground: #cca700;
--vscode-debugConsoleInputIcon-foreground: #cccccc;
--vscode-debugExceptionWidget-background: #420b0d;
--vscode-debugExceptionWidget-border: #a31515;
--vscode-debugIcon-breakpointCurrentStackframeForeground: #ffcc00;
--vscode-debugIcon-breakpointDisabledForeground: #848484;
--vscode-debugIcon-breakpointForeground: #e51400;
--vscode-debugIcon-breakpointStackframeForeground: #89d185;
--vscode-debugIcon-breakpointUnverifiedForeground: #848484;
--vscode-debugIcon-continueForeground: #75beff;
--vscode-debugIcon-disconnectForeground: #f48771;
--vscode-debugIcon-pauseForeground: #75beff;
--vscode-debugIcon-restartForeground: #89d185;
--vscode-debugIcon-startForeground: #89d185;
--vscode-debugIcon-stepBackForeground: #75beff;
--vscode-debugIcon-stepIntoForeground: #75beff;
--vscode-debugIcon-stepOutForeground: #75beff;
--vscode-debugIcon-stepOverForeground: #75beff;
--vscode-debugIcon-stopForeground: #f48771;
--vscode-debugTokenExpression-boolean: #4e94ce;
--vscode-debugTokenExpression-error: #f48771;
--vscode-debugTokenExpression-name: #c586c0;
--vscode-debugTokenExpression-number: #b5cea8;
--vscode-debugTokenExpression-string: #ce9178;
--vscode-debugTokenExpression-type: #4a90e2;
--vscode-debugTokenExpression-value: #cccccc99;
--vscode-debugView-exceptionLabelBackground: #6c2022;
--vscode-debugView-exceptionLabelForeground: #cccccc;
--vscode-debugView-stateLabelBackground: #88888844;
--vscode-debugView-stateLabelForeground: #cccccc;
--vscode-debugView-valueChangedHighlight: #569cd6;
--vscode-diffEditor-diagonalFill: #cccccc33;
--vscode-diffEditor-insertedLineBackground: #9bb95533;
--vscode-diffEditor-insertedTextBackground: #9ccc2c33;
--vscode-diffEditor-move-border: #8b8b8b9c;
--vscode-diffEditor-moveActive-border: #ffa500;
--vscode-diffEditor-removedLineBackground: #ff000033;
--vscode-diffEditor-removedTextBackground: #ff000033;
--vscode-diffEditor-unchangedCodeBackground: #74747429;
--vscode-diffEditor-unchangedRegionBackground: #181818;
--vscode-diffEditor-unchangedRegionForeground: #cccccc;
--vscode-diffEditor-unchangedRegionShadow: #000000;
--vscode-disabledForeground: #cccccc80;
--vscode-editor-compositionBorder: #ffffff;
--vscode-editor-findMatchHighlightBackground: #ea5c0055;
--vscode-editor-findRangeHighlightBackground: #3a3d4166;
--vscode-editor-focusedStackFrameHighlightBackground: #7abd7a4d;
--vscode-editor-foldBackground: #264f784d;
--vscode-editor-foldPlaceholderForeground: #808080;
--vscode-editor-hoverHighlightBackground: #264f7840;
--vscode-editor-inlineValuesBackground: #ffc80033;
--vscode-editor-inlineValuesForeground: #ffffff80;
--vscode-editor-lineHighlightBorder: #282828;
--vscode-editor-linkedEditingBackground: #ff00004d;
--vscode-editor-placeholder-foreground: #ffffff56;
--vscode-editor-rangeHighlightBackground: #ffffff0b;
--vscode-editor-selectionBackground: #264f78;
--vscode-editor-snippetFinalTabstopHighlightBorder: #525252;
--vscode-editor-snippetTabstopHighlightBackground: #7c7c7c4d;
--vscode-editor-stackFrameHighlightBackground: #ffff0033;
--vscode-editor-symbolHighlightBackground: #ea5c0055;
--vscode-editor-wordHighlightBackground: #575757b8;
--vscode-editor-wordHighlightStrongBackground: #004972b8;
--vscode-editor-wordHighlightTextBackground: #575757b8;
--vscode-editorActionList-background: #202020;
--vscode-editorActionList-focusBackground: #04395e;
--vscode-editorActionList-focusForeground: #ffffff;
--vscode-editorActionList-foreground: #cccccc;
--vscode-editorBracketHighlight-foreground1: #ffd700;
--vscode-editorBracketHighlight-foreground2: #da70d6;
--vscode-editorBracketHighlight-foreground3: #179fff;
--vscode-editorBracketHighlight-foreground4: #00000000;
--vscode-editorBracketHighlight-foreground5: #00000000;
--vscode-editorBracketHighlight-foreground6: #00000000;
--vscode-editorBracketHighlight-unexpectedBracket-foreground: #ff1212cc;
--vscode-editorBracketMatch-background: #0064001a;
--vscode-editorBracketMatch-border: #888888;
--vscode-editorBracketPairGuide-activeBackground1: #00000000;
--vscode-editorBracketPairGuide-activeBackground2: #00000000;
--vscode-editorBracketPairGuide-activeBackground3: #00000000;
--vscode-editorBracketPairGuide-activeBackground4: #00000000;
--vscode-editorBracketPairGuide-activeBackground5: #00000000;
--vscode-editorBracketPairGuide-activeBackground6: #00000000;
--vscode-editorBracketPairGuide-background1: #00000000;
--vscode-editorBracketPairGuide-background2: #00000000;
--vscode-editorBracketPairGuide-background3: #00000000;
--vscode-editorBracketPairGuide-background4: #00000000;
--vscode-editorBracketPairGuide-background5: #00000000;
--vscode-editorBracketPairGuide-background6: #00000000;
--vscode-editorCodeLens-foreground: #999999;
--vscode-editorCommentsWidget-rangeActiveBackground: #0078d41a;
--vscode-editorCommentsWidget-rangeBackground: #0078d41a;
--vscode-editorCommentsWidget-replyInputBackground: #252526;
--vscode-editorCommentsWidget-resolvedBorder: #cccccc80;
--vscode-editorCommentsWidget-unresolvedBorder: #0078d4;
--vscode-editorCursor-foreground: #aeafad;
--vscode-editorError-foreground: #f14c4c;
--vscode-editorGhostText-foreground: #ffffff56;
--vscode-editorGroup-dropBackground: #53595d80;
--vscode-editorGroup-dropIntoPromptBackground: #202020;
--vscode-editorGroup-dropIntoPromptForeground: #cccccc;
--vscode-editorGroupHeader-noTabsBackground: #1f1f1f;
--vscode-editorGutter-addedSecondaryBackground: #175021;
--vscode-editorGutter-background: #1f1f1f;
--vscode-editorGutter-commentGlyphForeground: #cccccc;
--vscode-editorGutter-commentRangeForeground: #37373d;
--vscode-editorGutter-commentUnresolvedGlyphForeground: #cccccc;
--vscode-editorGutter-deletedSecondaryBackground: #b91007;
--vscode-editorGutter-foldingControlForeground: #cccccc;
--vscode-editorGutter-itemBackground: #37373d;
--vscode-editorGutter-itemGlyphForeground: #cccccc;
--vscode-editorGutter-modifiedSecondaryBackground: #003c6a;
--vscode-editorHint-foreground: #eeeeeeb3;
--vscode-editorHoverWidget-background: #202020;
--vscode-editorHoverWidget-border: #454545;
--vscode-editorHoverWidget-foreground: #cccccc;
--vscode-editorHoverWidget-highlightForeground: #2aaaff;
--vscode-editorHoverWidget-statusBarBackground: #262626;
--vscode-editorIndentGuide-activeBackground2: #00000000;
--vscode-editorIndentGuide-activeBackground3: #00000000;
--vscode-editorIndentGuide-activeBackground4: #00000000;
--vscode-editorIndentGuide-activeBackground5: #00000000;
--vscode-editorIndentGuide-activeBackground6: #00000000;
--vscode-editorIndentGuide-background2: #00000000;
--vscode-editorIndentGuide-background3: #00000000;
--vscode-editorIndentGuide-background4: #00000000;
--vscode-editorIndentGuide-background5: #00000000;
--vscode-editorIndentGuide-background6: #00000000;
--vscode-editorInfo-foreground: #3794ff;
--vscode-editorInlayHint-background: #6161611a;
--vscode-editorInlayHint-foreground: #969696;
--vscode-editorInlayHint-parameterBackground: #6161611a;
--vscode-editorInlayHint-parameterForeground: #969696;
--vscode-editorInlayHint-typeBackground: #6161611a;
--vscode-editorInlayHint-typeForeground: #969696;
--vscode-editorLightBulb-foreground: #ffcc00;
--vscode-editorLightBulbAi-foreground: #ffcc00;
--vscode-editorLightBulbAutoFix-foreground: #75beff;
--vscode-editorLink-activeForeground: #4e94ce;
--vscode-editorMarkerNavigation-background: #1f1f1f;
--vscode-editorMarkerNavigationError-background: #f14c4c;
--vscode-editorMarkerNavigationError-headerBackground: #f14c4c1a;
--vscode-editorMarkerNavigationInfo-background: #3794ff;
--vscode-editorMarkerNavigationInfo-headerBackground: #3794ff1a;
--vscode-editorMarkerNavigationWarning-background: #cca700;
--vscode-editorMarkerNavigationWarning-headerBackground: #cca7001a;
--vscode-editorMinimap-inlineChatInserted: #9ccc2c1f;
--vscode-editorMultiCursor-primary-foreground: #aeafad;
--vscode-editorMultiCursor-secondary-foreground: #aeafad;
--vscode-editorOverviewRuler-addedForeground: #2ea04399;
--vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;
--vscode-editorOverviewRuler-commentForeground: #37373d;
--vscode-editorOverviewRuler-commentUnresolvedForeground: #37373d;
--vscode-editorOverviewRuler-commonContentForeground: #60606066;
--vscode-editorOverviewRuler-currentContentForeground: #40c8ae80;
--vscode-editorOverviewRuler-deletedForeground: #f8514999;
--vscode-editorOverviewRuler-errorForeground: #ff1212b3;
--vscode-editorOverviewRuler-findMatchForeground: #d186167e;
--vscode-editorOverviewRuler-incomingContentForeground: #40a6ff80;
--vscode-editorOverviewRuler-infoForeground: #3794ff;
--vscode-editorOverviewRuler-inlineChatInserted: #9ccc2c1f;
--vscode-editorOverviewRuler-inlineChatRemoved: #ff00001f;
--vscode-editorOverviewRuler-modifiedForeground: #0078d499;
--vscode-editorOverviewRuler-rangeHighlightForeground: #007acc99;
--vscode-editorOverviewRuler-selectionHighlightForeground: #a0a0a0cc;
--vscode-editorOverviewRuler-warningForeground: #cca700;
--vscode-editorOverviewRuler-wordHighlightForeground: #a0a0a0cc;
--vscode-editorOverviewRuler-wordHighlightStrongForeground: #c0a0c0cc;
--vscode-editorOverviewRuler-wordHighlightTextForeground: #a0a0a0cc;
--vscode-editorPane-background: #1f1f1f;
--vscode-editorRuler-foreground: #5a5a5a;
--vscode-editorStickyScroll-background: #1f1f1f;
--vscode-editorStickyScroll-shadow: #000000;
--vscode-editorStickyScrollHover-background: #2a2d2e;
--vscode-editorSuggestWidget-background: #202020;
--vscode-editorSuggestWidget-border: #454545;
--vscode-editorSuggestWidget-focusHighlightForeground: #2aaaff;
--vscode-editorSuggestWidget-foreground: #cccccc;
--vscode-editorSuggestWidget-highlightForeground: #2aaaff;
--vscode-editorSuggestWidget-selectedBackground: #04395e;
--vscode-editorSuggestWidget-selectedForeground: #ffffff;
--vscode-editorSuggestWidget-selectedIconForeground: #ffffff;
--vscode-editorSuggestWidgetStatus-foreground: #cccccc80;
--vscode-editorUnicodeHighlight-border: #cca700;
--vscode-editorUnnecessaryCode-opacity: #000000aa;
--vscode-editorWarning-foreground: #cca700;
--vscode-editorWatermark-foreground: #cccccc99;
--vscode-editorWhitespace-foreground: #e3e4e229;
--vscode-editorWidget-border: #454545;
--vscode-editorWidget-foreground: #cccccc;
--vscode-extensionBadge-remoteBackground: #0078d4;
--vscode-extensionBadge-remoteForeground: #ffffff;
--vscode-extensionButton-background: #0078d4;
--vscode-extensionButton-foreground: #ffffff;
--vscode-extensionButton-hoverBackground: #026ec1;
--vscode-extensionButton-prominentBackground: #0078d4;
--vscode-extensionButton-prominentForeground: #ffffff;
--vscode-extensionButton-prominentHoverBackground: #026ec1;
--vscode-extensionButton-separator: #ffffff66;
--vscode-extensionIcon-preReleaseForeground: #1d9271;
--vscode-extensionIcon-privateForeground: #ffffff60;
--vscode-extensionIcon-sponsorForeground: #d758b3;
--vscode-extensionIcon-starForeground: #ff8e00;
--vscode-extensionIcon-verifiedForeground: #4daafc;
--vscode-gauge-background: #007acc;
--vscode-gauge-errorBackground: #be1100;
--vscode-gauge-errorForeground: #be11004d;
--vscode-gauge-foreground: #007acc4d;
--vscode-gauge-warningBackground: #b89500;
--vscode-gauge-warningForeground: #b895004d;
--vscode-git-blame-editorDecorationForeground: #969696;
--vscode-gitDecoration-addedResourceForeground: #81b88b;
--vscode-gitDecoration-conflictingResourceForeground: #e4676b;
--vscode-gitDecoration-deletedResourceForeground: #c74e39;
--vscode-gitDecoration-ignoredResourceForeground: #8c8c8c;
--vscode-gitDecoration-modifiedResourceForeground: #e2c08d;
--vscode-gitDecoration-renamedResourceForeground: #73c991;
--vscode-gitDecoration-stageDeletedResourceForeground: #c74e39;
--vscode-gitDecoration-stageModifiedResourceForeground: #e2c08d;
--vscode-gitDecoration-submoduleResourceForeground: #8db9e2;
--vscode-gitDecoration-untrackedResourceForeground: #73c991;
--vscode-githd-blameView-info: #858585;
--vscode-githd-historyView-author: #9cdcfe;
--vscode-githd-historyView-branch: #c586c0;
--vscode-githd-historyView-email: #dcdcaa;
--vscode-githd-historyView-filePath: #d16969;
--vscode-githd-historyView-hash: #ce9178;
--vscode-githd-historyView-more: #9cdcfe;
--vscode-githd-historyView-ref: #608b4e;
--vscode-githd-historyView-subject: #569cd6;
--vscode-githd-historyView-title: #4ec9b0;
--vscode-githd-infoView-content: #608b4e;
--vscode-githd-infoView-new: #b5cea8;
--vscode-githd-infoView-old: #ce9178;
--vscode-githd-infoView-path: #569cd6;
--vscode-gitlens-closedAutolinkedIssueIconColor: #a371f7;
--vscode-gitlens-closedPullRequestIconColor: #f85149;
--vscode-gitlens-decorations-addedForegroundColor: #81b88b;
--vscode-gitlens-decorations-branchAheadForegroundColor: #35b15e;
--vscode-gitlens-decorations-branchBehindForegroundColor: #b15e35;
--vscode-gitlens-decorations-branchDivergedForegroundColor: #d8af1b;
--vscode-gitlens-decorations-branchMissingUpstreamForegroundColor: #c74e39;
--vscode-gitlens-decorations-branchUnpublishedForegroundColor: #cccccc;
--vscode-gitlens-decorations-branchUpToDateForegroundColor: #cccccc;
--vscode-gitlens-decorations-copiedForegroundColor: #73c991;
--vscode-gitlens-decorations-deletedForegroundColor: #c74e39;
--vscode-gitlens-decorations-ignoredForegroundColor: #8c8c8c;
--vscode-gitlens-decorations-modifiedForegroundColor: #e2c08d;
--vscode-gitlens-decorations-renamedForegroundColor: #73c991;
--vscode-gitlens-decorations-statusMergingOrRebasingConflictForegroundColor: #c74e39;
--vscode-gitlens-decorations-statusMergingOrRebasingForegroundColor: #d8af1b;
--vscode-gitlens-decorations-untrackedForegroundColor: #73c991;
--vscode-gitlens-decorations-workspaceCurrentForegroundColor: #35b15e;
--vscode-gitlens-decorations-workspaceRepoMissingForegroundColor: #909090;
--vscode-gitlens-decorations-workspaceRepoOpenForegroundColor: #35b15e;
--vscode-gitlens-decorations-worktreeHasUncommittedChangesForegroundColor: #e2c08d;
--vscode-gitlens-decorations-worktreeMissingForegroundColor: #c74e39;
--vscode-gitlens-graphChangesColumnAddedColor: #347d39;
--vscode-gitlens-graphChangesColumnDeletedColor: #c93c37;
--vscode-gitlens-graphLane10Color: #2ece9d;
--vscode-gitlens-graphLane1Color: #15a0bf;
--vscode-gitlens-graphLane2Color: #0669f7;
--vscode-gitlens-graphLane3Color: #8e00c2;
--vscode-gitlens-graphLane4Color: #c517b6;
--vscode-gitlens-graphLane5Color: #d90171;
--vscode-gitlens-graphLane6Color: #cd0101;
--vscode-gitlens-graphLane7Color: #f25d2e;
--vscode-gitlens-graphLane8Color: #f2ca33;
--vscode-gitlens-graphLane9Color: #7bd938;
--vscode-gitlens-graphMinimapMarkerHeadColor: #05e617;
--vscode-gitlens-graphMinimapMarkerHighlightsColor: #fbff0a;
--vscode-gitlens-graphMinimapMarkerLocalBranchesColor: #3087cf;
--vscode-gitlens-graphMinimapMarkerPullRequestsColor: #c76801;
--vscode-gitlens-graphMinimapMarkerRemoteBranchesColor: #2b5e88;
--vscode-gitlens-graphMinimapMarkerStashesColor: #b34db3;
--vscode-gitlens-graphMinimapMarkerTagsColor: #6b562e;
--vscode-gitlens-graphMinimapMarkerUpstreamColor: #09ae17;
--vscode-gitlens-graphScrollMarkerHeadColor: #05e617;
--vscode-gitlens-graphScrollMarkerHighlightsColor: #fbff0a;
--vscode-gitlens-graphScrollMarkerLocalBranchesColor: #3087cf;
--vscode-gitlens-graphScrollMarkerPullRequestsColor: #c76801;
--vscode-gitlens-graphScrollMarkerRemoteBranchesColor: #2b5e88;
--vscode-gitlens-graphScrollMarkerStashesColor: #b34db3;
--vscode-gitlens-graphScrollMarkerTagsColor: #6b562e;
--vscode-gitlens-graphScrollMarkerUpstreamColor: #09ae17;
--vscode-gitlens-gutterBackgroundColor: #ffffff13;
--vscode-gitlens-gutterForegroundColor: #bebebe;
--vscode-gitlens-gutterUncommittedForegroundColor: #00bcf299;
--vscode-gitlens-launchpadIndicatorAttentionColor: #d8af1b;
--vscode-gitlens-launchpadIndicatorAttentionHoverColor: #d8af1b;
--vscode-gitlens-launchpadIndicatorBlockedColor: #c74e39;
--vscode-gitlens-launchpadIndicatorBlockedHoverColor: #c74e39;
--vscode-gitlens-launchpadIndicatorMergeableColor: #3fb950;
--vscode-gitlens-launchpadIndicatorMergeableHoverColor: #3fb950;
--vscode-gitlens-lineHighlightBackgroundColor: #00bcf233;
--vscode-gitlens-lineHighlightOverviewRulerColor: #00bcf299;
--vscode-gitlens-mergedPullRequestIconColor: #a371f7;
--vscode-gitlens-openAutolinkedIssueIconColor: #3fb950;
--vscode-gitlens-openPullRequestIconColor: #3fb950;
--vscode-gitlens-trailingLineBackgroundColor: #00000000;
--vscode-gitlens-trailingLineForegroundColor: #99999959;
--vscode-gitlens-unpublishedChangesIconColor: #35b15e;
--vscode-gitlens-unpublishedCommitIconColor: #35b15e;
--vscode-gitlens-unpulledChangesIconColor: #b15e35;
--vscode-inlineChat-background: #202020;
--vscode-inlineChat-border: #454545;
--vscode-inlineChat-foreground: #cccccc;
--vscode-inlineChat-shadow: #0000005c;
--vscode-inlineChatDiff-inserted: #9ccc2c1a;
--vscode-inlineChatDiff-removed: #ff00001a;
--vscode-inlineChatInput-background: #313131;
--vscode-inlineChatInput-border: #454545;
--vscode-inlineChatInput-focusBorder: #0078d4;
--vscode-inlineChatInput-placeholderForeground: #989898;
--vscode-inlineEdit-gutterIndicator-background: #18181880;
--vscode-inlineEdit-gutterIndicator-primaryBackground: #0078d466;
--vscode-inlineEdit-gutterIndicator-primaryBorder: #0078d4;
--vscode-inlineEdit-gutterIndicator-primaryForeground: #ffffff;
--vscode-inlineEdit-gutterIndicator-secondaryBackground: #313131;
--vscode-inlineEdit-gutterIndicator-secondaryBorder: #313131;
--vscode-inlineEdit-gutterIndicator-secondaryForeground: #cccccc;
--vscode-inlineEdit-gutterIndicator-successfulBackground: #0078d4;
--vscode-inlineEdit-gutterIndicator-successfulBorder: #0078d4;
--vscode-inlineEdit-gutterIndicator-successfulForeground: #ffffff;
--vscode-inlineEdit-modifiedBackground: #9ccc2c0f;
--vscode-inlineEdit-modifiedBorder: #9ccc2c33;
--vscode-inlineEdit-modifiedChangedLineBackground: #9bb95524;
--vscode-inlineEdit-modifiedChangedTextBackground: #9ccc2c24;
--vscode-inlineEdit-originalBackground: #ff00000a;
--vscode-inlineEdit-originalBorder: #ff000033;
--vscode-inlineEdit-originalChangedLineBackground: #ff000029;
--vscode-inlineEdit-originalChangedTextBackground: #ff000029;
--vscode-inlineEdit-tabWillAcceptModifiedBorder: #9ccc2c33;
--vscode-inlineEdit-tabWillAcceptOriginalBorder: #ff000033;
--vscode-inputOption-activeForeground: #ffffff;
--vscode-inputOption-hoverBackground: #5a5d5e80;
--vscode-inputValidation-errorBackground: #5a1d1d;
--vscode-inputValidation-errorBorder: #be1100;
--vscode-inputValidation-infoBackground: #063b49;
--vscode-inputValidation-infoBorder: #007acc;
--vscode-inputValidation-warningBackground: #352a05;
--vscode-inputValidation-warningBorder: #b89500;
--vscode-interactive-activeCodeBorder: #007acc;
--vscode-interactive-inactiveCodeBorder: #37373d;
--vscode-keybindingLabel-background: #8080802b;
--vscode-keybindingLabel-border: #33333399;
--vscode-keybindingLabel-bottomBorder: #44444499;
--vscode-keybindingTable-headerBackground: #cccccc0a;
--vscode-keybindingTable-rowsBackground: #cccccc0a;
--vscode-list-activeSelectionBackground: #04395e;
--vscode-list-activeSelectionForeground: #ffffff;
--vscode-list-deemphasizedForeground: #8c8c8c;
--vscode-list-dropBetweenBackground: #cccccc;
--vscode-list-errorForeground: #f88070;
--vscode-list-filterMatchBackground: #ea5c0055;
--vscode-list-focusHighlightForeground: #2aaaff;
--vscode-list-focusOutline: #0078d4;
--vscode-list-highlightForeground: #2aaaff;
--vscode-list-hoverBackground: #2a2d2e;
--vscode-list-inactiveSelectionBackground: #37373d;
--vscode-list-invalidItemForeground: #b89500;
--vscode-list-warningForeground: #cca700;
--vscode-listFilterWidget-background: #202020;
--vscode-listFilterWidget-noMatchesOutline: #be1100;
--vscode-listFilterWidget-outline: #00000000;
--vscode-listFilterWidget-shadow: #0000005c;
--vscode-menu-selectionForeground: #ffffff;
--vscode-menubar-selectionBackground: #5a5d5e50;
--vscode-menubar-selectionForeground: #cccccc;
--vscode-merge-commonContentBackground: #60606029;
--vscode-merge-commonHeaderBackground: #60606066;
--vscode-merge-currentContentBackground: #40c8ae33;
--vscode-merge-currentHeaderBackground: #40c8ae80;
--vscode-merge-incomingContentBackground: #40a6ff33;
--vscode-merge-incomingHeaderBackground: #40a6ff80;
--vscode-mergeEditor-change-background: #9bb95533;
--vscode-mergeEditor-change-word-background: #9ccc2c33;
--vscode-mergeEditor-changeBase-background: #4b1818;
--vscode-mergeEditor-changeBase-word-background: #6f1313;
--vscode-mergeEditor-conflict-handled-minimapOverViewRuler: #adaca8ee;
--vscode-mergeEditor-conflict-handledFocused-border: #c1c1c1cc;
--vscode-mergeEditor-conflict-handledUnfocused-border: #86868649;
--vscode-mergeEditor-conflict-input1-background: #40c8ae33;
--vscode-mergeEditor-conflict-input2-background: #40a6ff33;
--vscode-mergeEditor-conflict-unhandled-minimapOverViewRuler: #fcba03;
--vscode-mergeEditor-conflict-unhandledFocused-border: #ffa600;
--vscode-mergeEditor-conflict-unhandledUnfocused-border: #ffa6007a;
--vscode-mergeEditor-conflictingLines-background: #ffea0047;
--vscode-minimap-chatEditHighlight: #1f1f1f99;
--vscode-minimap-errorHighlight: #ff1212b3;
--vscode-minimap-findMatchHighlight: #d18616;
--vscode-minimap-foregroundOpacity: #000000;
--vscode-minimap-infoHighlight: #3794ff;
--vscode-minimap-selectionHighlight: #264f78;
--vscode-minimap-selectionOccurrenceHighlight: #676767;
--vscode-minimap-warningHighlight: #cca700;
--vscode-minimapGutter-addedBackground: #2ea043;
--vscode-minimapGutter-deletedBackground: #f85149;
--vscode-minimapGutter-modifiedBackground: #0078d4;
--vscode-minimapSlider-activeBackground: #bfbfbf33;
--vscode-minimapSlider-background: #79797933;
--vscode-minimapSlider-hoverBackground: #64646459;
--vscode-multiDiffEditor-background: #1f1f1f;
--vscode-multiDiffEditor-border: #2b2b2b;
--vscode-multiDiffEditor-headerBackground: #262626;
--vscode-notebook-cellBorderColor: #37373d;
--vscode-notebook-cellEditorBackground: #181818;
--vscode-notebook-cellInsertionIndicator: #0078d4;
--vscode-notebook-cellStatusBarItemHoverBackground: #ffffff26;
--vscode-notebook-cellToolbarSeparator: #80808059;
--vscode-notebook-editorBackground: #1f1f1f;
--vscode-notebook-focusedCellBorder: #0078d4;
--vscode-notebook-focusedEditorBorder: #0078d4;
--vscode-notebook-inactiveFocusedCellBorder: #37373d;
--vscode-notebook-selectedCellBackground: #37373d;
--vscode-notebook-selectedCellBorder: #37373d;
--vscode-notebook-symbolHighlightBackground: #ffffff0b;
--vscode-notebookEditorOverviewRuler-runningCellForeground: #89d185;
--vscode-notebookScrollbarSlider-activeBackground: #bfbfbf66;
--vscode-notebookScrollbarSlider-background: #79797966;
--vscode-notebookScrollbarSlider-hoverBackground: #646464b3;
--vscode-notebookStatusErrorIcon-foreground: #f85149;
--vscode-notebookStatusRunningIcon-foreground: #cccccc;
--vscode-notebookStatusSuccessIcon-foreground: #89d185;
--vscode-notificationCenter-border: #313131;
--vscode-notificationLink-foreground: #4daafc;
--vscode-notificationToast-border: #313131;
--vscode-notificationsErrorIcon-foreground: #f14c4c;
--vscode-notificationsInfoIcon-foreground: #3794ff;
--vscode-notificationsWarningIcon-foreground: #cca700;
--vscode-panel-dropBorder: #cccccc;
--vscode-panelSection-border: #2b2b2b;
--vscode-panelSection-dropBackground: #53595d80;
--vscode-panelSectionHeader-background: #80808033;
--vscode-panelStickyScroll-background: #181818;
--vscode-panelStickyScroll-shadow: #000000;
--vscode-panelTitleBadge-background: #0078d4;
--vscode-panelTitleBadge-foreground: #ffffff;
--vscode-peekView-border: #3794ff;
--vscode-peekViewEditorGutter-background: #1f1f1f;
--vscode-peekViewEditorStickyScroll-background: #1f1f1f;
--vscode-peekViewResult-fileForeground: #ffffff;
--vscode-peekViewResult-lineForeground: #bbbbbb;
--vscode-peekViewResult-selectionBackground: #3399ff33;
--vscode-peekViewResult-selectionForeground: #ffffff;
--vscode-peekViewTitle-background: #252526;
--vscode-peekViewTitleDescription-foreground: #ccccccb3;
--vscode-peekViewTitleLabel-foreground: #ffffff;
--vscode-pickerGroup-foreground: #3794ff;
--vscode-problemsErrorIcon-foreground: #f14c4c;
--vscode-problemsInfoIcon-foreground: #3794ff;
--vscode-problemsWarningIcon-foreground: #cca700;
--vscode-profileBadge-background: #4d4d4d;
--vscode-profileBadge-foreground: #ffffff;
--vscode-profiles-sashBorder: #2b2b2b;
--vscode-prompt-frontMatter-background: #191919;
--vscode-prompt-frontMatter-inactiveBackground: #1c1c1c;
--vscode-quickInputList-focusBackground: #04395e;
--vscode-quickInputList-focusForeground: #ffffff;
--vscode-quickInputList-focusIconForeground: #ffffff;
--vscode-quickInputTitle-background: #ffffff1b;
--vscode-radio-activeBackground: #2489db82;
--vscode-radio-activeBorder: #2488db;
--vscode-radio-activeForeground: #ffffff;
--vscode-radio-inactiveBorder: #ffffff33;
--vscode-radio-inactiveHoverBackground: #5a5d5e80;
--vscode-sash-hoverBorder: #0078d4;
--vscode-scmGraph-foreground1: #ffb000;
--vscode-scmGraph-foreground2: #dc267f;
--vscode-scmGraph-foreground3: #994f00;
--vscode-scmGraph-foreground4: #40b0a6;
--vscode-scmGraph-foreground5: #b66dff;
--vscode-scmGraph-historyItemBaseRefColor: #ea5c00;
--vscode-scmGraph-historyItemHoverAdditionsForeground: #81b88b;
--vscode-scmGraph-historyItemHoverDefaultLabelBackground: #616161;
--vscode-scmGraph-historyItemHoverDefaultLabelForeground: #cccccc;
--vscode-scmGraph-historyItemHoverDeletionsForeground: #c74e39;
--vscode-scmGraph-historyItemHoverLabelForeground: #ffffff;
--vscode-scmGraph-historyItemRefColor: #3794ff;
--vscode-scmGraph-historyItemRemoteRefColor: #b180d7;
--vscode-scrollbar-shadow: #000000;
--vscode-scrollbarSlider-activeBackground: #bfbfbf66;
--vscode-scrollbarSlider-background: #79797966;
--vscode-scrollbarSlider-hoverBackground: #646464b3;
--vscode-search-resultsInfoForeground: #cccccca6;
--vscode-searchEditor-findMatchBackground: #ea5c0038;
--vscode-searchEditor-textInputBorder: #3c3c3c;
--vscode-settings-checkboxBackground: #313131;
--vscode-settings-checkboxBorder: #3c3c3c;
--vscode-settings-checkboxForeground: #cccccc;
--vscode-settings-dropdownForeground: #cccccc;
--vscode-settings-dropdownListBorder: #454545;
--vscode-settings-focusedRowBackground: #2a2d2e99;
--vscode-settings-focusedRowBorder: #0078d4;
--vscode-settings-headerBorder: #2b2b2b;
--vscode-settings-numberInputBackground: #313131;
--vscode-settings-numberInputBorder: #3c3c3c;
--vscode-settings-numberInputForeground: #cccccc;
--vscode-settings-rowHoverBackground: #2a2d2e4d;
--vscode-settings-sashBorder: #2b2b2b;
--vscode-settings-settingsHeaderHoverForeground: #ffffffb3;
--vscode-settings-textInputBackground: #313131;
--vscode-settings-textInputBorder: #3c3c3c;
--vscode-settings-textInputForeground: #cccccc;
--vscode-sideBar-dropBackground: #53595d80;
--vscode-sideBarActivityBarTop-border: #2b2b2b;
--vscode-sideBarStickyScroll-background: #181818;
--vscode-sideBarStickyScroll-shadow: #000000;
--vscode-sideBarTitle-background: #181818;
--vscode-sideBySideEditor-horizontalBorder: #ffffff17;
--vscode-sideBySideEditor-verticalBorder: #ffffff17;
--vscode-simpleFindWidget-sashBorder: #454545;
--vscode-statusBar-debuggingBorder: #2b2b2b;
--vscode-statusBar-noFolderBorder: #2b2b2b;
--vscode-statusBar-noFolderForeground: #cccccc;
--vscode-statusBarItem-activeBackground: #ffffff2e;
--vscode-statusBarItem-compactHoverBackground: #ffffff33;
--vscode-statusBarItem-errorBackground: #b91007;
--vscode-statusBarItem-errorForeground: #ffffff;
--vscode-statusBarItem-errorHoverBackground: #ffffff1f;
--vscode-statusBarItem-errorHoverForeground: #cccccc;
--vscode-statusBarItem-hoverBackground: #ffffff1f;
--vscode-statusBarItem-hoverForeground: #cccccc;
--vscode-statusBarItem-offlineBackground: #6c1717;
--vscode-statusBarItem-offlineForeground: #ffffff;
--vscode-statusBarItem-offlineHoverBackground: #ffffff1f;
--vscode-statusBarItem-offlineHoverForeground: #cccccc;
--vscode-statusBarItem-prominentForeground: #cccccc;
--vscode-statusBarItem-prominentHoverBackground: #ffffff1f;
--vscode-statusBarItem-prominentHoverForeground: #cccccc;
--vscode-statusBarItem-remoteHoverBackground: #ffffff1f;
--vscode-statusBarItem-remoteHoverForeground: #cccccc;
--vscode-statusBarItem-warningBackground: #7a6400;
--vscode-statusBarItem-warningForeground: #ffffff;
--vscode-statusBarItem-warningHoverBackground: #ffffff1f;
--vscode-statusBarItem-warningHoverForeground: #cccccc;
--vscode-symbolIcon-arrayForeground: #cccccc;
--vscode-symbolIcon-booleanForeground: #cccccc;
--vscode-symbolIcon-classForeground: #ee9d28;
--vscode-symbolIcon-colorForeground: #cccccc;
--vscode-symbolIcon-constantForeground: #cccccc;
--vscode-symbolIcon-constructorForeground: #b180d7;
--vscode-symbolIcon-enumeratorForeground: #ee9d28;
--vscode-symbolIcon-enumeratorMemberForeground: #75beff;
--vscode-symbolIcon-eventForeground: #ee9d28;
--vscode-symbolIcon-fieldForeground: #75beff;
--vscode-symbolIcon-fileForeground: #cccccc;
--vscode-symbolIcon-folderForeground: #cccccc;
--vscode-symbolIcon-functionForeground: #b180d7;
--vscode-symbolIcon-interfaceForeground: #75beff;
--vscode-symbolIcon-keyForeground: #cccccc;
--vscode-symbolIcon-keywordForeground: #cccccc;
--vscode-symbolIcon-methodForeground: #b180d7;
--vscode-symbolIcon-moduleForeground: #cccccc;
--vscode-symbolIcon-namespaceForeground: #cccccc;
--vscode-symbolIcon-nullForeground: #cccccc;
--vscode-symbolIcon-numberForeground: #cccccc;
--vscode-symbolIcon-objectForeground: #cccccc;
--vscode-symbolIcon-operatorForeground: #cccccc;
--vscode-symbolIcon-packageForeground: #cccccc;
--vscode-symbolIcon-propertyForeground: #cccccc;
--vscode-symbolIcon-referenceForeground: #cccccc;
--vscode-symbolIcon-snippetForeground: #cccccc;
--vscode-symbolIcon-stringForeground: #cccccc;
--vscode-symbolIcon-structForeground: #cccccc;
--vscode-symbolIcon-textForeground: #cccccc;
--vscode-symbolIcon-typeParameterForeground: #cccccc;
--vscode-symbolIcon-unitForeground: #cccccc;
--vscode-symbolIcon-variableForeground: #75beff;
--vscode-tab-activeModifiedBorder: #3399cc;
--vscode-tab-dragAndDropBorder: #ffffff;
--vscode-tab-inactiveModifiedBorder: #3399cc80;
--vscode-tab-unfocusedActiveBackground: #1f1f1f;
--vscode-tab-unfocusedActiveForeground: #ffffff80;
--vscode-tab-unfocusedActiveModifiedBorder: #3399cc80;
--vscode-tab-unfocusedInactiveBackground: #181818;
--vscode-tab-unfocusedInactiveForeground: #9d9d9d80;
--vscode-tab-unfocusedInactiveModifiedBorder: #3399cc40;
--vscode-terminal-ansiBlack: #000000;
--vscode-terminal-ansiBlue: #2472c8;
--vscode-terminal-ansiBrightBlack: #666666;
--vscode-terminal-ansiBrightBlue: #3b8eea;
--vscode-terminal-ansiBrightCyan: #29b8db;
--vscode-terminal-ansiBrightGreen: #23d18b;
--vscode-terminal-ansiBrightMagenta: #d670d6;
--vscode-terminal-ansiBrightRed: #f14c4c;
--vscode-terminal-ansiBrightWhite: #e5e5e5;
--vscode-terminal-ansiBrightYellow: #f5f543;
--vscode-terminal-ansiCyan: #11a8cd;
--vscode-terminal-ansiGreen: #0dbc79;
--vscode-terminal-ansiMagenta: #bc3fbc;
--vscode-terminal-ansiRed: #cd3131;
--vscode-terminal-ansiWhite: #e5e5e5;
--vscode-terminal-ansiYellow: #e5e510;
--vscode-terminal-border: #2b2b2b;
--vscode-terminal-dropBackground: #53595d80;
--vscode-terminal-findMatchBackground: #9e6a03;
--vscode-terminal-findMatchHighlightBackground: #ea5c0055;
--vscode-terminal-hoverHighlightBackground: #264f7820;
--vscode-terminal-initialHintForeground: #ffffff56;
--vscode-terminal-selectionBackground: #264f78;
--vscode-terminalCommandDecoration-defaultBackground: #ffffff40;
--vscode-terminalCommandDecoration-errorBackground: #f14c4c;
--vscode-terminalCommandDecoration-successBackground: #1b81a8;
--vscode-terminalCommandGuide-foreground: #37373d;
--vscode-terminalOverviewRuler-border: #010409;
--vscode-terminalOverviewRuler-cursorForeground: #a0a0a0cc;
--vscode-terminalOverviewRuler-findMatchForeground: #d186167e;
--vscode-terminalStickyScrollHover-background: #2a2d2e;
--vscode-terminalSymbolIcon-aliasForeground: #b180d7;
--vscode-terminalSymbolIcon-argumentForeground: #75beff;
--vscode-terminalSymbolIcon-fileForeground: #cccccc;
--vscode-terminalSymbolIcon-flagForeground: #ee9d28;
--vscode-terminalSymbolIcon-folderForeground: #cccccc;
--vscode-terminalSymbolIcon-methodForeground: #b180d7;
--vscode-terminalSymbolIcon-optionForeground: #ee9d28;
--vscode-terminalSymbolIcon-optionValueForeground: #75beff;
--vscode-testing-coverCountBadgeBackground: #616161;
--vscode-testing-coverCountBadgeForeground: #f8f8f8;
--vscode-testing-coveredBackground: #9ccc2c33;
--vscode-testing-coveredBorder: #9ccc2c26;
--vscode-testing-coveredGutterBackground: #9ccc2c1f;
--vscode-testing-iconErrored: #f14c4c;
--vscode-testing-iconErrored-retired: #f14c4cb3;
--vscode-testing-iconFailed: #f14c4c;
--vscode-testing-iconFailed-retired: #f14c4cb3;
--vscode-testing-iconPassed: #73c991;
--vscode-testing-iconPassed-retired: #73c991b3;
--vscode-testing-iconQueued: #cca700;
--vscode-testing-iconQueued-retired: #cca700b3;
--vscode-testing-iconSkipped: #848484;
--vscode-testing-iconSkipped-retired: #848484b3;
--vscode-testing-iconUnset: #848484;
--vscode-testing-iconUnset-retired: #848484b3;
--vscode-testing-message-error-badgeBackground: #f14c4c;
--vscode-testing-message-error-badgeBorder: #f14c4c;
--vscode-testing-message-error-badgeForeground: #000000;
--vscode-testing-message-info-decorationForeground: #cccccc80;
--vscode-testing-messagePeekBorder: #3794ff;
--vscode-testing-messagePeekHeaderBackground: #3794ff1a;
--vscode-testing-peekBorder: #f14c4c;
--vscode-testing-peekHeaderBackground: #f14c4c1a;
--vscode-testing-runAction: #73c991;
--vscode-testing-uncoveredBackground: #ff000033;
--vscode-testing-uncoveredBorder: #ff000026;
--vscode-testing-uncoveredBranchBackground: #781212;
--vscode-testing-uncoveredGutterBackground: #ff00004d;
--vscode-toolbar-activeBackground: #63666750;
--vscode-toolbar-hoverBackground: #5a5d5e50;
--vscode-tree-inactiveIndentGuidesStroke: #58585866;
--vscode-tree-indentGuidesStroke: #585858;
--vscode-tree-tableColumnsBorder: #cccccc20;
--vscode-tree-tableOddRowsBackground: #cccccc0a;
--vscode-walkThrough-embeddedEditorBackground: #00000066;
--vscode-walkthrough-stepTitle-foreground: #ffffff;
--vscode-welcomePage-progress-background: #313131;
--vscode-welcomePage-tileBorder: #ffffff1a;
--vscode-welcomePage-tileHoverBackground: #262626;
--vscode-widget-shadow: #0000005c;
}
