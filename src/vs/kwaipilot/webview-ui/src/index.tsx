import * as ReactDOM from "react-dom/client";
import { ChakraProvider } from "@chakra-ui/react";
import App from "@/App";
import theme from "@/utils/theme";
import CodeImage from "@/components/Prediction/codeImage";
import { initHighlighterInstance } from "./utils/highlighter";
import { ConfigProvider as AntConfigProvider } from "antd";
import { IdeEnvProvider } from "./providers/IdeEnvProvider";
import { getRootContainer } from "./utils/dom";

initHighlighterInstance().then(() => {
  const rootElement = getRootContainer();

  ReactDOM.createRoot(rootElement).render(
    <AntConfigProvider
      theme={{
        token: {
          fontFamily: "var(--vscode-font-family)",
        },
      }}
    >
      <ChakraProvider theme={theme}>
        <IdeEnvProvider value={{ isKwaiPilotIDE: false }}>
          <CodeImage />
          <App />
        </IdeEnvProvider>
      </ChakraProvider>
    </AntConfigProvider>,
  );
});
