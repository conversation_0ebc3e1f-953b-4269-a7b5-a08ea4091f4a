# VSCode Services 集成使用指南

## 概述

本文档介绍如何在 KwaiPilot WebView UI 中集成和使用 VSCode 的各种服务，包括命令服务、配置服务、日志服务等。

## 核心特性

### 1. 预准备服务架构

通过 `mountApp` 函数的第三个参数传递预准备好的 VSCode 服务实例，避免运行时通过字符串 ID 查找服务而导致的错误。

### 2. React Hooks 集成

提供了一系列 React hooks 来方便地在组件中使用 VSCode 服务：

- `useVSCodeServices()` - 获取 services accessor
- `useCommandService()` - 命令服务
- `useConfigurationService()` - 配置服务
- `useLogService()` - 日志服务
- `useThemeService()` - 主题服务
- `useStorageService()` - 存储服务
- `useNotificationService()` - 通知服务
- `useViewsService()` - 视图服务
- `useWorkspaceService()` - 工作区服务

### 3. 类型安全与错误处理

所有服务调用都包含完整的错误处理，确保应用的稳定性。

## 架构设计

### 服务预准备方式

在 `aiAssistant.contribution.ts` 中，直接创建服务包装器：

```typescript
const servicesAccessor = {
  // 命令服务
  commandService: {
    executeCommand: async (commandId: string, ...args: any[]) => {
      try {
        return await this.commandService.executeCommand(commandId, ...args);
      } catch (error) {
        this.logService.error('Failed to execute command:', commandId, error);
        throw error;
      }
    }
  },

  // 配置服务
  configurationService: {
    getValue: <T>(section: string): T | undefined => {
      try {
        return this.configurationService.getValue<T>(section);
      } catch (error) {
        this.logService.error('Failed to get configuration:', section, error);
        return undefined;
      }
    },
    updateValue: async (section: string, value: any): Promise<void> => {
      // 实现配置更新逻辑
    }
  },

  // 其他服务...
};

mountApp(this.rootContainer, this.bridgeImpl, servicesAccessor);
```

## 使用方法

### 1. 在 React 组件中使用

#### 使用命令服务

```typescript
import { useCommandService } from '@/hooks/useVSCodeServices';

export const MyComponent: React.FC = () => {
  const commandService = useCommandService();

  const handleOpenSettings = async () => {
    if (commandService) {
      try {
        await commandService.executeCommand('workbench.action.openSettings');
      } catch (error) {
        console.error('Failed to open settings:', error);
      }
    }
  };

  return (
    <button
      onClick={handleOpenSettings}
      disabled={!commandService}
    >
      打开设置
    </button>
  );
};
```

#### 使用配置服务

```typescript
import { useConfigurationService } from '@/hooks/useVSCodeServices';

export const ConfigComponent: React.FC = () => {
  const configService = useConfigurationService();

  const getFontSize = () => {
    if (configService) {
      return configService.getValue<number>('editor.fontSize') || 14;
    }
    return 14;
  };

  const updateFontSize = async (newSize: number) => {
    if (configService) {
      try {
        await configService.updateValue('editor.fontSize', newSize);
      } catch (error) {
        console.error('Failed to update font size:', error);
      }
    }
  };

  return (
    <div>
      <p>当前字体大小: {getFontSize()}</p>
      <button
        onClick={() => updateFontSize(16)}
        disabled={!configService}
      >
        设置字体大小为 16
      </button>
    </div>
  );
};
```

#### 使用日志服务

```typescript
import { useLogService } from '@/hooks/useVSCodeServices';

export const LoggingComponent: React.FC = () => {
  const logService = useLogService();

  const logMessage = () => {
    if (logService) {
      logService.info('这是一条信息日志');
      logService.warn('这是一条警告日志');
      logService.error('这是一条错误日志');
      logService.debug('这是一条调试日志');
    } else {
      // 降级到 console
      console.log('日志服务不可用');
    }
  };

  return (
    <button onClick={logMessage}>
      记录日志
    </button>
  );
};
```

#### 使用主题服务

```typescript
import { useThemeService } from '@/hooks/useVSCodeServices';

export const ThemeComponent: React.FC = () => {
  const themeService = useThemeService();

  const getCurrentTheme = () => {
    if (themeService) {
      const theme = themeService.getColorTheme();
      return `当前主题: ${theme.label} (${theme.type})`;
    }
    return '主题信息不可用';
  };

  return (
    <div>
      <p>{getCurrentTheme()}</p>
    </div>
  );
};
```

### 2. 服务可用性检查

每个 hook 都会返回服务实例或 `null`，可以用于检查服务可用性：

```typescript
const commandService = useCommandService();
const isCommandServiceAvailable = !!commandService;

// 根据服务可用性渲染不同内容
if (!commandService) {
  return <div>命令服务不可用</div>;
}
```

### 3. 获取任意服务（备用方案）

如需获取未预定义的服务，可以使用 `getService` 方法：

```typescript
import { useVSCodeServices } from '@/hooks/useVSCodeServices';

export const CustomServiceComponent: React.FC = () => {
  const accessor = useVSCodeServices();

  const customService = accessor?.getService?.(YourServiceToken);

  if (!customService) {
    return <div>自定义服务不可用</div>;
  }

  return (
    <div>
      {/* 使用自定义服务 */}
    </div>
  );
};
```

## 优势对比

### 旧方案的问题
- 通过字符串 ID 运行时查找服务
- 容易出现 "unknown service" 错误
- 类型安全性差

### 新方案的优势
- ✅ **预先准备**: 在构建时就准备好服务实例
- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **错误处理**: 完善的错误捕获和降级机制
- ✅ **性能优化**: 避免运行时服务查找
- ✅ **稳定性**: 减少运行时错误

## 错误处理策略

1. **服务级错误处理**: 每个服务方法都包含 try-catch
2. **降级方案**: 服务不可用时提供备选方案
3. **日志记录**: 所有错误都会记录到 VSCode 日志
4. **用户体验**: UI 元素根据服务可用性禁用/启用

## 扩展新服务

要添加新的 VSCode 服务支持：

1. **在 `mount-export.tsx` 中**：
   ```typescript
   export interface VSCodeServicesAccessor {
     // 添加新服务接口
     yourNewService?: {
       someMethod(): void;
     };
   }
   ```

2. **在 `aiAssistant.contribution.ts` 中**：
   ```typescript
   const servicesAccessor = {
     // 添加新服务实现
     yourNewService: {
       someMethod: () => {
         // 实现逻辑
       }
     }
   };
   ```

3. **在 `useVSCodeServices.ts` 中**：
   ```typescript
   export const useYourNewService = () => {
     const accessor = useVSCodeServices();
     return accessor?.yourNewService || null;
   };
   ```

## 示例组件

查看 `src/components/VSCodeServicesExample.tsx` 了解完整的使用示例，包括：

- 服务可用性检查
- 错误处理演示
- 实际功能调用示例

## 开发调试

在开发过程中，可以：

- 查看浏览器控制台的日志信息
- 检查 VSCode 输出面板的日志
- 使用示例组件测试各个服务的功能
