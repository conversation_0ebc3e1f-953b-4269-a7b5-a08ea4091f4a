import { useMemo } from "react";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
// import { ExtensionAction } from "@shared/types/channel/extension";
import { useUserStore } from "@/store/user";
import { Tooltip } from "@/components/Union/chakra-ui";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { chakra } from "@chakra-ui/react";
import { useContextHeaderContext } from "../UserInputTextarea/ContextHeader/ContextHeaderContext";
import { Icon } from "@/components/Union/t-iconify";

export const UploadFileBtn: React.FC = () => {
  const userInfo = useUserStore(state => state.userInfo);

  const { state: contextHeaderState } = useContextHeaderContext();

  const handleUpload = async () => {
    if (!userInfo) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "请先登录",
      });
      return;
    }
    if (disable) return;
    const params: ReportOpt<"uploadFile"> = {
      key: "uploadFile",
      type: undefined,
    };
    reportUserAction(params);
    const files = await kwaiPilotBridgeAPI.extensionMisc.$uploadFile();
    files.forEach((file) => {
      console.log("上传的文件信息", file);
      contextHeaderState.tryInsertNode({
        structure: {
          type: "remoteFile",
          uri: file.uri,
          relativePath: file.relativePath,
          uploadInfo: file.uploadInfo,
        },
        isVirtualContext: false,
        followActiveEditor: false,
      }, {
        source: "context",
      });
    });
  };

  const disable = useMemo(() => {
    return !userInfo;
  }, [userInfo]);

  return (
    <>
      <Tooltip label="支持上传多个文件（总大小不超过 3MB），支持 .pdf、.docx、.txt 代码文件 等">

        <chakra.button
          className="cursor-pointer flex items-center w-6 h-6 bg-transparent text-icon-foreground rounded hover:bg-toolbar-hoverBackground disabled:bg-transparent justify-center"
          onClick={handleUpload}
          disabled={disable}
        >
          <Icon icon="codicon:new-file" width="16" height="16" color="inherit" />
        </chakra.button>
      </Tooltip>
    </>
  );
};
