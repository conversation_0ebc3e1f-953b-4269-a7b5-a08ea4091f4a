import { createContext, useContext } from "react";
import { UserInputTextareaProps } from "./UserInputTextArea";

export const UserInputTextAreaContext = createContext<{
  role: UserInputTextareaProps["role"];
} | null>(null);

export function useUserInputTextAreaContext() {
  const context = useContext(UserInputTextAreaContext);
  if (!context) {
    throw new Error("UserInputTextAreaContext is not found");
  }
  return context;
}
