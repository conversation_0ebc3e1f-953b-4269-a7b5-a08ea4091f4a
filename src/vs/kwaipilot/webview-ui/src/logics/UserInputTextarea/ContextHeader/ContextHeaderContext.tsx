import { reportUserAction } from "@/utils/weblogger";
import { ReportKeys, ReportOpt } from "@shared/types/logger";
import { createContext, Dispatch, SetStateAction, useCallback, useContext, useMemo, useState } from "react";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";

/**
 * context header 需要对元素去重，这是去重的核心逻辑
 * @param a
 * @param b
 * @returns
 */
export function isIdentical(a: MentionNodeV2Structure, b: MentionNodeV2Structure): boolean {
  if (!(a.type === b.type && a.uri === b.uri)) {
    return false;
  }
  if (a.type === "selection" && b.type === "selection") {
    return a.range.start.line === b.range.start.line && a.range.end.line === b.range.end.line;
  }
  return true;
}

export function mentionNodeUniqueKey(structure: MentionNodeV2Structure) {
  const base = `${structure.type}_${structure.uri}`;
  if (structure.type === "selection") {
    return `${base}_${structure.range.start.line}_${structure.range.end.line}`;
  }
  return base;
}

export interface ContextHeaderState {
  nodes: ContextHeaderItem[];
  setNodes: Dispatch<SetStateAction<ContextHeaderItem[]>>;
  /**
   * 尝试插入节点 如果存在则不插入
   *
   * 合并相同类型的节点
   * @param node
   * @returns
   */
  tryInsertNode: (half: {
    structure: MentionNodeV2Structure;
    followActiveEditor: boolean;
    isVirtualContext: boolean;
  }, extra: {
      source: ReportKeys["input_add_context"];
    }) => void;

  /**
   * 查找是否已经存在
   */
  findNode: (structure: MentionNodeV2Structure) => ContextHeaderItem | undefined;

  /**
   * 尝试删除节点
   */
  tryDeleteNode: (structure: MentionNodeV2Structure) => void;

  /**
   * 上下文是否被用户修改 如果被修改 则上下文不会根据当前文件绑定
   */
  markAsModified: (modified?: boolean) => void;

  isModified: boolean;
}

export interface ContextHeaderItem {
  structure: MentionNodeV2Structure;
  followActiveEditor: boolean;
  /**
   * shadow context 会绑定在 用户输入的节点上，如果用户输入的节点被删除，则 shadow context 也会被删除
   */
  isVirtualContext: boolean;
}

export function useContextHeaderState({
  initialNodes,
  sessionId,
}: {
  initialNodes?: ContextHeaderItem[];
  sessionId: string;
}) {
  const [nodes, _setNodes] = useState<ContextHeaderItem[]>(initialNodes || []);
  const setNodes = useCallback<Dispatch<SetStateAction<ContextHeaderItem[]>>>((p) => {
    if (typeof p === "function") {
      _setNodes((prev) => {
        const res = p(prev);
        return res;
      });
    }
    else {
      _setNodes(p);
    }
  }, []);

  const [isModified, setIsModified] = useState(false);

  const markAsModified = useCallback((modified: boolean = true) => {
    if (modified) {
    // 将所有节点的 followActiveEditor 设置为 false
      setNodes(nodes => nodes.map(node => ({
        ...node,
        followActiveEditor: false,
      })));
    }
    setIsModified(modified);
  }, [setNodes]);

  const tryInsertNode = useCallback((node: ContextHeaderItem, {
    source,
  }: {
    /**
     * 从什么位置添加的
     */
    source: ReportKeys["input_add_context"];
  }) => {
    const existingNode = nodes.find(item => isIdentical(item.structure, node.structure));

    const CONTEXT_TYPE_MAP: Record<MentionNodeV2Structure["type"], string> = {
      file: "file",
      remoteFile: "remoteFile",
      rule: "rule",
      tree: "dir",
      selection: "selection",
      web: "web",
      codebase: "codebase",
      knowledge: "knowledge",
      slashCommand: "slashCommand",
    };
    const doReport = () => {
      const parms: ReportOpt<"input_add_context"> = {
        key: "input_add_context",
        type: source,
        content: CONTEXT_TYPE_MAP[node.structure.type],
      };
      reportUserAction(parms, /* 输入框没有 chatId */"", sessionId);
    };
    if (!existingNode) {
      setNodes(prev => [...prev, node]);
      markAsModified();
      doReport();
    }
    else if (node.isVirtualContext && existingNode.followActiveEditor && !existingNode.isVirtualContext) {
      /* 如果输入框带入的 node 和当前文件相同, 要原地替换,  并且此时如果切换文件,要保留输入框输入的,同时再增加一个当前文件 ) */
      setNodes(prev => prev.map(item => item === existingNode
        ? {
            ...existingNode,
            isVirtualContext: true,
          }
        : item));
      doReport();
    }
    else if (
      /* 1. 添加之前是 virtual 则需要替换为非 virtual 2. selection 要按照最新的内容 @see B2489967 */
      (existingNode.isVirtualContext && !node.isVirtualContext)
      || existingNode.structure.type === "selection"
    ) {
      setNodes(prev => prev.map(item => item === existingNode ? node : item));
      markAsModified();
      doReport();
    }
  }, [nodes, sessionId, setNodes, markAsModified]);

  const nodeMap = useMemo(() => {
    return Object.fromEntries(nodes.map(item => [mentionNodeUniqueKey(item.structure), item]));
  }, [nodes]);

  const findNode = useCallback((structure: MentionNodeV2Structure) => {
    return nodeMap[mentionNodeUniqueKey(structure)];
  }, [nodeMap]);

  const tryDeleteNode = useCallback((structure: MentionNodeV2Structure) => {
    setNodes(prev => prev.filter(node => !isIdentical(node.structure, structure)));
    markAsModified();
  }, [markAsModified, setNodes]);

  const state = useMemo<ContextHeaderState>(() => ({
    nodes,
    setNodes,
    tryInsertNode,
    findNode,
    tryDeleteNode,
    markAsModified,
    isModified,
  }), [nodes, setNodes, tryInsertNode, findNode, tryDeleteNode, markAsModified, isModified]);

  return state;
}

export const ContextHeaderContext = createContext<{
  state: ContextHeaderState;
} | null>(null);

export function useContextHeaderContext() {
  const context = useContext(ContextHeaderContext);
  if (!context) {
    throw new Error("ContextHeaderContext is not found");
  }
  return context;
}
