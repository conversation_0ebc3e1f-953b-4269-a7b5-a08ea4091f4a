import { Dispatch, SetStateAction } from "react";
import { ContextHeaderItem, isIdentical } from "./ContextHeaderContext";
import { produce } from "immer";
import { collectMentionNodeV2 } from "./collectMentionNode";
import { LexicalEditor } from "lexical";

/**
 * 根据当前的编辑器状态更新上下文内容. 例如编辑器输入新的文件后, 上下文区域也要同步展示
 * @param param0
 */
export function updateContextNodesByEditor({
  nodes,
  setNodes,
  markAsModified,
  editor,
}: {
  nodes: ContextHeaderItem[];
  setNodes: Dispatch<SetStateAction<ContextHeaderItem[]>>;
  markAsModified: () => void;
  editor: LexicalEditor;
}) {
  let shouldMarkAsModified = false;
  let draftModified = false;

  const editorNodes = collectMentionNodeV2(editor).filter(v => v.__structure.type !== "slashCommand");
  const modifiedNodes = produce(nodes, (draft) => {
    const toBeDeleted: ContextHeaderItem[] = [];
    // 尝试删除虚拟节点
    for (const node of draft.filter(n => n.isVirtualContext && !n.followActiveEditor)) {
      if (!editorNodes.some(n => isIdentical(n.__structure, node.structure))) {
        toBeDeleted.push(node);
        draftModified = true;
      }
    }
    const toBeAdded: ContextHeaderItem[] = [];
    for (const editorNode of editorNodes) {
      // 尝试合并
      const target = draft.find(v => isIdentical(v.structure, editorNode.__structure));
      if (!target) {
        toBeAdded.push({
          structure: editorNode.__structure,
          followActiveEditor: false,
          isVirtualContext: true,
        });
        draftModified = true;
        // 如果有新增节点，标记为已修改
        shouldMarkAsModified = true;
      }
    }
    for (const node of toBeDeleted) {
      draft.splice(draft.indexOf(node), 1);
    }
    for (const node of toBeAdded) {
      draft.push(node);
    }
  });
  if (draftModified) {
    setNodes(modifiedNodes);
  }

  if (shouldMarkAsModified) {
    markAsModified();
  }
}
