import {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  RefObject,
} from "react";
import { MenuOption } from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { TextNode } from "lexical";
import { Input } from "@chakra-ui/react";
import { DOM } from "@/utils/dom";

export interface MentionPanelControllerProps<TOption extends MenuOption> {
  onQueryChange: (matchingString: string | null) => void;
  onSelectOption: (
    option: TOption,
    textNodeContainingQuery: TextNode | null,
    closeMenu: () => void,
    matchingString: string
  ) => void;
  options: Array<TOption>;
  menuRenderFn: (
    itemProps: {
      selectedIndex: number | null;
      selectOptionAndCleanUp: (option: TOption) => void;
      setHighlightedIndex: (index: number) => void;
      options: Array<TOption>;
    },
    bottomElement: JSX.Element
  ) => JSX.Element;
  onClose: () => void;
  inputRef?: RefObject<HTMLInputElement>;
}

export function MentionPanelController<TOption extends MenuOption>({
  onClose,
  onQueryChange,
  onSelectOption,
  options,
  inputRef,
  menuRenderFn,
}: MentionPanelControllerProps<TOption>) {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          setSelectedIndex((prev) => {
            if (prev === null) return 0;
            return prev < options.length - 1 ? prev + 1 : 0;
          });
          break;
        case "ArrowUp":
          event.preventDefault();
          setSelectedIndex((prev) => {
            if (prev === null) return options.length - 1;
            return prev > 0 ? prev - 1 : options.length - 1;
          });
          break;
        case "Enter":
          event.preventDefault();
          if (selectedIndex !== null && options[selectedIndex]) {
            onSelectOption(options[selectedIndex], null, onClose, "");
          }
          break;
        case "Escape":
          event.preventDefault();
          onClose();
          break;
      }
    },
    [selectedIndex, options, onClose, onSelectOption],
  );

  useEffect(() => {
    DOM.registerKeydownEvent(handleKeyDown);
    return () => DOM.unregisterKeydownEvent(handleKeyDown);
  }, [handleKeyDown]);

  const selectOptionAndCleanUp = useCallback(
    (selectedEntry: TOption) => {
      onSelectOption(selectedEntry, null, close, "");
    },
    [onSelectOption],
  );

  const listItemProps = useMemo(
    () => ({
      options,
      selectOptionAndCleanUp,
      selectedIndex,
      setHighlightedIndex: setSelectedIndex,
    }),
    [options, selectOptionAndCleanUp, selectedIndex],
  );

  return (
    <div ref={menuRef} className=" relative">
      {menuRenderFn(
        listItemProps,
        <div className=" mx-auto w-[calc(100%-4px)] h-[28px] z-10 mb-[2px]">
          <Input
            ref={inputRef}
            placeholder="请输入"
            className="box-border"
            width="full"
            height="full"
            _focus={{
              outlineColor: "transparent",
            }}
            borderWidth={0}
            _focusVisible={{
              borderColor: "inherit",
            }}
            fontSize="12px"
            outline="none"
            rounded="3px"
            onChange={e => onQueryChange(e.target.value || null)}
          />
        </div>,
      )}
    </div>
  );
}
