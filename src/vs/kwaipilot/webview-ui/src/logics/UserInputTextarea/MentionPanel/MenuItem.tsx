import clsx from "clsx";
import {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { HighlightText } from "./Highlight";
import AutoTooltip from "@/components/AutoTooltip";
import { MentionTypeaheadOption, TypeaheadMenuOptionType } from "./useOptions";
import { MentionPanelContext, useMentionPanelContext } from "./MentionPanelContext";
import { Spinner, useMergeRefs, useSize } from "@chakra-ui/react";
import KidIcon from "@/components/Union/kid";
import { useTypeaheadMenuContext } from "@/components/TextArea/lexical/MentionsV2PluginContext";
import { Icon } from "@/components/Union/t-iconify";
import { getIcon } from "@/utils/fileIcon";
import {
  MentionNodeV2Structure_File,
  MentionNodeV2Structure_Knowledge,
  MentionNodeV2Structure_Rule,
  MentionNodeV2Structure_Tree,
  placeholderMentionNodeV2Structure_Codebase,
  placeholderMentionNodeV2Structure_Web,
} from "shared/lib/MentionNodeV2/nodes";

import IconArrowLeft from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_left";
import { useContextHeaderContext } from "../ContextHeader/ContextHeaderContext";
import { FilenameDisplay } from "@/logics/composer/tools/components/FilenameDisplay";
import { ConfigProvider } from "antd";
import { ReportOpt } from "@shared/types/logger";
import { reportUserAction } from "@/utils/weblogger";
import { MenuItemUI } from "@/components/ui/MentionPanelMenuItemUI";

export interface MenuItemProps {
  data: MentionTypeaheadOption;
  index: number;
  disabled: boolean;
  disabledMsg: string;
  children?: React.ReactNode;
}

export const MenuItem = forwardRef<HTMLButtonElement, MenuItemProps>(
  function MenuItem(
    { data, index, disabled, disabledMsg, children },
    forwardedRef,
  ) {
    const option = data;
    const context = useContext(MentionPanelContext);
    if (!context) {
      throw new Error("MentionPanelContext is not provided");
    }
    const { handleSelectMenu, query, selectIndex, submittingIndex } = context;
    const {
      itemProps: { setHighlightedIndex },
    } = useTypeaheadMenuContext();

    const isSecondary = false;

    const selected = index === selectIndex;

    const menuRef = useRef<HTMLButtonElement>(null);

    const composedRef = useMergeRefs(menuRef, forwardedRef);

    useEffect(() => {
      if (selected && menuRef.current) {
        menuRef.current.scrollIntoView({
          block: "nearest",
          behavior: "instant",
        });
      }
    }, [selected]);

    const handleHoverSelect = useCallback(() => {
      setHighlightedIndex(index);
    }, [index, setHighlightedIndex]);

    return (
      <button
        ref={composedRef}
        style={{
          /* FIXME: --vscode-font-family 是否应当在全局设置 */
          fontFamily: isSecondary ? "var(--vscode-font-family)" : "inherit",
        }}
        className={clsx(
          `group flex  items-center  px-3  w-full rounded-sm`,
          [isSecondary ? "h-[28px]" : "h-[36px]"],
          [isSecondary ? "leading-[16px]" : "leading-[19.5px]"],
          [selected ? "bg-list-hoverBackground" : ""],
          [disabled ? "cursor-not-allowed" : "cursor-pointer"],
        )}
        disabled={disabled}
        onClick={() => handleSelectMenu(data, index)}
        onMouseMove={handleHoverSelect}
        onMouseDown={(e) => {
          e.preventDefault();
        }}
      >
        {children || (
          <>
            <div
              className={clsx("overflow-hidden  mr-6 flex items-center", [
                disabled
                  ? "text-text-common-disable"
                  : "text-text-common-primary",
              ])}
            >
              <HighlightText text={option.name} highlight={query} />
            </div>
            <div
              className={clsx(
                "kwaipilot-rich-editor-menu-description",
                [
                  disabled
                    ? ""
                    : selected
                      ? "flex"
                      : isSecondary
                        ? ""
                        : "hidden",
                ],
                "flex-auto overflow-hidden",
                [
                  disabled
                    ? "text-text-common-disable"
                    : "text-text-common-secondary",
                ],
                ["justify-end", "flex"],
              )}
            >
              <AutoTooltip title={option.secondaryText} placement="top">
                {disabled ? disabledMsg : option.secondaryText}
              </AutoTooltip>
            </div>
            {submittingIndex === index && <Spinner ml="4px" size="xs" />}
          </>
        )}
      </button>
    );
  },
);

const OPTION_TYPE_LABELS: Record<TypeaheadMenuOptionType, string> = {
  [TypeaheadMenuOptionType.none]: "",
  [TypeaheadMenuOptionType.heading]: "heading",
  [TypeaheadMenuOptionType.header]: "header",
  [TypeaheadMenuOptionType.file]: "文件",
  [TypeaheadMenuOptionType.folder]: "目录",
  [TypeaheadMenuOptionType.knowledge]: "知识库",
  [TypeaheadMenuOptionType.rule]: "规则",
  [TypeaheadMenuOptionType.customPrompt]: "自定义指令",
  [TypeaheadMenuOptionType.addRule]: "添加规则",
  [TypeaheadMenuOptionType.web]: "联网",
  [TypeaheadMenuOptionType.codebase]: "代码库",
  [TypeaheadMenuOptionType.slashCommand]: "指令",
};

export const MenuItem_File = forwardRef<
  HTMLButtonElement,
  {
    data: MentionTypeaheadOption;
    index: number;
  }
>(function MenuItem_File({ data, index }, forwardedRef) {
  const context = useContext(MentionPanelContext);
  const { state: contextHeaderState } = useContextHeaderContext();
  if (!context) {
    throw new Error("MentionPanelContext is not provided");
  }
  const { handleSelectMenu, query, selectIndex, submittingIndex } = context;
  const {
    itemProps: { setHighlightedIndex },
  } = useTypeaheadMenuContext();

  const selected = index === selectIndex;

  const rootRef = useRef<HTMLButtonElement>(null);

  const containerSize = useSize(rootRef);

  const shadowTextForMeasurement = useRef<HTMLDivElement | null>(null);
  const shadowTextForMeasurementSize = useSize(shadowTextForMeasurement);
  const filenameLabelFullWidth = shadowTextForMeasurementSize?.width || 0;
  const filenameLabelMaxWidth = Math.floor((containerSize?.width || 400) * 0.6);
  const filenameLabelFinalWidth = Math.min(filenameLabelFullWidth, filenameLabelMaxWidth);

  useEffect(() => {
    if (selected) {
      rootRef.current?.scrollIntoView({
        behavior: "instant",
        block: "nearest",
      });
    }
  }, [selected]);

  const isInContextHeader = useMemo(() => {
    if (
      [
        TypeaheadMenuOptionType.file,
        TypeaheadMenuOptionType.folder,
        TypeaheadMenuOptionType.rule,
      ].includes(data.type)
      && data.structure
    ) {
      const structure = data.structure as
        | MentionNodeV2Structure_File
        | MentionNodeV2Structure_Tree
        | MentionNodeV2Structure_Rule;
      return !!contextHeaderState.findNode(structure);
    }
    return false;
  }, [data, contextHeaderState]);

  const handleHoverSelect = useCallback(() => {
    setHighlightedIndex(index);
  }, [index, setHighlightedIndex]);

  const fileIcon = useMemo(() => {
    if (data.type === TypeaheadMenuOptionType.file && data.structure) {
      const structure = data.structure as MentionNodeV2Structure_File;
      return getIcon(structure.relativePath, false);
    }
    else if (data.type === TypeaheadMenuOptionType.folder && data.structure) {
      const structure = data.structure as MentionNodeV2Structure_Tree;
      return getIcon(structure.relativePath, true);
    }
    else if (data.type === TypeaheadMenuOptionType.rule && data.structure) {
      return "lucide:file-code";
    }
    return "vscode-icons:file-type-text";
  }, [data]);

  const { selectMode } = useMentionPanelContext();

  return (
    <button
      ref={useMergeRefs(rootRef, forwardedRef)}
      style={{
        fontFamily: "var(--vscode-font-family)",
      }}
      className={clsx(
        "group flex items-center text-left px-3 gap-1 w-full rounded-sm h-[28px] leading-[16px] cursor-pointer focus-visible:outline-none relative",
        selected
          ? "bg-list-hoverBackground"
          : "",
      )}
      onClick={() => handleSelectMenu(data, index)}
      onMouseMove={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
    >
      <Icon icon={fileIcon} className="text-[14px] flex-none" />
      <div
        className={clsx(
          "overflow-hidden mr-2 flex items-center gap-1 flex-none text-menu-foreground",
        )}
        style={{
          width: filenameLabelFinalWidth + "px",
        }}
      >
        <ConfigProvider
          theme={{
            components: {
              Tooltip: {
                zIndexPopup: 10000 /* > var(--chakra-zIndices-popover) */,
              },
            },
          }}
        >
          <FilenameDisplay
            highlight={query}
            filename={data.name}
            maxSuffixLenth={5}
          >
          </FilenameDisplay>
          <div className="w-[500px] -z-10 opacity-0 pointer-events-none absolute left-2 top-0 text-left">
            <div ref={shadowTextForMeasurement} className=" inline-block">
              {data.name}
            </div>
          </div>
        </ConfigProvider>
      </div>
      <div
        className={clsx(
          "kwaipilot-rich-editor-menu-description flex-auto overflow-hidden text-descriptionForeground justify-end flex",
        )}
      >
        <AutoTooltip
          title={data.secondaryText || ""}
          openDelay={700}
          placement="top"
        >
          {data.secondaryText}
        </AutoTooltip>
      </div>
      {selectMode === "unique" && isInContextHeader && (
        <Icon icon="codicon:check" width="14" height="14" className="ml-auto flex-none" color="currentColor" />
      )}
      {submittingIndex === index && <Spinner ml="4px" size="xs" />}
    </button>
  );
});

export const MenuItem_Web = forwardRef<
  HTMLButtonElement,
  {
    data: MentionTypeaheadOption;
    index: number;
  }
>(function MenuItem_Web({ data, index }, forwardedRef) {
  const context = useContext(MentionPanelContext);
  const { state: contextHeaderState } = useContextHeaderContext();
  if (!context) {
    throw new Error("MentionPanelContext is not provided");
  }
  const { handleSelectMenu, selectIndex } = context;
  const {
    itemProps: { setHighlightedIndex },
  } = useTypeaheadMenuContext();

  const handleHoverSelect = useCallback(() => {
    setHighlightedIndex(index);
  }, [index, setHighlightedIndex]);
  const isInContextHeader = useMemo(() => {
    return !!contextHeaderState.findNode(placeholderMentionNodeV2Structure_Web);
  }, [contextHeaderState]);

  const { selectMode } = useMentionPanelContext();

  const selected = index === selectIndex;
  return (
    <MenuItemUI
      ref={forwardedRef}
      iconifyIcon="codicon:globe"
      isInContextHeader={isInContextHeader}
      selected={selected}
      selectMode={selectMode}
      onClick={() => handleSelectMenu(data, index)}
      onMouseMove={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
      className="px-2"
    >
      <span className=" text-menu-foreground text-xs">联网</span>
    </MenuItemUI>
  );
});

export const MenuItem_Codebase = forwardRef<
  HTMLButtonElement,
  {
    data: MentionTypeaheadOption;
    index: number;
  }
>(function MenuItem_Codebase({ data, index }, forwardedRef) {
  const context = useContext(MentionPanelContext);
  const { state: contextHeaderState } = useContextHeaderContext();
  if (!context) {
    throw new Error("MentionPanelContext is not provided");
  }
  const { handleSelectMenu, selectIndex } = context;
  const {
    itemProps: { setHighlightedIndex },
  } = useTypeaheadMenuContext();

  const handleHoverSelect = useCallback(() => {
    setHighlightedIndex(index);
  }, [index, setHighlightedIndex]);
  const isInContextHeader = useMemo(() => {
    return !!contextHeaderState.findNode(placeholderMentionNodeV2Structure_Codebase);
  }, [contextHeaderState]);

  const { selectMode } = useMentionPanelContext();

  const selected = index === selectIndex;
  return (
    <MenuItemUI
      ref={forwardedRef}
      iconifyIcon="codicon:code"
      isInContextHeader={isInContextHeader}
      selected={selected}
      selectMode={selectMode}
      onClick={() => handleSelectMenu(data, index)}
      onMouseMove={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
      className="px-2"
    >
      <span className=" text-menu-foreground text-xs">代码库</span>
    </MenuItemUI>
  );
});

export const MenuItem_SlashCommand = forwardRef<
  HTMLButtonElement,
  {
    data: MentionTypeaheadOption;
    index: number;
  }
>(function MenuItem_Codebase({ data, index }, forwardedRef) {
  const structure = (data as MentionTypeaheadOption<TypeaheadMenuOptionType.slashCommand>).structure;
  const context = useContext(MentionPanelContext);
  if (!context) {
    throw new Error("MentionPanelContext is not provided");
  }
  const { handleSelectMenu, selectIndex } = context;
  const {
    itemProps: { setHighlightedIndex },
  } = useTypeaheadMenuContext();

  const handleHoverSelect = useCallback(() => {
    setHighlightedIndex(index);
  }, [index, setHighlightedIndex]);

  const { selectMode } = useMentionPanelContext();

  const selected = index === selectIndex;
  return (
    <MenuItemUI
      ref={forwardedRef}
      isInContextHeader={false}
      selected={selected}
      selectMode={selectMode}
      onClick={() => handleSelectMenu(data, index)}
      onMouseMove={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
      className="px-2"
    >
      <span className=" text-menu-foreground text-xs">
        /
        {structure.label}
      </span>
    </MenuItemUI>
  );
});

export const MenuItem_Knowledge = forwardRef<
  HTMLButtonElement,
  {
    data: MentionTypeaheadOption;
    index: number;
  }
>(function MenuItem_Knowledge({ data, index }, forwardedRef) {
  const context = useContext(MentionPanelContext);
  const structure = data.structure as MentionNodeV2Structure_Knowledge;
  const { state: contextHeaderState } = useContextHeaderContext();
  if (!context) {
    throw new Error("MentionPanelContext is not provided");
  }
  const { handleSelectMenu, selectIndex } = context;
  const {
    itemProps: { setHighlightedIndex },
  } = useTypeaheadMenuContext();

  const handleHoverSelect = useCallback(() => {
    setHighlightedIndex(index);
  }, [index, setHighlightedIndex]);
  const isInContextHeader = useMemo(() => {
    return !!contextHeaderState.findNode(data.structure as MentionNodeV2Structure_Knowledge);
  }, [contextHeaderState, data.structure]);

  const { selectMode, query } = useMentionPanelContext();

  const selected = index === selectIndex;
  return (
    <MenuItemUI
      ref={forwardedRef}
      iconifyIcon="codicon:book"
      isInContextHeader={isInContextHeader}
      selected={selected}
      selectMode={selectMode}
      onClick={() => handleSelectMenu(data, index)}
      onMouseMove={handleHoverSelect}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
      className="px-2"
    >
      <div
        className={clsx(
          "overflow-hidden mr-6 gap-1 text-menu-foreground text-xs whitespace-nowrap text-ellipsis flex-auto text-left",
        )}
      >
        <HighlightText text={structure.doc.name} highlight={query} />
      </div>
    </MenuItemUI>
  );
});

function TagBeta() {
  return (
    <div className="w-[30px] h-[15px] border border-solid bg-textPreformat-background border-checkbox-border rounded-full relative">
      <div className="  text-foreground text-[20px] whitespace-nowrap  absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 scale-50">Beta</div>
    </div>
  );
}

export const MenuItem_Heading = forwardRef<HTMLButtonElement, MenuItemProps>(
  function MenuItem_Heading({ data, index, disabled, children }, forwardedRef) {
    const composedRef = useMergeRefs(forwardedRef);
    const context = useContext(MentionPanelContext);
    if (!context) {
      throw new Error("MentionPanelContext is not provided");
    }
    const { handleSelectMenu, query, selectIndex } = context;
    const {
      itemProps: { setHighlightedIndex },
    } = useTypeaheadMenuContext();

    const selected = index === selectIndex;

    return (
      <button
        ref={composedRef}
        className={clsx(
          `group flex  items-center  px-2 text-[12px]  w-full rounded-sm h-[26px] leading-[18px]`,
          [
            selected
              ? "bg-list-hoverBackground"
              : "",
          ],
          [disabled ? "cursor-not-allowed" : "cursor-pointer"],
        )}
        disabled={disabled}
        onClick={() => {
          handleSelectMenu(data, index);
        }}
        onMouseMove={() => {
          setHighlightedIndex(index);
        }}
        onMouseDown={(e) => {
          e.preventDefault();
        }}
      >
        {children || (
          <>
            <div
              className={clsx(
                "overflow-hidden w-full flex items-center gap-1",
                [
                  disabled
                    ? "text-text-common-disable"
                    : "",
                ],
              )}
            >
              {data.name === TypeaheadMenuOptionType.customPrompt
                ? undefined
                : (
                    <Icon
                      width={12}
                      height={12}
                      icon={data.name === TypeaheadMenuOptionType.folder
                        ? "codicon:folder"
                        : data.name === TypeaheadMenuOptionType.file
                          ? "codicon:file"
                          : data.name === TypeaheadMenuOptionType.rule
                            ? "codicon:symbol-ruler"
                            : data.name === TypeaheadMenuOptionType.knowledge
                              ? "codicon:book"
                              : "codicon:file"}
                    />
                  )}
              <HighlightText
                text={
                  OPTION_TYPE_LABELS[data.name as TypeaheadMenuOptionType]
                  || data.name
                }
                highlight={query}
              />
              {data.name === TypeaheadMenuOptionType.customPrompt && (
                <TagBeta />
              )}
              <Icon icon="codicon:chevron-right" width={12} className="ml-auto text-disabledForeground"></Icon>
            </div>
          </>
        )}
      </button>
    );
  },
);

export function MenuItem_Header({
  item,
  index,
}: {
  item: MentionTypeaheadOption<TypeaheadMenuOptionType.header>;
  index: number;
}) {
  const { selectIndex, handleSelectMenu } = useMentionPanelContext();
  const { itemProps: { setHighlightedIndex }, currentMenu } = useTypeaheadMenuContext();
  const handleAddRuleClick = useCallback(() => {
    handleSelectMenu(
      new MentionTypeaheadOption<TypeaheadMenuOptionType.addRule>(
        TypeaheadMenuOptionType.addRule,
        "",
        "",
        undefined,
      ),
      0,
    );
    const param: ReportOpt<"input_rules_create"> = {
      key: "input_rules_create",
      type: undefined,
      content: "new_composer",
    };
    reportUserAction(param);
  }, [handleSelectMenu]);
  if (currentMenu === TypeaheadMenuOptionType.none) {
    // 已经不能返回了，展示为只读
    return (
      <div className=" p-[3px] border-b-[0.5px] border-settings-dropdownBorder">
        <div className="px-2 py-1 text-[12px]">
          {item.name}
        </div>
      </div>
    );
  }
  return (
    <div className=" p-[3px] border-b-[0.5px] border-settings-dropdownBorder">
      <button
        className={clsx(
          "flex px-2 w-full  items-center   text-[12px] h-[26px] box-border rounded justify-between  leading-[18px]",
          selectIndex === index
            ? "bg-list-hoverBackground"
            : "",
        )}
        onMouseMove={() => {
          setHighlightedIndex(index);
        }}
        onClick={() => {
          handleSelectMenu(item, index);
        }}
      >
        <div
          className={clsx(
            "flex gap-[6px] text-text-common-primary w-full items-center font-medium leading-[19.5px]",
          )}
        >
          <KidIcon
            config={IconArrowLeft}
            size={16}
            color="currentColor"
          >
          </KidIcon>
          {item.name}
          {item.name === "自定义指令" && (
            <TagBeta />
          )}
          {item.name === "知识库" && (
            <span className=" text-[12px] text-disabledForeground">当前仅支持单选</span>
          )}
          {item.name === "规则"
            ? (
                <div
                  tabIndex={-1}
                  onClick={handleAddRuleClick}
                  className=" text-[12px] text-text-common-primary ml-auto flex items-center gap-1"
                >
                  <Icon icon="famicons:add" />
                  添加项目规则
                </div>
              )
            : (
                <div className=" text-[12px] text-text-common-tertiary scale-[.83] ml-auto flex items-center gap-1">
                  点击或
                  <svg
                    width="13"
                    height="12"
                    viewBox="0 0 13 12"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.119 2.5C10.2415 2.50002 10.3597 2.54498 10.4512 2.62636C10.5427 2.70774 10.6012 2.81987 10.6155 2.9415L10.619 3V6.2145C10.619 7.1435 10.022 7.943 9.20703 7.997L9.11903 8H3.82653L4.97253 9.1465C5.06221 9.23648 5.11427 9.35722 5.11815 9.48419C5.12203 9.61117 5.07743 9.73486 4.99341 9.83014C4.90939 9.92542 4.79226 9.98515 4.6658 9.9972C4.53934 10.0092 4.41303 9.97271 4.31253 9.895L4.26553 9.8535L2.26553 7.8535C2.1718 7.75974 2.11914 7.63258 2.11914 7.5C2.11914 7.36742 2.1718 7.24026 2.26553 7.1465L4.26553 5.1465C4.35551 5.05683 4.47625 5.00476 4.60323 5.00089C4.7302 4.99701 4.85389 5.04161 4.94917 5.12562C5.04446 5.20964 5.10419 5.32678 5.11623 5.45324C5.12828 5.5797 5.09174 5.706 5.01403 5.8065L4.97253 5.8535L3.82653 7H9.11903C9.34853 7 9.58703 6.7115 9.61603 6.2985L9.61903 6.2145V3C9.61903 2.86739 9.67171 2.74021 9.76548 2.64645C9.85925 2.55268 9.98643 2.5 10.119 2.5Z"
                      fill="currentColor"
                    />
                  </svg>
                  返回
                </div>
              )}
        </div>
      </button>
    </div>
  );
}
