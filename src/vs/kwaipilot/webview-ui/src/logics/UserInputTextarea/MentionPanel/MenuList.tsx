import React, { ReactNode, useContext, useEffect, useMemo, useRef } from "react";
import { EmptyText } from "./EmptyText";
import {
  MenuItem,
  MenuItem_Codebase,
  MenuItem_File,
  MenuItem_Header,
  MenuItem_Heading,
  MenuItem_Knowledge,
  MenuItem_SlashCommand,
  MenuItem_Web,
} from "./MenuItem";
import { MentionPanelContext, useMentionPanelContext } from "./MentionPanelContext";
import { Popover, Portal } from "@/components/Union/chakra-ui";
import {
  PopoverTrigger,
  PopoverContent,
  Link,
  Spinner,
  Box,
} from "@chakra-ui/react";

import { ExternalLinkIcon } from "@chakra-ui/icons";
import { useScrolling } from "react-use";
import {
  CustomScrollBar,
  useCustomScrollBar,
} from "@/components/CustomScrollbar";
import { Env } from "@/http/env";
import { weblog } from "@/utils/weblogger";
import { MentionTypeaheadOption, TypeaheadMenuOptionType } from "./useOptions";
import { useTypeaheadMenuContext } from "@/components/TextArea/lexical/MentionsV2PluginContext";
import { getRootContainer } from "@/utils/dom";
import { vsCss } from "@/style/vscode";

const PROMPT_ARTIST_PLATFORM_URL = Env.IS_PROD
  ? "https://prompt-artist.corp.kuaishou.com"
  : "https://prompt-artist.staging.kuaishou.com";

export const CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME
  = "custom-prompt-popover-content";

function CustomPromptItem({
  item,
  index,
  isRecent,
}: {
  item: MentionTypeaheadOption<TypeaheadMenuOptionType.customPrompt>;
  index: number;
  isRecent: boolean;
}) {
  const { submittingIndex, selectIndex } = useMentionPanelContext();

  const containerRef = useRef<HTMLElement>(getRootContainer());

  const { container: scrollContainer } = useCustomScrollBar();
  const isScrolling = useScrolling(scrollContainer);

  const menuItem = (
    <MenuItem index={index} data={item} disabled={false} disabledMsg="">
      <span className=" flex-none text-[12px] text-menu-foreground">
        {item.structure.name}
      </span>
      {isRecent && index === selectIndex && (
        <span className=" h-[18px] bg-scrollbarSlider-background rounded ml-[6px]">
          <span className=" text-foreground text-[12px] scale-[0.83] whitespace-nowrap inline-block">
            最近
          </span>
        </span>
      )}
      <span className="pl-4 whitespace-nowrap text-ellipsis overflow-hidden text-disabledForeground text-[12px] ml-auto">
        {item.structure.content}
      </span>
      {submittingIndex === index && <Spinner ml="4px" size="xs" />}
    </MenuItem>
  );

  if (isScrolling) {
    return menuItem;
  }
  return (
    <Popover
      trigger="hover"
      placement="top-end"
      boundary="clippingParents"
      key={item.structure.id}
    >
      <PopoverTrigger>
        <div>{menuItem}</div>
      </PopoverTrigger>
      <Portal containerRef={containerRef}>
        <PopoverContent
          w="270px"
          maxW="70vw"
          maxH={200}
          tabIndex={-1}
          className={CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME}
        >
          <div className=" p-2">
            <div className=" text-text-common-primary text-[13px] whitespace-nowrap leading-[20px] font-medium">
              {item.structure.name}
            </div>
          </div>
          <div className=" px-2 py-1 flex-auto overflow-y-auto">
            <div className=" text-text-common-secondary text-[12px] leading-[18px]">
              {item.structure.content}
            </div>
          </div>
          <div className=" px-2 py-[6px] flex justify-between items-center  border-0 border-t-[1px] border-t-border-horizontal">
            <div className=" text-text-common-secondary text-[12px] leading-[18px]">
              @
              {item.structure.owner_id}
            </div>
            <div className=" text-text-brand-default text-[12px] leading-[18px] ">
              <Link
                isExternal
                href={`${PROMPT_ARTIST_PLATFORM_URL}/playground?id=${item.structure.id}&bizCode=kwaipilot`}
                color="current"
              >
                提示词管理平台
                <ExternalLinkIcon color="current" mx="2px" />
              </Link>
            </div>
          </div>
        </PopoverContent>
      </Portal>
    </Popover>
  );
}

function MenuListCustomPrompt() {
  const { query } = useMentionPanelContext();

  const { options: _options, recentUsedPrompts } = useMentionPanelContext();

  const indexedOptions = useMemo(() => {
    return _options.map((option, index) => ({
      data: option,
      index,
    }));
  }, [_options]);

  const headerItem = useMemo(
    () =>
      indexedOptions.find(
        v => v.data.type === TypeaheadMenuOptionType.header,
      ),
    [indexedOptions],
  );
  const options = useMemo(
    () =>
      indexedOptions.filter(
        (v): v is {
          data: MentionTypeaheadOption<TypeaheadMenuOptionType.customPrompt>;
          index: number;
        } =>
          v.data.type === TypeaheadMenuOptionType.customPrompt,
      ),
    [indexedOptions],
  );

  const recentUsedPromptIdSet = useMemo(
    () => new Set(recentUsedPrompts.map(v => v.id)),
    [recentUsedPrompts],
  );

  const recentUsedOptions = useMemo(() => options.filter(v => recentUsedPromptIdSet.has(v.data.structure.id)), [options, recentUsedPromptIdSet]);
  const restOptions = useMemo(() => options.filter(v => !recentUsedPromptIdSet.has(v.data.structure.id)), [options, recentUsedPromptIdSet]);

  // 添加曝光埋点
  useEffect(() => {
    weblog?.sendImmediately("SHOW", {
      action: "VS_CUSTOM_PROMPT_PANEL",
    });
  }, []);

  const noPropmpts = !options.length;

  const noHitQueryPrompts = query && noPropmpts;

  if (noPropmpts || noHitQueryPrompts) {
    return (
      <div className=" flex flex-col items-center py-[31px]">
        {options.length && noHitQueryPrompts
          ? (
              <div className=" text-foreground text-xs">
                搜索无结果
              </div>
            )
          : null}
        <div className=" text-xs mt-2 text-disabledForeground">
          暂无自定义指令，前往
          <Link
            href={`${PROMPT_ARTIST_PLATFORM_URL}/index`}
            textDecoration="none"
            color={vsCss.textLinkForeground}
            isExternal
          >
            提示词管理平台
          </Link>
          新建
        </div>
      </div>
    );
  }

  return (
    <>
      {headerItem && (
        <MenuItem_Header
          item={
            headerItem.data as MentionTypeaheadOption<TypeaheadMenuOptionType.header>
          }
          index={headerItem.index}
        />
      )}

      <CustomScrollBar suppressScrollX className="max-h-[300px] px-[2px]">
        <Box my="2px">
          {recentUsedOptions.map((item) => {
            return (
              <CustomPromptItem
                key={item.data.structure.id}
                item={item.data}
                index={item.index}
                isRecent={true}
              />
            );
          })}
          {recentUsedOptions.length > 0 && restOptions.length > 0 && (
            <div className="h-[1px] bg-dropdown-background w-full"></div>
          )}
          {restOptions.map((item) => {
            return (
              <CustomPromptItem
                key={item.data.structure.id}
                item={item.data}
                index={item.index}
                isRecent={false}
              />
            );
          })}
        </Box>
      </CustomScrollBar>
    </>
  );
}

const MenuItemMapper: Record<TypeaheadMenuOptionType, (props: {
  data: MentionTypeaheadOption;
  index: number;
}) => ReactNode> = {
  [TypeaheadMenuOptionType.file]: MenuItem_File,
  [TypeaheadMenuOptionType.folder]: MenuItem_File,
  [TypeaheadMenuOptionType.rule]: MenuItem_File,
  [TypeaheadMenuOptionType.web]: MenuItem_Web,
  [TypeaheadMenuOptionType.knowledge]: MenuItem_Knowledge,
  [TypeaheadMenuOptionType.codebase]: MenuItem_Codebase,
  [TypeaheadMenuOptionType.slashCommand]: MenuItem_SlashCommand,
  [TypeaheadMenuOptionType.heading]: function (props) {
    return (
      <MenuItem_Heading
        data={props.data}
        index={props.index}
        disabled={false}
        disabledMsg=""
      />
    );
  },
  [TypeaheadMenuOptionType.header]: () => <div>should not be rendered</div>,
  [TypeaheadMenuOptionType.customPrompt]: () => <div>should not be rendered</div>,
  [TypeaheadMenuOptionType.addRule]: () => <div>should not be rendered</div>,
  [TypeaheadMenuOptionType.none]: () => <div>should not be rendered</div>,
};

function MenuListCommon() {
  const { options: _options } = useMentionPanelContext();

  const indexedOptions = useMemo(() => {
    return _options.map((option, index) => ({
      data: option,
      index,
    }));
  }, [_options]);

  const headerItem = useMemo(
    () =>
      indexedOptions.find(
        v => v.data.type === TypeaheadMenuOptionType.header,
      ),
    [indexedOptions],
  );
  const options = useMemo(
    () =>
      indexedOptions.filter(
        v => v.data.type !== TypeaheadMenuOptionType.header,
      ),
    [indexedOptions],
  );

  return (
    <>
      {headerItem && (
        <MenuItem_Header
          item={
            headerItem.data as MentionTypeaheadOption<TypeaheadMenuOptionType.header>
          }
          index={headerItem.index}
        />
      )}

      <CustomScrollBar suppressScrollX className="max-h-[300px] px-[2px]">
        <Box my="2px">
          {options.map((option) => {
            const MenuItemUI = MenuItemMapper[option.data.type];

            return option.data.type === TypeaheadMenuOptionType.heading && option.data.name === TypeaheadMenuOptionType.customPrompt
              ? (
                  <div key={option.index}>
                    <div className=" mx-auto w-[calc(100%-20px)] bg-settings-dropdownBorder h-[0.5px]"></div>
                    <MenuItem_Heading
                      data={option.data}
                      index={option.index}
                      disabled={false}
                      disabledMsg=""
                    />
                  </div>
                )
              : MenuItemUI
                ? (
                    <MenuItemUI
                      key={option.index}
                      data={option.data}
                      index={option.index}
                    />
                  )
                : (
                    <div key={option.index}>
                      UNKNOWN
                      {option.data.type}
                    </div>
                  );
          })}
        </Box>
      </CustomScrollBar>
      {!options.length && <EmptyText subMenuTitle="空" isSecondary={false} />}
    </>
  );
}

export function MenuList() {
  const context = useContext(MentionPanelContext);
  if (!context) {
    throw new Error("NewPanelContext is not provided");
  }
  const { currentMenu } = useTypeaheadMenuContext();

  return currentMenu === TypeaheadMenuOptionType.customPrompt
    && currentMenu === TypeaheadMenuOptionType.customPrompt
    ? (
        <MenuListCustomPrompt />
      )
    : (
        <MenuListCommon />
      );
}
