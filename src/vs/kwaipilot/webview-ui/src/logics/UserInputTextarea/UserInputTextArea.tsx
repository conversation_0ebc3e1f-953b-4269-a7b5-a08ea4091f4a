import { useCallback, useState, useMemo, useRef, useEffect, ComponentProps, ReactNode } from "react";
import { useBoolean, useColorMode, useMergeRefs } from "@chakra-ui/react";
import clsx from "clsx";

import {
  SharpCommand,
  // RichEditorBoxPanelData,
} from "@shared/types";
import { RichEditor } from "./RichEditor";
import { LexicalEditor, SerializedEditorState, SerializedLexicalNode } from "lexical";
import { Enable, Resizable } from "re-resizable";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { isMentionNode } from "shared/lib/MentionNode";
import { InternalLocalMessage, isHumanMessage } from "shared/lib/agent";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { ContextHeader } from "./ContextHeader";
import { ContextHeaderContext, ContextHeaderState, useContextHeaderState } from "./ContextHeader/ContextHeaderContext";
import { useEvent } from "react-use";
import { isMentionNodeV2, MentionNodeV2Structure, MentionNodeV2Structure_SlashCommand, slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";
import { isCustomVariableNode } from "shared/lib/CustomVariable";
import { useContextInitiation } from "./useContextInitiation";
import { EventBusEvents, useEventBusListener } from "@/utils/eventBus";
import { UserInputTextAreaContext } from "./UserInputTextAreaContext";
import { useToast } from "@chakra-ui/react";

export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}
export interface DocListType {
  id: number;
  name: string;
  urls?: string[];
}

export interface CommandPluginRef {
  close: () => void;
  open: () => void;
  getShown: () => boolean;
}

export interface UserInputTextareaProps extends ComponentProps<"div"> {
  editorClassName?: string;
  wrapperClassName?: string;
  enable?: Enable | false;
  /* 如果是正在编辑的某条消息 */
  localMessage?: InternalLocalMessage;
  role: "bottom" | "conversation";
  editorRef?: React.RefObject<LexicalEditor>;
  doSubmit: (state: {
    contextHeaderState: ContextHeaderState;
    editor: LexicalEditor;
  }) => Promise<{ result: boolean }>;
  doStop: () => void;
  isStreaming: boolean;
  sessionId: string;
  isContextConsumer: boolean;
  applyStatusElement?: ReactNode;
  /**
   * 输入框左下角附加区域
   */
  moreOpt?: ReactNode;
  /**
   * 发送按钮左侧位置
   */
  action?: ReactNode;
  placeholder: JSX.Element;
  uploadFileEnabled?: boolean;
  mode: "chat" | "composer";
  disabled?: boolean;
}

export const UserInputTextarea: React.FC<UserInputTextareaProps> = (props: UserInputTextareaProps) => {
  const {
    editorClassName,
    wrapperClassName,
    localMessage,
    role,
    editorRef: editorRefProp,
    doSubmit: _doSubmit,
    isStreaming,
    sessionId,
    isContextConsumer,
    applyStatusElement,
    moreOpt,
    placeholder,
    action: actionProp,
    uploadFileEnabled,
    mode,
    doStop,
    ...rest
  } = props;
  if (localMessage && !isHumanMessage(localMessage)) {
    throw new Error("data is not a human message");
  }

  const { colorMode: theme } = useColorMode();

  const contextHeaderState = useContextHeaderState({
    initialNodes: localMessage?.contextItems?.map(v => ({
      structure: v,
      followActiveEditor: false,
      isVirtualContext: false,
    })) || [],
    sessionId,
  });

  const initialEditorState = useMemo(() => localMessage?.editorState ? JSON.stringify(localMessage.editorState) : undefined, [localMessage]);

  const optRef = useRef<HTMLDivElement>(null);

  const editorRef = useRef<LexicalEditor>(null);
  const composedEditorRef = useMergeRefs(editorRef, editorRefProp);

  const clearRef = useRef<{
    clear: () => void;
  }>();
  const [richEditorState, setRichEditorState]
    = useState<SerializedEditorState<SerializedLexicalNode>>();
  const changeEditorState = useCallback(
    (state: SerializedEditorState<SerializedLexicalNode>) => {
      setRichEditorState(state);
    },
    [setRichEditorState],
  );

  const {
    resetContext,
    setEditorStateInitializationDone,
  } = useContextInitiation({
    /* 如果有正在编辑的历史消息, 优先使用历史消息 */
    isContextConsumer,
    contextHeaderState,
    disabledCurrentFileBinding: role !== "bottom",
    editor: editorRef,
    sessionId,
  });

  const resetEditorState = useCallback(() => {
    if (role === "bottom") {
      resetContext();
      clearRef.current?.clear();
    }
    else {
      setEditorStateInitializationDone(true);
    }
  }, [resetContext, role, setEditorStateInitializationDone]);

  useEffect(() => {
    resetEditorState();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  const isDark = theme === "dark";

  const [focused, setFocused] = useState(false);

  const onResetCheckpoint: EventBusEvents["composer:onResetCheckpoint"] = useCallback(({ humanMessage }) => {
    const editor = editorRef.current;
    if (!editor) {
      return;
    }
    editor.setEditorState(editor.parseEditorState(humanMessage.editorState));
    if (humanMessage.contextItems.length) {
      contextHeaderState.setNodes(humanMessage.contextItems.map(v => ({
        structure: v,
        followActiveEditor: false,
        isVirtualContext: false,
      })));
    }
  }, [contextHeaderState]);

  useEventBusListener("composer:onResetCheckpoint", onResetCheckpoint);

  const isBlank = useMemo(() => {
    // 一些节点不支持单独出现
    const InvalidSingleMentionNodeType: MentionNodeV2Structure["type"][] = ["web", "knowledge", "codebase"];
    function walkHasText(node: SerializedLexicalNode): boolean {
      if (isCustomVariableNode(node)) {
        return true;
      }
      if (isMentionNode(node)) {
        return true;
      }
      else if (isMentionNodeV2(node) && !InvalidSingleMentionNodeType.includes(node.structure.type)) {
        return true;
      }

      else if ("text" in node) {
        return Boolean(String(node.text).trim());
      }
      if ("children" in node && Array.isArray(node.children)) {
        for (const child of node.children) {
          if (walkHasText(child)) {
            return true;
          }
        }
      }
      return false;
    }
    return richEditorState ? !walkHasText(richEditorState.root) : true;
  }, [richEditorState]);

  const slashCommandInvalid = useMemo<MentionNodeV2Structure_SlashCommand | undefined>(() => {
    function walkInvalid(node: SerializedLexicalNode): MentionNodeV2Structure_SlashCommand | undefined {
      if (isMentionNodeV2(node) && node.structure.type === "slashCommand") {
        const structure = node.structure;
        if (slashCommandSetRequiringContextItem.has(structure.command)) {
          // 检查是否没有contextItem，或者contextItem的uri为空
          if (!structure.contextItem || !structure.contextItem.uri || structure.contextItem.uri.trim() === "") {
            return structure;
          }
        }
      }

      if ("children" in node && Array.isArray(node.children)) {
        for (const child of node.children) {
          const invalidChild = walkInvalid(child);
          if (invalidChild) {
            return invalidChild;
          }
        }
      }
      return undefined;
    }
    return richEditorState ? walkInvalid(richEditorState.root) : undefined;
  }, [richEditorState]);

  const disabled = (!isStreaming && isBlank) || Boolean(slashCommandInvalid);

  const onEscape = useCallback(() => {
    if (localMessage) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [localMessage]);

  const doSubmit = useCallback(async () => {
    if (slashCommandInvalid) {
      kwaiPilotBridgeAPI.showToast({
        level: "info",
        message: `未选择 "/${slashCommandInvalid.label}" 指令指定的文件或代码`,
      });
    }
    if (disabled) {
      return;
    }
    if (isStreaming) {
      if (!isBlank) {
        kwaiPilotBridgeAPI.showToast({
          level: "error",
          message: "正在生成回答，请稍后尝试",
        });
      }
      return;
    }
    if (!editorRef.current) {
      return;
    }
    const res = await _doSubmit({
      contextHeaderState,
      editor: editorRef.current,
    });
    if (res.result) {
    /* 完成回答后清空 https://team.corp.kuaishou.com/task/B2489947 */
      resetEditorState();
    }
  }, [_doSubmit, contextHeaderState, disabled, isBlank, isStreaming, resetEditorState, slashCommandInvalid]);

  const dragRef = useRef<HTMLDivElement>(null);
  const { isDragging } = useDragFile(dragRef);
  const [, { on: onResize }] = useBoolean();

  const resizableRef = useRef<Resizable>(null);
  useEffect(() => {
    if (!props.enable) {
      // 如果关闭 resize 需要把 reziable 重新设置尺寸
      resizableRef.current?.updateSize({ });
    }
  });

  return (
    <ContextHeaderContext.Provider value={{ state: contextHeaderState }}>
      <UserInputTextAreaContext.Provider value={{ role }}>
        <div
          className={clsx(
            "flex relative flex-col",
            wrapperClassName,
          )}
          {...rest}
        >
          <div>
            {applyStatusElement}
            <Resizable
              ref={resizableRef}
              enable={props.enable ?? false}
              maxHeight={340}
              minHeight={116}
              onResize={onResize}
            >
              <div
                className={clsx("bg-input-background border border-dropdown-border hover:border-focusBorder flex flex-col rounded-lg transition-all relative h-full z-10 p-[1px]", focused && "border-focusBorder")}
                style={{
                  maxHeight: "340px",
                  boxShadow: !focused
                    ? ""
                    : isDark
                      ? "box-shadow: 0px 16px 30px 0px rgba(30, 147, 252, 0.08), 0px 8px 21.6px 0px rgba(36, 143, 253, 0.12), 0px 4px 4px 0px rgba(26, 150, 251, 0.08), 0px 2px 2px 0px rgba(24, 151, 251, 0.06)"
                      : "box-shadow: 0px 16px 30px 0px rgba(138, 192, 255, 0.08), 0px 8px 21.6px 0px rgba(138, 192, 255, 0.08), 0px 4px 4px 0px rgba(138, 192, 255, 0.08), 0px 2px 2px 0px rgba(138, 192, 255, 0.06)",
                }}
                ref={dragRef}
              >
                {editorRef.current && <ContextHeader zIndex={1} editor={editorRef.current} mode={mode} />}
                <CustomScrollBar className=" flex-auto">
                  <RichEditor
                    editorRef={composedEditorRef}
                    customOptions={{
                      sharpCommandEnabled: true,
                      uploadFileEnabled: uploadFileEnabled,
                      filterSharpCommandKeyList: [SharpCommand.CODEBASE, SharpCommand.FOLDER],
                    }}
                    initialEditorState={initialEditorState}
                    onSubmit={doSubmit}
                    onStop={doStop}
                    onEscape={onEscape}
                    optRef={optRef}
                    moreOpt={moreOpt}
                    action={actionProp}
                    changeEditorState={changeEditorState}
                    clearRef={clearRef}
                    disabled={disabled}
                    editorClassName={editorClassName}
                    className={clsx(
                      "flex-auto relative ",
                      {
                        "rounded-t-[7.5px] ": true,
                      },
                    )}
                    focused={focused}
                    onFocusedChange={setFocused}
                    loading={isStreaming}
                    placeholder={placeholder}
                    mode={mode}
                  />
                </CustomScrollBar>
                <div ref={optRef} className="rounded-b-[7px]"></div>
              </div>

              {isDragging && <div className="pointer-events-none absolute left-0 right-0 top-0 bottom-0 bg-[hsla(216,_28%,_14%,_0.7)] rounded-lg z-10"></div>}
            </Resizable>
          </div>
        </div>
      </UserInputTextAreaContext.Provider>
    </ContextHeaderContext.Provider>
  );
};

/**
 * TODO: 实现有问题
 * @param ref
 * @returns
 */
function useDragFile(ref: React.RefObject<HTMLElement>) {
  // 拖拽状态
  const [isDragging, setIsDragging] = useState(false);
  // 计数器，用于处理嵌套元素的 dragenter/dragleave
  const [dragCounter, setDragCounter] = useState(0);
  const toast = useToast();

  // 处理拖拽进入
  useEvent("dragenter", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    console.log("drag#dragenter");
    // 增加计数器，首次进入时设置拖拽状态
    setDragCounter(prev => {
      const next = prev + 1;
      if (next === 1) {
        setIsDragging(true);
      }
      return next;
    });
  }, ref.current);

  // 处理拖拽离开
  useEvent("dragleave", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    console.log("drag#dragleave");
    // 减少计数器，完全离开时重置拖拽状态
    setDragCounter(prev => {
      const next = prev - 1;
      if (next === 0) {
        setIsDragging(false);
      }
      return next;
    });
  }, ref.current);

  // 必须阻止 dragover 默认行为，否则 drop 不会触发
  useEvent("dragover", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
  }, ref.current);

  // 处理拖拽结束，清理状态
  useEvent("dragend", (e: Event) => {
    console.log("drag#dragend");
    setIsDragging(false);
    setDragCounter(0);
  }, ref.current);

  // 处理 drop 事件
  useEvent("drop", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(false);
    setDragCounter(0);
    console.log("drag#drop");

    if (!dragEvent.dataTransfer) return;

    // 1. 优先处理 VS Code/IDE 内部自定义格式（用换行分割）
    const internalUriList = dragEvent.dataTransfer.getData('application/vnd.code.uri-list');
    if (internalUriList) {
      // VS Code/IDE 拖拽时，application/vnd.code.uri-list 是以换行分割的 URI 字符串
      const uriList = internalUriList.split('\n').map(s => s.trim()).filter(Boolean);
      processUriList(uriList);
      return;
    }

    // 2. 兼容 ResourceURLs 格式
    const resourcesData = dragEvent.dataTransfer.getData('ResourceURLs');
    if (resourcesData) {
      try {
        const resources = JSON.parse(resourcesData);
        processUriList(resources);
        return;
      } catch (error) {
        console.error('Failed to parse resources:', error);
      }
    }

    // 3. 标准 text/uri-list
    const uriListRaw = dragEvent.dataTransfer.getData("text/uri-list");
    if (uriListRaw) {
      const uriList = uriListRaw.split("\n").map(s => s.trim()).filter(Boolean);
      processUriList(uriList);
      return;
    }

    // 4. 兜底处理原生文件
    if (dragEvent.dataTransfer.files && dragEvent.dataTransfer.files.length > 0) {
      const fileUris = Array.from(dragEvent.dataTransfer.files).map(file => {
        // 注意：这里仅能获取文件名，无法获取完整路径
        return `file://${file.name}`;
      });
      processUriList(fileUris);
      return;
    }

    // 5. 没有可用数据，提示用户
    toast({
      title: "仅支持拖拽文件",
      description: "请从文件资源管理器拖拽文件到输入框。",
      status: "warning",
      duration: 2000,
      isClosable: true,
    });
  }, ref.current);

  // 统一处理 URI 列表
  const processUriList = (uriList: string[]) => {
    if (!uriList || uriList.length === 0) {
      toast({
        title: "未检测到文件",
        description: "请拖拽有效的文件。",
        status: "warning",
        duration: 2000,
        isClosable: true,
      });
      return;
    }

    let hasFile = false;
    uriList.forEach(uri => {
      if (uri.startsWith("file://")) {
        hasFile = true;
        kwaiPilotBridgeAPI.extensionComposer.$addFileToContext(uri);
      }
    });

    if (!hasFile) {
      toast({
        title: "仅支持文件类型",
        description: "暂不支持拖拽文件夹或其他类型。",
        status: "warning",
        duration: 2000,
        isClosable: true,
      });
    }
  };

  return {
    isDragging,
  };
}
