import {
  InitialConfigType,
  InitialEditorStateType,
  LexicalComposer,
} from "@lexical/react/LexicalComposer";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { EditorRefPlugin } from "@lexical/react/LexicalEditorRefPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { useControllableState, useMergeRefs } from "@chakra-ui/react";
import { FocusPlugin } from "@/components/TextArea/lexical/FocusPlugin";
import {
  MutableRefObject,
  RefCallback,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { DisableRichEditorMenu, SharpCommand } from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { RichEditorDisabledReason } from "@shared/constant";
import { MentionNode } from "@/components/TextArea/lexical/CommandNode";
import { DeletePlugin } from "@/components/TextArea/lexical/DeletePlugin";
import { EmptyNode } from "@/components/TextArea/lexical/EmptyNode";
import { SaveStatePlugin } from "@/components/TextArea/lexical/SaveStatePlugin";
import {
  COMMAND_PRIORITY_LOW,
  KEY_ESCAPE_COMMAND,
  LexicalEditor,
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";

import { ClearPlugin } from "@/components/TextArea/lexical/ClearPlugin";
import { InsertLineBreakPlugin } from "@/components/TextArea/lexical/InsertLineBreakPlugin";
import { InsertCommandSign } from "@/components/TextArea/lexical/InsertCommandSign";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { EnterSendPlugin } from "@/components/TextArea/lexical/EnterSendPlugin";
import { CommandPluginRef } from "@/components/TextArea";
import { OptPlugin } from "@/components/TextArea/lexical/OptPlugin";
import { DialogSetting } from "@/store/record";
import eventBus from "@/utils/eventBus";
import repoChatService, { WorkspaceState } from "@/services/repo-chat";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { RichEditorContext } from "@/components/TextArea/hooks/useRichEditorContext";
import { twMerge } from "tailwind-merge";
import { CustomVariableNode } from "@/components/TextArea/lexical/CustomVariableNode";
import { CustomVariableAutoUpdatePlugin } from "@/components/TextArea/lexical/CustomVariableAutoUpdatePlugin";
import { MentionNodeV2 } from "@/components/TextArea/lexical/MentionNodeV2/MentionNodeV2";
import { withProviders } from "@udecode/cn";
import { MentionsV2Plugin } from "@/components/TextArea/lexical/MentionsV2Plugin";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import { SlashV2Plugin } from "@/components/TextArea/lexical/SlashV2Plugin";
import { SlashV2AutoUpdatePlugin } from "@/components/TextArea/lexical/SlashV2AutoUpdatePlugin";

interface IProps {
  editorRef?: RefObject<LexicalEditor> | RefCallback<LexicalEditor>;
  optRef?: RefObject<HTMLDivElement>;
  moreOpt?: React.ReactNode;
  action?: React.ReactNode;
  clearRef?: MutableRefObject<{ clear: () => void } | undefined>;
  insertSignRef?: MutableRefObject<
    { insertSign: (sign: string) => void } | undefined
  >;
  onSubmit: () => unknown;
  onStop?: () => unknown;
  onEscape?: () => unknown;
  changeEditorState: (
    state: SerializedEditorState<SerializedLexicalNode>
  ) => void;
  commandShown?: {
    sharpShown: boolean;
    slashShown: boolean;
  };
  onCommandShownChange?: (state: {
    sharpShown: boolean;
    slashShown: boolean;
  }) => void;
  setting?: DialogSetting;
  disabled: boolean;
  className?: string;
  editorClassName?: string;
  customOptions?: {
    sharpCommandEnabled?: boolean;
    uploadFileEnabled?: boolean;
    /** 过滤掉知识命令中某些menu的key */
    filterSharpCommandKeyList?: SharpCommand[];
  };
  focused?: boolean;
  onFocusedChange?: (focused: boolean) => void;
  initialEditorState?: InitialEditorStateType;
  editable?: boolean;
  namespace?: string;
  loading?: boolean;
  placeholder: JSX.Element;
  mode: "chat" | "composer";
}

export const RichEditor = withProviders()((props: IProps) => {
  const {
    editorRef: editorRefProp,
    customOptions,
    onCommandShownChange,
    commandShown: commandShownProp,
    changeEditorState,
    clearRef,
    onSubmit,
    onStop,
    insertSignRef: insertSignRefProp,
    optRef,
    moreOpt,
    action,
    setting,
    disabled,
    initialEditorState: initialEditorStateProp,
    className,
    editorClassName,
    editable = true,
    namespace = "RichEditor",
    loading = false,
    onEscape: onEscapeProp,
    placeholder,
    mode,
  } = props;

  const insertSignRefInner = useRef<{ insertSign: (sign: string) => void }>();

  const insertSignRef = useMergeRefs(insertSignRefProp, insertSignRefInner);

  const sharpPluginRef = useRef<CommandPluginRef | undefined>();

  const richEditorPanelMenuStore = useRichEditPanelMenuStore();
  const setDisabledMenu = useRichEditPanelMenuStore(
    state => state.setDisabledMenu,
  );
  const editorRef = useRef<LexicalEditor | null>(null);
  const composedEditorRef = useMergeRefs(editorRefProp, editorRef);

  const [focused, setFocused] = useControllableState({
    value: props.focused,
    onChange: props.onFocusedChange,
    defaultValue: false,
  });

  const initialEditorState = useMemo<InitialEditorStateType | undefined>(() => {
    if (typeof initialEditorStateProp === "string") {
      const serializedEditorState = JSON.parse(initialEditorStateProp) as SerializedEditorState<SerializedLexicalNode>;
      // 检查是否为空 防止抛错
      if (serializedEditorState.root && serializedEditorState.root.children?.length === 0) {
        return null;
      }
    }
    return initialEditorStateProp;
  }, [initialEditorStateProp]);

  const initialConfig = useMemo<InitialConfigType>(() => ({
    namespace: namespace,
    nodes: [MentionNode, EmptyNode, CustomVariableNode, MentionNodeV2],
    onError: (error: any) => {
      throw error;
    },
    editorState: initialEditorState,
    editable,
  }), [editable, initialEditorState, namespace]);

  const setWorkspaceTree = useCallback(
    (files: string[], dirs: string[]) => {
      richEditorPanelMenuStore.setCodeSearchWorkspaceDir(dirs);
      richEditorPanelMenuStore.setCodeSearchWorkspaceFiles(files);
    },
    [richEditorPanelMenuStore],
  );

  const fetchWorkspaceTree = useCallback(async () => {
    const files = await repoChatService.getWorkspaceFileList({
      excludeDirList: ["node_modules", ".kwaipilot/rules"],
    });
    const dir = await repoChatService.getWorkspaceDirList();
    setWorkspaceTree(files, dir);
  }, [setWorkspaceTree]);

  const fetchCurrentFilAndCodeBaseRepo = useCallback(async () => {
    const { filePath, repoPath }
      = await repoChatService.getCurrentFilePathAndRepoPath();
    richEditorPanelMenuStore.updateCodeBasePath(repoPath || "");
    richEditorPanelMenuStore.updateCurrentFilePath(filePath || "");
    richEditorPanelMenuStore.setDisabledMenu({
      [SharpCommand.CURRENT_FILE]: {
        status: !filePath,
        msg: !filePath ? RichEditorDisabledReason.unopenedFile : "",
      },
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    kwaiPilotBridgeAPI.onActiveTextChange((data) => {
      const {
        document: { relativePath },
      } = data;
      // 更新当前文件
      richEditorPanelMenuStore.updateCurrentFilePath(relativePath || "");
      richEditorPanelMenuStore.setDisabledMenu({
        [SharpCommand.CURRENT_FILE]: {
          status: !relativePath,
          msg: !relativePath ? RichEditorDisabledReason.unopenedFile : "",
        },
      });
      if (relativePath) {
        repoChatService.currentFilePath = relativePath;
        repoChatService.setSelectFileQueue(
          WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY,
          [relativePath],
        );
        repoChatService.setSelectFileQueue(
          WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY,
          [repoChatService.handleFilePathToDir(relativePath)],
        );
      }
    });
  }, [richEditorPanelMenuStore]);

  useEffect(() => {
    fetchWorkspaceTree();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    fetchCurrentFilAndCodeBaseRepo();
  }, [fetchCurrentFilAndCodeBaseRepo]);

  useEffect(() => {
    const disableMenuListener = (data: DisableRichEditorMenu) => {
      richEditorPanelMenuStore.setDisabledMenu(data);
    };

    eventBus.on("pushRichEditorPanelDisableMenu", disableMenuListener);

    return () => {
      eventBus.off("pushRichEditorPanelDisableMenu", disableMenuListener);
    };
  }, [richEditorPanelMenuStore]);

  useEffect(() => {
    kwaiPilotBridgeAPI.getSystemInfo().then((data) => {
      if (data.ide !== "kwaipilot-xcode") {
        setDisabledMenu({
          [SharpCommand.FILE]: {
            status: false,
            msg: "",
          },
        });
      }
    });
  }, [setDisabledMenu]);

  const [commandShown, setCommandShown] = useControllableState({
    value: commandShownProp,
    onChange: onCommandShownChange,
    defaultValue: {
      sharpShown: false,
      slashShown: false,
    },
  });

  const [mentionShown, setMentionShown] = useState(false);
  const [slashV2Shown, setSlashV2Shown] = useState(false);

  useEffect(() => {
    return editorRef.current?.registerCommand(
      KEY_ESCAPE_COMMAND,
      () => {
        onEscapeProp?.();
        return false;
      },
      COMMAND_PRIORITY_LOW,
    );
  }, [onEscapeProp]);

  const [, isKwaiPilotIDE] = useIdeEnv();

  const handleKeyDownInIde = useCallback((event: React.KeyboardEvent) => {
    // 在 ide 内渲染时，ctrl+a, ctrl+z, ctrl+shift+z等快捷键会被文本编辑器处理，默认阻止事件冒泡
    const isMacOS = navigator.platform.toUpperCase().indexOf("MAC") >= 0;
    const modifier = isMacOS ? event.metaKey : event.ctrlKey;

    if (modifier) {
      const key = event.key.toLowerCase();
      // 对于command+a (全选), command+z (撤销), command+shift+z (重做)等，
      if (key === "a" || key === "z") {
        event.stopPropagation();
      }
    }
  }, []);

  return (
    <div className={className}>
      <RichEditorContext.Provider
        value={{
          focused,
          setFocused,
          commandShown,
          setCommandShown,
          mentionShown,
          setMentionShown,
          slashV2Shown,
          setSlashV2Shown,
        }}
      >
        <LexicalComposer initialConfig={initialConfig}>
          <RichTextPlugin
            contentEditable={(
              <ContentEditable
                className={twMerge(
                  "w-full text-[13px] leading-[20px] pl-3 pr-2 pt-2 z-10 cursor-text relative focus-visible:outline-none text-[var(--custom-text-common)]",
                  editorClassName,
                )}
                style={{
                  outline: "none",
                  fontFamily: "PingFang SC, PingFang SC-Regular",
                }}
                onKeyDown={isKwaiPilotIDE ? handleKeyDownInIde : undefined}
              >
              </ContentEditable>
            )}
            placeholder={placeholder}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <EditorRefPlugin editorRef={composedEditorRef} />
          <HistoryPlugin />
          <AutoFocusPlugin />
          <FocusPlugin changeFocused={setFocused} />
          {mode === "chat" && (
            <>
              <SlashV2Plugin mode={mode} />
              <SlashV2AutoUpdatePlugin />
            </>
          )}
          {(customOptions?.sharpCommandEnabled ?? true) && <MentionsV2Plugin mode={mode} />}
          <EnterSendPlugin submit={onSubmit} />
          <DeletePlugin />
          <SaveStatePlugin changeEditorState={changeEditorState} />
          <ClearPlugin ref={clearRef} />
          <InsertLineBreakPlugin />
          <InsertCommandSign focused={focused} ref={insertSignRef} />
          {optRef && (
            <OptPlugin
              loading={loading}
              optRef={optRef}
              moreOpt={moreOpt}
              action={action}
              setting={setting}
              onSubmit={onSubmit}
              onStop={onStop}
              disabled={disabled}
              sharpPluginRef={sharpPluginRef}
              focused={focused}
              uploadFileEnabled={customOptions?.uploadFileEnabled}
            >
            </OptPlugin>
          )}
          <CustomVariableAutoUpdatePlugin />
        </LexicalComposer>
      </RichEditorContext.Provider>
    </div>
  );
});
