/*
测试 command + L 将当前行添加到输入框
*/

import "./commandL.spec.before.js";
import { createRef, forwardRef, useEffect, useImperativeHandle, useRef } from "react";
import { ContextHeader } from "./ContextHeader";
import { LexicalEditor, createEditor } from "lexical";
import { describe, expect, it, Mock, vi } from "vitest";
import { useComposerMessageConsumer } from "@/store/composerMessageConsumer";
import { render, waitFor } from "@testing-library/react";
import { ContextHeaderContext, ContextHeaderState, useContextHeaderState } from "./ContextHeader/ContextHeaderContext.js";
import { useContextInitiation } from "./useContextInitiation.js";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes.js";
import { IdeEnvProvider } from "@/providers/IdeEnvProvider";

// Mock the bridge observable API
vi.mock("@/bridge/useBridgeObservableAPI", () => ({
  useBridgeObservableAPI: vi.fn(),
}));

// Mock the entire bridge module
vi.mock("@/bridge", () => ({
  kwaiPilotBridgeAPI: {
    observableAPI: {
      visibility: vi.fn(),
      currentFileAndSelection: vi.fn(),
      currentTheme: vi.fn(),
      isDeveloperMode: vi.fn(),
      latestCopiedContent: vi.fn(),
      indexState: vi.fn(),
      mcpServers: vi.fn(),
      customPanelPage: vi.fn(),
      rulesList: vi.fn(),
      userInfo: vi.fn(),
    },
    addMessageListener: vi.fn(),
    getOpenTabFiles: vi.fn().mockReturnValue(Promise.resolve({ list: [] })),
    getActiveEditor: vi.fn().mockReturnValue(Promise.resolve({
      document: {
        relativePath: "test.tsx",
        fileName: "test.tsx",
        languageId: "typescript",
      },
    })),
    printLogger: vi.fn(),
    getWorkspaceUri: vi.fn().mockReturnValue({ result: "" }),
    getState: vi.fn().mockReturnValue({}),
    updateState: vi.fn(),
    executeCmd: vi.fn(),
    fs: {
      readFile: vi.fn(),
    },
    getFileStatus: vi.fn(),
    getSystemInfo: () => Promise.resolve({}),

    get logger() {
      return {
        onReportUserAction: () => {
        },
      };
    },
  },
}));

const mockUseBridgeObservableAPI = useBridgeObservableAPI as Mock<typeof useBridgeObservableAPI>;

interface TestWrapperRef {
  getState: () => ContextHeaderState;
}

interface TestWrapperProps {
  editor: LexicalEditor;
}

const _TestWrapper = forwardRef<TestWrapperRef, TestWrapperProps>(function TestWrapper({ editor }, ref) {
  const contextHeaderState = useContextHeaderState({
    initialNodes: [],
    sessionId: "",
  });

  const {
    resetContext,
  } = useContextInitiation({
    isContextConsumer: true,
    contextHeaderState,
    disabledCurrentFileBinding: false,
    editor: useRef(editor),
    sessionId: "",
  });
  useEffect(() => {
    resetContext();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useImperativeHandle(ref, () => ({
    getState: () => contextHeaderState,
  }));
  return (

    <IdeEnvProvider value={{ isKwaiPilotIDE: false }}>

      <ContextHeaderContext.Provider value={{ state: contextHeaderState }}>
        <ContextHeader mode="composer" zIndex={1} editor={editor} />
      </ContextHeaderContext.Provider>
    </IdeEnvProvider>
  );
});

const TestWrapper = forwardRef<TestWrapperRef, TestWrapperProps>(function TestWrapper(props, ref) {
  return (
    <_TestWrapper {...props} ref={ref} />
  );
});

describe("Command L functionality", () => {
  it("当前在其他页面时, command L 应该触发进入当前页面", async () => {
    // 没法模拟 router 可以模拟 webviewBridge
    const expectedFile: MentionNodeV2Structure = {
      type: "file",
      uri: "file:///path/to/test.tsx",
      relativePath: "path/to/test.tsx",
    };
    useComposerMessageConsumer.getState().produceComposerContext(expectedFile);

    const mockEditor = createEditor({});
    mockUseBridgeObservableAPI.mockImplementation((api) => {
      if (api === "currentFileAndSelection") {
        return {
          uri: "file:///test/workspace/test.tsx",
          relativePath: "test.tsx",
          languageId: "typescript",
          selection: null,
        };
      }
      return null as unknown as any;
    });

    const testWrapper = createRef<TestWrapperRef>();
    render(<TestWrapper ref={testWrapper} editor={mockEditor} />);

    waitFor(() => {
      const state = testWrapper.current?.getState();
      expect(state?.nodes).toHaveLength(2);
      expect(state?.nodes[1].structure).toEqual(expectedFile);
    });
  });
});
