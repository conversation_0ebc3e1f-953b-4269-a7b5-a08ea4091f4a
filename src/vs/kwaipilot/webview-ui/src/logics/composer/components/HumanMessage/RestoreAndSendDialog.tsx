import { Box, CloseButton, Flex, useOutsideClick } from "@chakra-ui/react";
import { createContext, useCallback, useContext, useMemo, useRef } from "react";
import { DialogButton } from "../DialogButton";

import KidIcon from "@/components/Union/kid";

import IconWarn from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_warn_surface";
import { useDisclosureWithReason, UseDisclosureWithReasonReturn } from "./useDisclosureWithReason";

export type RestoreAndSendDialogResult = "cancel" | "restoreAndSend" | "keepAndSend";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface ContextValue extends UseDisclosureWithReasonReturn<RestoreAndSendDialogResult> {
}

const _RestoreAndSendDialogContext = createContext<ContextValue | null>(null);

export function useRestoreAndSendDialog(): {
  invoke(): Promise<RestoreAndSendDialogResult>;
  isOpen: boolean;
} {
  const context = useContext(_RestoreAndSendDialogContext);
  if (!context) {
    throw new Error("useRestoreAndSendDialog must be used within a RestoreAndSendDialogContext");
  }
  const invoke = useCallback(() => {
    return context.onOpen().settled;
  }, [context]);
  return {
    invoke,
    isOpen: context.isOpen,
  };
}

export function RestoreAndSendDialogContext({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isOpen, onOpen, onClose } = useDisclosureWithReason<RestoreAndSendDialogResult>();
  const provided = useMemo<ContextValue>(() => {
    return {
      isOpen,
      onOpen,
      onClose,
    };
  }, [isOpen, onOpen, onClose]);
  return (
    <_RestoreAndSendDialogContext.Provider value={provided}>
      {children}
    </_RestoreAndSendDialogContext.Provider>
  );
}

export function RestoreAndSendDialog() {
  const context = useContext(_RestoreAndSendDialogContext);
  if (!context) {
    throw new Error("RestoreAndSendDialog must be used within a RestoreAndSendDialogContext");
  }
  const { isOpen, onClose } = context;
  const onRestoreAndSend = useCallback(() => {
    onClose("restoreAndSend");
  }, [onClose]);
  const onKeepAndSend = useCallback(() => {
    onClose("keepAndSend");
  }, [onClose]);
  const onCancel = useCallback(() => {
    onClose("cancel");
  }, [onClose]);

  const ref = useRef<HTMLDivElement>(null);
  useOutsideClick({
    ref,
    handler: () => {
      onClose("cancel");
    },
  });
  return isOpen && (
    <Box ref={ref} mt={2} className=" border-border-common border border-solid border-editorWidget-border bg-statusBarItem-remoteHoverBackground rounded-lg relative">
      <Flex alignItems="center" gap={1} py={3} px={4}>
        <KidIcon config={IconWarn} color="#FFBB26" size={16} />
        <span className=" text-[13px] leading-[18px] font-medium">确认发送？</span>
      </Flex>
      <Box className="  text-descriptionForeground text-[13px] text-foreground leading-[20px] pl-9 pr-4 pb-3">
        所有代码变更都将被回退到该对话发生之前，且后续的对话记录将会被清除，是否继续？
      </Box>
      <Flex justify="right" gap={2} px={4} pb={3}>
        <DialogButton onClick={onKeepAndSend}>
          仅修改对话
        </DialogButton>
        <DialogButton isPrimary onClick={onRestoreAndSend}>
          继续且回退代码
        </DialogButton>
      </Flex>
      <CloseButton size="sm" className=" absolute top-2 right-2" onClick={onCancel} />
    </Box>
  );
}
