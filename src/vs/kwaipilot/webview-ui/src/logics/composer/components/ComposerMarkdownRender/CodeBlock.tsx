import React, { useCallback } from "react";
import Code from "../CodeBlockBase";
import { Icon } from "@/components/Union/t-iconify";
import { getFileExtension, getIcon } from "@/utils/fileIcon";
import { SingleIcon } from "@/components/SingleIcon";
import CopyIcon from "@/assets/copy.svg?react";

interface IProps {
  content: string;
  language: string;
  handleCopy: (text: string) => void;
}

export const CodeBlock: React.FC<IProps> = (props) => {
  const { content: c, language, handleCopy } = props;
  const iconType = getIcon("index" + getFileExtension(language), false);
  const onCopy = useCallback(() => {
    handleCopy(c);
  }, [c, handleCopy]);

  return (
    <div className="border-commandCenter-inactiveBorder border border-solid rounded-[8px]">
      <div className="px-3 justify-between flex items-center h-[32px] sticky top-[-13px] z-10 bg-editor-background">
        {/* Using a nested div for an opaque background layer */}
        <div className="bg-statusBarItem-remoteHoverBackground w-full h-full absolute top-0 left-0 opacity-90"></div>
        <div className="flex gap-1 items-center relative z-20">
          <Icon icon={iconType} className="size-[14px]"></Icon>
          <span className="text-[13px] leading-[18px] text-[var(--custom-text-common)]">{language}</span>
        </div>
        <div className="relative z-20">
          <SingleIcon
            title="复制"
            onClick={onCopy}
            className="text-icon-foreground"
          >
            <CopyIcon className="size-[14px]" />
          </SingleIcon>
        </div>
      </div>
      <Code source={c} language={language} clearPreTopBorder={true}></Code>
    </div>
  );
};
