import { kwaiPilotBridgeAPI } from "@/bridge";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { Popover } from "@/components/Union/chakra-ui";
import { Box, BoxProps, Flex, PopoverContent, PopoverTrigger } from "@chakra-ui/react";
import clsx from "clsx";
import { SVGProps, useCallback, useEffect, useState } from "react";
import IconExternal from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_skip";
import KidIcon from "@/components/Union/kid";

import AutoTooltip from "@/components/AutoTooltip";
import { McpServer } from "shared/lib/mcp/types";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";

const getMcpStatusInfo = (status: McpServer["status"]): { color: string; text: string } => {
  const statusMap: Record<McpServer["status"], { color: string; text: string }> = {
    connected: { color: "#73C991ff", text: "可使用" },
    connecting: { color: "#CCA700ff", text: "准备中" },
    disconnected: { color: "#C74E39ff", text: "不可使用" },
  };
  return statusMap[status];
};

function IconSetting({ ...props }: SVGProps<SVGSVGElement>) {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M6.99952 4.15633C6.43708 4.15633 5.88727 4.32312 5.41962 4.63559C4.95197 4.94807 4.58748 5.3922 4.37224 5.91183C4.157 6.43145 4.10069 7.00324 4.21041 7.55487C4.32014 8.1065 4.59098 8.61321 4.98869 9.01092C5.38639 9.40862 5.8931 9.67947 6.44473 9.78919C6.99637 9.89892 7.56815 9.8426 8.08778 9.62737C8.60741 9.41213 9.05154 9.04764 9.36401 8.57999C9.67649 8.11233 9.84327 7.56252 9.84327 7.00008C9.8424 6.24614 9.54252 5.52333 9.0094 4.99021C8.47628 4.45709 7.75346 4.1572 6.99952 4.15633ZM6.99952 8.53133C6.69667 8.53133 6.40062 8.44153 6.14881 8.27327C5.89699 8.10502 5.70073 7.86587 5.58483 7.58607C5.46894 7.30627 5.43861 6.99838 5.49769 6.70135C5.55678 6.40432 5.70262 6.13148 5.91676 5.91733C6.13091 5.70318 6.40376 5.55734 6.70079 5.49826C6.99782 5.43917 7.30571 5.4695 7.58551 5.58539C7.86531 5.70129 8.10445 5.89755 8.27271 6.14937C8.44097 6.40118 8.53077 6.69723 8.53077 7.00008C8.53077 7.4062 8.36944 7.79568 8.08228 8.08284C7.79511 8.37001 7.40563 8.53133 6.99952 8.53133ZM12.0308 7.04329V6.95688L12.7964 5.9993C12.8607 5.91907 12.9052 5.82486 12.9264 5.72426C12.9475 5.62367 12.9448 5.5195 12.9183 5.42016C12.7802 4.89905 12.5737 4.3985 12.3042 3.93157C12.2523 3.84253 12.1801 3.76696 12.0936 3.711C12.007 3.65504 11.9085 3.62025 11.806 3.60946L10.5876 3.47274L10.5269 3.41204L10.3901 2.19305C10.3793 2.09064 10.3444 1.99223 10.2885 1.90578C10.2325 1.81933 10.157 1.74726 10.068 1.6954C9.60106 1.42507 9.10033 1.21782 8.5789 1.07907C8.47947 1.053 8.37531 1.05066 8.27481 1.07222C8.17431 1.09378 8.08029 1.13865 8.0003 1.20321L7.04272 1.96883H6.95632L5.99874 1.20321C5.91851 1.13894 5.82429 1.09442 5.7237 1.07324C5.6231 1.05206 5.51894 1.0548 5.4196 1.08126C4.89835 1.22028 4.3978 1.4277 3.93101 1.69813C3.84231 1.74978 3.76697 1.8215 3.71102 1.90755C3.65508 1.9936 3.6201 2.09158 3.6089 2.1936L3.47218 3.41204L3.41148 3.47274L2.19249 3.60946C2.09008 3.62034 1.99167 3.65516 1.90522 3.71112C1.81877 3.76708 1.74669 3.8426 1.69483 3.93157C1.42527 4.39865 1.21876 4.89939 1.08069 5.42071C1.05433 5.51997 1.05163 5.62403 1.07281 5.72452C1.09399 5.82501 1.13846 5.91913 1.20265 5.9993L1.96827 6.95688V7.04329L1.20265 8.00086C1.13838 8.0811 1.09386 8.17531 1.07268 8.27591C1.05149 8.3765 1.05424 8.48067 1.08069 8.58001C1.21989 9.10128 1.4275 9.60183 1.69812 10.0686C1.74971 10.1572 1.82135 10.2325 1.9073 10.2885C1.99325 10.3444 2.09111 10.3794 2.19304 10.3907L3.41148 10.5263L3.47218 10.587L3.6089 11.8071C3.61977 11.9095 3.6546 12.0079 3.71056 12.0944C3.76652 12.1808 3.84203 12.2529 3.93101 12.3048C4.39798 12.5751 4.89872 12.7823 5.42015 12.9211C5.51957 12.9472 5.62373 12.9495 5.72423 12.9279C5.82473 12.9064 5.91876 12.8615 5.99874 12.797L6.95632 12.0313H7.04272L8.0003 12.797C8.08054 12.8612 8.17475 12.9057 8.27534 12.9269C8.37594 12.9481 8.4801 12.9454 8.57944 12.9189C9.10056 12.7808 9.6011 12.5742 10.068 12.3048C10.1569 12.2533 10.2325 12.1816 10.2886 12.0955C10.3448 12.0095 10.3799 11.9114 10.3912 11.8093L10.5269 10.5909L10.5876 10.5302L11.8066 10.3907C11.9087 10.3794 12.0067 10.3442 12.0928 10.2881C12.1788 10.232 12.2505 10.1564 12.302 10.0675C12.5723 9.60053 12.7796 9.09979 12.9183 8.57837C12.9445 8.47927 12.9471 8.37542 12.926 8.27513C12.9048 8.17484 12.8604 8.08091 12.7964 8.00086L12.0308 7.04329ZM10.7117 6.77586C10.7205 6.92522 10.7205 7.07495 10.7117 7.2243C10.7019 7.38666 10.7528 7.54686 10.8544 7.67383L11.5566 8.55157C11.4903 8.74777 11.4112 8.93941 11.3198 9.12524L10.2026 9.24938C10.0408 9.26788 9.8917 9.34584 9.78421 9.46813C9.68486 9.58006 9.57895 9.68597 9.46702 9.78532C9.34473 9.89281 9.26677 10.0419 9.24827 10.2037L9.12468 11.3204C8.93887 11.4122 8.74724 11.4916 8.55101 11.5583L7.67327 10.8556C7.55679 10.7626 7.41214 10.7121 7.26312 10.7123C7.24999 10.7123 7.23687 10.7123 7.22374 10.7123C7.07439 10.721 6.92465 10.721 6.7753 10.7123C6.61306 10.7027 6.45298 10.7533 6.32577 10.8545L5.44804 11.5572C5.25183 11.4909 5.0602 11.4118 4.87437 11.3204L4.75023 10.2031C4.73173 10.0414 4.65376 9.89226 4.53148 9.78477C4.41955 9.68543 4.31363 9.57951 4.21429 9.46758C4.1068 9.34529 3.95769 9.26733 3.79593 9.24883L2.67921 9.12524C2.58743 8.93944 2.50796 8.7478 2.44132 8.55157L3.14351 7.67383C3.24515 7.54686 3.29601 7.38666 3.28624 7.2243C3.27749 7.07495 3.27749 6.92522 3.28624 6.77586C3.29601 6.61351 3.24515 6.45331 3.14351 6.32633L2.44241 5.4486C2.5087 5.2524 2.5878 5.06076 2.67921 4.87493L3.79648 4.75079C3.95824 4.73229 4.10735 4.65433 4.21483 4.53204C4.31418 4.42011 4.4201 4.31419 4.53202 4.21485C4.65431 4.10736 4.73228 3.95825 4.75077 3.79649L4.87437 2.67977C5.06017 2.58799 5.25181 2.50852 5.44804 2.44188L6.32577 3.14461C6.45298 3.24578 6.61306 3.29641 6.7753 3.2868C6.92465 3.27805 7.07439 3.27805 7.22374 3.2868C7.38602 3.29673 7.54622 3.24606 7.67327 3.14461L8.55101 2.44188C8.74724 2.50852 8.93887 2.58799 9.12468 2.67977L9.24882 3.79704C9.26731 3.9588 9.34528 4.10791 9.46757 4.2154C9.57949 4.31474 9.68541 4.42066 9.78476 4.53258C9.89225 4.65487 10.0414 4.73284 10.2031 4.75133L11.3198 4.87493C11.4116 5.06073 11.4911 5.25237 11.5577 5.4486L10.8555 6.32633C10.7535 6.45316 10.7022 6.61337 10.7117 6.77586Z" fill="currentColor" />
    </svg>
  );
}

function IconPendingDot({ className, ...props }: BoxProps) {
  return (
    <Box className={clsx("w-1 h-1 rounded-full bg-text-common-secondary", className)} {...props}></Box>
  );
}

export function McpStatusBar() {
  const [mcpServers, setMcpServers] = useState<{
    mcpServers: McpServer[];
    error: string;
  }>({
    mcpServers: [],
    error: "",
  });

  const availableServers = mcpServers.mcpServers.filter(server => server.disabled !== true);

  const updateImmediately = useCallback(() => {
    kwaiPilotBridgeAPI.extensionMCP.$getAllMcpServers().then((res) => {
      if (res.status !== "ok") {
        setMcpServers({
          mcpServers: [],
          error: res.message || "",
        });
      }
      else {
        setMcpServers({
          mcpServers: res.data?.mcpServers || [],
          error: "",
        });
      }
    });
  }, []);

  useEffect(() => {
    kwaiPilotBridgeAPI.observableAPI.mcpServers().subscribe((res) => {
      if (res.isError) {
        setMcpServers({
          error: res.message,
          mcpServers: [],
        });
      }
      else {
        setMcpServers({
          mcpServers: res.mcpServers || [],
          error: "",
        });
      }
    });
  }, []);

  useEffect(() => {
    updateImmediately();
  }, [updateImmediately]);

  return (
    <Popover
      placement="top-start"
      onOpen={() => {
        updateImmediately();
      }}
    >
      <PopoverTrigger>
        <Box
          onClick={() => {
            const param: ReportOpt<"mcp_action"> = {
              key: "mcp_action",
              type: "mcp_chat_click",
            };
            reportUserAction(param);
          }}
          as="button"
          className=" cursor-pointer leading-[18px] font-medium inline-block text-foreground text-[13px] px-[6px] py-[3px] rounded hover:bg-toolbar-hoverBackground"
        >
          MCP
        </Box>
      </PopoverTrigger>
      <PopoverContent w="240px" p="2px" className="!bg-dropdown-background">
        <CustomScrollBar className=" max-h-[200px] w-full">

          {
            availableServers.length
              ? availableServers.map((mcpServer) => {
                  return (
                    <Flex key={mcpServer.name} className="h-[26px] w-full rounded px-2 gap-1 mt-[2px] hover:bg-list-hoverBackground" align="center">
                      <div className=" w-0  flex-auto">
                        <AutoTooltip className=" select-none block  text-[12px] text-text-common-secondary" label={mcpServer.name}>
                          {mcpServer.name}
                        </AutoTooltip>
                      </div>
                      <Box className=" float-none flex ml-auto items-center gap-1" color={getMcpStatusInfo(mcpServer.status).color}>
                        <IconPendingDot bg={getMcpStatusInfo(mcpServer.status).color}></IconPendingDot>
                        <Box className=" text-[12px] select-none" color={getMcpStatusInfo(mcpServer.status).color}>
                          {getMcpStatusInfo(mcpServer.status).text}
                        </Box>
                      </Box>
                    </Flex>
                  );
                })
              : mcpServers.error
                ? (
                    <Flex className="h-[160px] w-full text-[12px] justify-center flex-col text-text-common-secondary " align="center">
                      <div>MCP 配置文件异常</div>
                      <div>
                        <button
                          onClick={() => {
                            kwaiPilotBridgeAPI.extensionSettings.$openSettings("mcp");
                          }}
                          className=" text-text-brand-default hover:text-textLink-activeForeground"
                        >
                          查看
                        </button>
                      </div>
                    </Flex>
                  )
                : (
                    <Flex className="h-[160px] w-full text-[12px] justify-center flex-col text-text-common-secondary " align="center">
                      <div>暂无 MCP Servers</div>
                      <div>
                        <button
                          onClick={() => {
                            kwaiPilotBridgeAPI.extensionSettings.$openSettings("mcp");
                          }}
                          className=" text-text-brand-default hover:text-textLink-activeForeground"
                        >
                          添加
                        </button>
                      </div>
                    </Flex>
                  )
          }
        </CustomScrollBar>
        <Flex className=" border-t border-t-border-settings-dropdownBorder border-solid px-2 py-1" align="center" justify="space-between">
          <div
            className=" cursor-pointer flex items-center gap-1 hover:text-textLink-activeForeground"
            onClick={() => {
              kwaiPilotBridgeAPI.extensionSettings.$openSettings("mcp");
            }}
          >
            <IconSetting className=" text-inherit"></IconSetting>
            <span className=" text-[12px]">
              MCP 管理
            </span>
          </div>
          <a href="https://wanqing.corp.kuaishou.com/api/v1/mcp-server-manager/mcp-kwaipilot/common/plaza-redirect" target="_blank" className=" text-[12px] text-text-common-secondary flex items-center gap-1 hover:text-textLink-activeForeground">
            <span>
              MCP 市场
            </span>
            <KidIcon config={IconExternal} size={12} color="inherit"></KidIcon>
          </a>
        </Flex>
      </PopoverContent>
    </Popover>
  );
}
