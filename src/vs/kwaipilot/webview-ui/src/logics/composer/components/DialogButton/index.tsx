import { Box, HTMLChakraProps } from "@chakra-ui/react";
import clsx from "clsx";
import { forwardRef } from "react";

export interface DialogButtonProps extends HTMLChakraProps<"button"> {
  isPrimary?: boolean;
}

export const DialogButton = forwardRef<HTMLButtonElement, DialogButtonProps>(({ isPrimary = false, children, className, ...props }, ref) => {
  return (
    <Box
      as="button"
      {...props}
      ref={ref}
      className={clsx(
        isPrimary
          ? ` bg-button-background hover:bg-button-hoverBackground text-button-foreground`
          : "  bg-button-secondaryBackground hover:bg-button-secondaryHoverBackground  text-button-secondaryForeground",
        "text-[12px] leading-[18px] font-medium px-[6px] py-[2px] rounded-[4px]",
        className,
      )}
    >
      {children}
    </Box>
  );
},
);
