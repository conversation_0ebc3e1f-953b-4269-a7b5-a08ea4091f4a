import { RichEditor } from "@/logics/UserInputTextarea/RichEditor";
import { LexicalEditor } from "lexical";
import { useCallback, useMemo, useRef } from "react";
import { InternalLocalMessage, isHumanMessage } from "shared/lib/agent";
import { useComposerState } from "../../context/ComposerStateContext";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ContextHeaderReadonly } from "@/logics/UserInputTextarea/ContextHeader";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { Tooltip } from "@/components/Union/chakra-ui";
import { useToast, Box, ButtonProps } from "@chakra-ui/react";

import clsx from "clsx";
import { vsCss } from "@/style/vscode";
import { cn } from "@udecode/cn";
import { useDesignToken } from "@/hooks/useDesignToken";
import { useAsync } from "react-use";
import {
  isCheckpointCreatedMessage,
  isTerminalMessage,
  isToolEditFileMessage,
} from "shared/lib/agent/isToolMessage";
import eventBus from "@/utils/eventBus";
import {
  useRestoreConfirmDialog,
} from "./RestoreConfirmDialog";
import {
  useRestoreAndSendDialog,
} from "./RestoreAndSendDialog";
import {
  useSendConfirmDialog,
} from "./SendConfirmDialog";
import { VsCodeToast } from "@/components/ui/VsCodeToast";
import { getCurrentEnvIsInIDE } from "@/utils/ide";

function IconRestore() {
  return (
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.56491 7.8751C1.56593 8.86107 1.95805 9.80636 2.65523 10.5035C3.35241 11.2007 4.2977 11.5928 5.28366 11.5939H10.0962C10.2702 11.5939 10.4371 11.5247 10.5602 11.4016C10.6833 11.2786 10.7524 11.1117 10.7524 10.9376C10.7524 10.7636 10.6833 10.5966 10.5602 10.4736C10.4371 10.3505 10.2702 10.2814 10.0962 10.2814H5.28366C4.64549 10.2814 4.03345 10.0278 3.58219 9.57658C3.13093 9.12532 2.87741 8.51328 2.87741 7.8751C2.87741 7.23693 3.13093 6.62489 3.58219 6.17363C4.03345 5.72237 4.64549 5.46885 5.28366 5.46885H11.1352L9.63023 6.97331C9.50694 7.09659 9.43768 7.2638 9.43768 7.43815C9.43768 7.6125 9.50694 7.77971 9.63023 7.90299C9.75351 8.02628 9.92072 8.09554 10.0951 8.09554C10.2694 8.09554 10.4366 8.02628 10.5599 7.90299L13.1849 5.27799C13.2461 5.21703 13.2946 5.14458 13.3278 5.06481C13.3609 4.98504 13.3779 4.89952 13.3779 4.81315C13.3779 4.72678 13.3609 4.64126 13.3278 4.56149C13.2946 4.48172 13.2461 4.40927 13.1849 4.34831L10.5599 1.72331C10.4366 1.60002 10.2694 1.53076 10.0951 1.53076C9.92072 1.53076 9.75351 1.60002 9.63023 1.72331C9.50694 1.84659 9.43768 2.0138 9.43768 2.18815C9.43768 2.3625 9.50694 2.52971 9.63023 2.65299L11.1352 4.15635H5.28366C4.29774 4.15751 3.35254 4.54968 2.65539 5.24683C1.95824 5.94398 1.56607 6.88918 1.56491 7.8751Z"
        fill="currentColor"
      />
    </svg>
  );
}

function RestoreButton({ children, className, noCheckpoint, ...rest }: ButtonProps & { noCheckpoint: boolean }) {
  const { tokens } = useDesignToken();
  const multiWorkspaceLabel = "在 VSCode 工作区模式下暂不支持对话回退功能";
  const noCheckpointLabel = "本轮对话不支持回退";
  const { value: workspaceFileUri } = useAsync(() => kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile(), []);
  const isVscodeWorkspace = useMemo(() => {
    return Boolean(workspaceFileUri);
  }, [workspaceFileUri]);
  return (
    <Tooltip label={isVscodeWorkspace ? multiWorkspaceLabel : noCheckpoint ? noCheckpointLabel : "回退到本轮对话发起前"} placement="top-start">
      <Box
        as="button"
        display="inline-flex"
        rounded="4px"
        fontSize={12}
        alignItems="center"
        height="auto"
        fontWeight="normal"
        gap={1}
        lineHeight="18px"
        padding="4px"
        color={tokens.colorTextCommonSecondary}
        className={cn(
          " border-solid border border-radio-inactiveBorder text-icon-foreground",
          className,
        )}
        backdropFilter="blur(2px)"
        disabled={isVscodeWorkspace || noCheckpoint}
        _hover={isVscodeWorkspace || noCheckpoint
          ? {
              cursor: "not-allowed",
            }
          : {
              backgroundColor: vsCss.listHoverBackground,
            }}
        bg={vsCss.statusBarItemHoverBackground}
        {...rest}
      >
        {children || (
          <IconRestore />
        )}
      </Box>
    </Tooltip>
  );
}
export function HumanMessageReadonly({ data }: { data: InternalLocalMessage }) {
  if (!isHumanMessage(data)) {
    throw new Error("data is not a human message");
  }
  const { sessionId, isCurrentWorkspaceSession, isStreaming, localMessages, currentMessageTs } = useComposerState();
  const { editorState } = data;
  const editorRef = useRef<LexicalEditor>(null);
  const initialEditorState = useMemo(() => editorState ? JSON.stringify(editorState) : undefined, [editorState]);

  const {
    invoke: invokeRestoreConfirmDialog,
    isOpen: isRestoreConfirmDialogOpen,
  } = useRestoreConfirmDialog();
  const { isOpen: isRestoreAndSendDialogOpen } = useRestoreAndSendDialog();
  const { isOpen: isSendConfirmDialogOpen } = useSendConfirmDialog();

  const toast = useToast({
    render: VsCodeToast,
  });

  const copySessionId = useCallback(() => {
    kwaiPilotBridgeAPI.copyToClipboard(sessionId);
    toast({
      title: "复制成功",
      description: `sessionId已复制到剪贴板 ${sessionId}`,
    });
  }, [sessionId, toast]);

  const copyChatId = useCallback(() => {
    kwaiPilotBridgeAPI.copyToClipboard(data.chatId || "");
    toast({
      title: "复制成功",
      description: `chatId已复制到剪贴板 ${data.chatId}`,
    });
  }, [data.chatId, toast]);

  const transformToEditable = useCallback(async (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isCurrentWorkspaceSession) {
      return;
    }
    if (isStreaming) {
      return;
    }
    // 防止 conversation onClick setEditingMessageTs(undefined) 触发 https://team.corp.kuaishou.com/task/B2489555
    e.stopPropagation();
    await kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(data.ts);
  }, [data.ts, isCurrentWorkspaceSession, isStreaming]);

  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  // 添加对话回退相关逻辑
  const humanMessageIndex = useMemo(
    () => localMessages.findIndex(v => v.ts === data.ts),
    [localMessages, data.ts],
  );

  const relatedCheckpointMessage = useMemo(() => {
    if (humanMessageIndex === -1) {
      return null;
    }
    const prediction = localMessages[humanMessageIndex + 1];
    if (prediction && isCheckpointCreatedMessage(prediction)) {
      return prediction;
    }
    return null;
  }, [localMessages, humanMessageIndex]);

  const showCheckpoint = useMemo(() => {
    return localMessages
      .slice(humanMessageIndex + 1)
      .some(v => isToolEditFileMessage(v) || isTerminalMessage(v));
  }, [localMessages, humanMessageIndex]);

  const haveCheckpoint = Boolean(relatedCheckpointMessage);

  const onConfirmRestore = useCallback(async () => {
    if (!relatedCheckpointMessage?.lastCheckpointHash) {
      kwaiPilotBridgeAPI.showToast({
        message: "没有找到相关的检查点",
        level: "error",
      });
      return;
    }
    const humanMessage = localMessages[humanMessageIndex];
    if (!humanMessage || !isHumanMessage(humanMessage)) {
      kwaiPilotBridgeAPI.showToast({
        message: "没有找到相关的消息",
        level: "error",
      });
      return;
    }

    if(getCurrentEnvIsInIDE()) {
      kwaiPilotBridgeAPI.editor.clearAllDiffState();
    }

    await kwaiPilotBridgeAPI.extensionComposer.$restoreCheckpoint({
      humanMessageTs: humanMessage.ts,
      restoreCommitHash: relatedCheckpointMessage.lastCheckpointHash,
      updateStateImmediately: true,
    });
    eventBus.emit("composer:onResetCheckpoint", {
      humanMessage,
    });
  }, [
    relatedCheckpointMessage?.lastCheckpointHash,
    localMessages,
    humanMessageIndex,
  ]);

  const onRestoreButtonClick = useCallback(() => {
    invokeRestoreConfirmDialog().then((reason) => {
      if (reason === "restore") {
        onConfirmRestore();
      }
    });
  }, [invokeRestoreConfirmDialog, onConfirmRestore]);

  return (
    <>
      <div className="flex gap-2 ml-[24px] justify-end items-end">
        {isCurrentWorkspaceSession && showCheckpoint && !isStreaming && (
          <>
            {!isRestoreConfirmDialogOpen
            && !isRestoreAndSendDialogOpen
            && !isSendConfirmDialogOpen
            && (currentMessageTs && currentMessageTs === relatedCheckpointMessage?.ts
              ? (
                  <></>
            // {/* <RedoButton onClick={onRedoButtonClick} mt={2} /> */}
                )
              : (
                  <Box position="relative">
                    <RestoreButton
                      onClick={onRestoreButtonClick}
                      noCheckpoint={!haveCheckpoint}
                      zIndex={10}
                    />
                  </Box>
                ))}
          </>
        )}
        <div
          className={clsx(
            "bg-list-inactiveSelectionBackground rounded-l-[12px] rounded-tr-[2px] rounded-br-[12px] p-[12px] max-w-full")}
          onClick={transformToEditable}
        >
          {data.contextItems?.length ? <ContextHeaderReadonly pb={3} persistentNodes={data.contextItems || []} onClick={e => /* 防止transformToEditable */e.stopPropagation()} /> : null}
          <RichEditor
            mode="composer"
            editable={false}
            editorRef={editorRef}
            onSubmit={async () => { }}
            changeEditorState={() => { }}
            disabled={false}
            editorClassName=" w-fit p-0"
            initialEditorState={initialEditorState}
            customOptions={{
              sharpCommandEnabled: false,
            }}
            placeholder={(
              <div className="absolute text-[13px] leading-[19.5px] text-tab-inactiveForeground top-2 left-3">
                有问题尽管问我，# 引用知识
              </div>
            )}
          />
        </div>
      </div>

      {isDeveloperMode && (
        <div>
          sessionId:
          <span onClick={copySessionId}>
            {data.sessionId}
          </span>
          <br />
          chatId:
          <span onClick={copyChatId}>
            {data.chatId}
          </span>
        </div>
      )}
    </>
  );
};
