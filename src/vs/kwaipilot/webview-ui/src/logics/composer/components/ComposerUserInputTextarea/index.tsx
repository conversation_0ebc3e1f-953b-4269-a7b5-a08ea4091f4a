import { UserInputTextareaProps, UserInputTextarea } from "@/logics/UserInputTextarea/UserInputTextArea";

export const ComposerUserInputTextarea: React.FC<Omit<UserInputTextareaProps, "placeholder" | "mode">> = (props) => {
  return (
    <UserInputTextarea
      placeholder={(
        <div className="absolute text-[13px] leading-[19.5px] text-tab-inactiveForeground top-2 left-3">
          有问题尽管问我，# 引用知识
        </div>
      )}
      mode="composer"
      {...props}
    >
    </UserInputTextarea>
  );
};
