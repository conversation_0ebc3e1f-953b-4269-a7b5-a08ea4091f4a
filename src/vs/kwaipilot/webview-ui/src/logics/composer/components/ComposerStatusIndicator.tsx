import { Flex, Spinner } from "@chakra-ui/react";
import { useComposerState } from "../context/ComposerStateContext";

/**
 * 当前可能需要给用户透出的状态，例如 正在等待接口返回，需要给用户提示一下 优化交互
 */
export function ComposerStatusIndicator() {
  const { localMessages, currentTaskInterrupted } = useComposerState();
  const latestMessage = localMessages.at(-1);
  if (!latestMessage) {
    return null;
  }
  if (currentTaskInterrupted) {
    return null;
  }
  const isCurrentToolExecuting = latestMessage.type === "say" && latestMessage.say === "tool";
  if (isCurrentToolExecuting) {
    return null;
  }
  if (latestMessage.type === "say" && latestMessage.say === "api_req_started") {
    return (
      <Flex align="center" gap={2.5}>
        <Spinner size="xs"></Spinner>
        思考中
      </Flex>
    );
  }
  if (latestMessage.type === "ask" && latestMessage.ask === "command") {
    // 等待执行命令
    return null;
  }
  // if (latestMessage.type === 'ask')
  return null;
}
