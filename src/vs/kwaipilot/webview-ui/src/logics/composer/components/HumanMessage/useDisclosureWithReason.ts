import { useDisclosure } from "@chakra-ui/react";
import { useCallback, useState } from "react";

export type InnerReason = "cancel";

export interface UseDisclosureWithReasonReturn<R> {
  isOpen: boolean;
  onOpen: () => {
    settled: Promise<R | InnerReason>;
  };
  onClose: (reason: R | InnerReason) => void;
}

export function useDisclosureWithReason<R>(): UseDisclosureWithReasonReturn<R> {
  const { isOpen, onOpen: _onOpen, onClose: _onClose } = useDisclosure();
  const [handler, setHandler] = useState<{ resolve: (result: R | InnerReason) => void; reject: (reason: unknown) => void } | null>(null);
  const onOpen = useCallback<UseDisclosureWithReasonReturn<R>["onOpen"]>(() => {
    _onOpen();
    if (handler) {
      // clear previous callback
      handler.resolve("cancel");
    }
    return {
      settled: new Promise<R | InnerReason>((resolve, reject) => {
        setHandler({ resolve, reject });
      }),
    };
  }, [_onOpen, handler]);
  const onClose = useCallback<UseDisclosureWithReasonReturn<R>["onClose"]>((reason) => {
    _onClose();
    handler?.resolve(reason);
    setHandler(null);
  }, [_onClose, handler]);

  return {
    isOpen,
    onOpen,
    onClose,
  };
}
