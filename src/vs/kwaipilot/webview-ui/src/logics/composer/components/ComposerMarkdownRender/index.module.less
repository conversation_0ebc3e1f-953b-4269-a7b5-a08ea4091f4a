.kwaipilot-markdown {
  font-family: var(--vscode-font-family);

  display: flex;
  flex-direction: column;
  /* 添加文本渲染优化 */
  text-rendering: optimizeLegibility;
  /* 添加字距调整 */
  /* 添加自动调整CJK文字与西文之间的间距 */
  text-spacing: ideograph-alpha ideograph-numeric;
  text-spacing-trim: ideograph-alpha ideograph-numeric;

  .katex-error,
  :global(.katex-error),
  [class].katex-error,
  [class*="katex-error"] {
    color: var(--colors-text-main) !important;
  }

  ul,
  ol {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 2em;
  }
  
  ol ol,
  ul ol {
    list-style-type: lower-roman;
  }

  ul ul ol,
  ul ol ol,
  ol ul ol,
  ol ol ol {
    list-style-type: lower-alpha;
  }
  li {
    line-height: 20px;
    padding-top: 3px;
    padding-bottom: 3px;
  }

  h1 {
    line-height: 27px;
    padding-top: 9px;
    padding-bottom: 7px;
    font-weight: 600;
    font-size: 18px;
    /* 防止标题孤字 */
    hanging-punctuation: first last;
  }
  h2 {
    line-height: 24px;
    padding-top: 8px;
    padding-bottom: 6px;
    font-weight: 600;
    font-size: 16px;
    /* 防止标题孤字 */
    hanging-punctuation: first last;
  }

  h3 {
    line-height: 21px;
    padding-top: 7px;
    padding-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
  }

  h4 {
    line-height: 20px;
    padding-top: 6px;
    padding-bottom: 5px;
    font-size: 13px;
    font-weight: 600;
  }
  h5 {
    line-height: 20px;
    padding-top: 6px;
    padding-bottom: 5px;
    font-weight: 600;
    font-size: 13px;
  }

  p {
    line-height: 20px;
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: 13px;
    font-weight: 400;
    /* 添加段落首行缩进 */
    text-indent: 0;
    /* 添加段落断字规则 */
    word-break: normal;
    overflow-wrap: break-word;
    /* 避免标点符号出现孤行 */
    orphans: 2;
    widows: 2;
    /* 避免标点符号出现在行首 */
    hanging-punctuation: allow-end last;
  }
  table {
    border-spacing: 0;
    border-collapse: separate;
    display: table;
    width: 100%;
    table-layout: fixed;
    overflow: auto;
    font-variant: tabular-nums;
    border-radius: 8px; /* 添加8px的圆角 */
    overflow: hidden;
    margin-top: 8px;
    margin-bottom: 8px;
    border: 1px solid var(--vscode-dropdown-border); /* 添加外层边框确保圆角处有边框 */
    /* 添加表格内容对齐 */
    font-feature-settings: "tnum";
    /* 改善表格可读性 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  td,
  th {
    padding: 0;
  }

  table th {
    font-weight: 600;
  }
  table th,
  table td {
    padding: 6px 13px;
    border-right: 1px solid var(--vscode-dropdown-border); /* 修改表格边框颜色为更柔和的VSCode变量 */
    border-bottom: 1px solid var(--vscode-dropdown-border); /* 修改表格边框颜色为更柔和的VSCode变量 */
  }
  /* 去掉最右侧单元格的右边框 */
  table th:last-child,
  table td:last-child {
    border-right: none;
  }
  
  /* 去掉最后一行单元格的底部边框 */
  table tr:last-child td {
    border-bottom: none;
  }

  table td > :last-child {
    margin-bottom: 0;
  }

  table tr {
    border-top: none;
  }

  /* 为表头行设置特殊背景色 */
  table thead tr {
    background-color: var(--vscode-statusBarItem-remoteHoverBackground);
  }
  /* 为表头单元格添加底部边框 */
  table thead th {
    border-bottom: 1px solid var(--vscode-dropdown-border);
  }
  /* 设置数据行的背景色 */
  table tbody tr:nth-child(odd) {
    background-color: var(--vscode-sideBar-background);
  }

  table tbody tr:nth-child(even) {
    background-color: var(--vscode-statusBarItem-remoteHoverBackground);
  }
  table img {
    background-color: transparent;
  }

  /* 添加对代码块的样式 */
  pre {
    margin-top: 8px;
    margin-bottom: 8px;
    /* 添加代码块圆角和优化 */
    border-radius: 5px;
    font-feature-settings: "calt" 0;
  }

a {
  background-color: transparent;
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
  /* 优化链接过渡效果 */
  transition: color 0.2s ease;
}
 a:focus,
 [role=button]:focus,
 input[type=radio]:focus,
 input[type=checkbox]:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: -2px;
  box-shadow: none;
}

 a:focus:not(:focus-visible),
 [role=button]:focus:not(:focus-visible),
 input[type=radio]:focus:not(:focus-visible),
 input[type=checkbox]:focus:not(:focus-visible) {
  outline: solid 1px transparent;
}

 a:focus-visible,
 [role=button]:focus-visible,
 input[type=radio]:focus-visible,
 input[type=checkbox]:focus-visible {
  outline: 2px solid var(--focus-outlineColor);
  outline-offset: -2px;
  box-shadow: none;
}

 a:not([class]):focus,
 a:not([class]):focus-visible,
 input[type=radio]:focus,
 input[type=radio]:focus-visible,
 input[type=checkbox]:focus,
 input[type=checkbox]:focus-visible {
  outline-offset: 0;
}
a:hover {
  text-decoration: underline;
  /* 鼠标悬停时稍微加深颜色 */
  filter: brightness(85%);
}
a:not([href]) {
  color: inherit;
  text-decoration: none;
}

/* 优化引用块样式 */
blockquote {
  padding: 0 1em;
  border-left: 0.25em solid var(--vscode-radio-inactiveBorder);
  background-color: transparent;
}
/* 添加图片优化 */
img {
  max-width: 200px;
  height: auto;
  border-radius: 8px;
  display: block;
  margin: 8px 0;
  position: relative;
}

/* 图片加载失败时的替代样式 - 使用:global确保在CSS模块中也能正常工作 */
:global(.error-image),
img.error-image,
img[class*="error-image"] {
  visibility: hidden !important;
  min-height: 80px !important;
  min-width: 80px !important;
  position: relative !important;
  display: inline-block !important;
  margin: 8px 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  
  &::after {
    content: "" !important;
    visibility: visible !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 80px !important;
    height: 80px !important;
    background-color: var(--vscode-editor-inactiveSelectionBackground) !important;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJjdXJyZW50Q29sb3IiIGQ9Ik0yMSA1djExcTAgLjUtLjMxMi43NVQyMCAxN3QtLjY4Ny0uMjYydC0uMzEzLS43NjNWNUg4cS0uNSAwLS43NS0uMzEyVDcgNHQuMjUtLjY4N1Q4IDNoMTFxLjgyNSAwIDEuNDEzLjU4OFQyMSA1TTUgMjFxLS44MjUgMC0xLjQxMi0uNTg3VDMgMTlWNS44bC0uOS0uOXEtLjI3NS0uMjc1LS4yNzUtLjd0LjI3NS0uN3QuNy0uMjc1dC43LjI3NWwxNyAxN3EuMjc1LjI3NS4yNzUuN3QtLjI3NS43dC0uNy4yNzV0LS43LS4yNzVsLS45LS45em05LjE3NS00SDdxLS4zIDAtLjQ1LS4yNzV0LjA1LS41MjVsMi0yLjY3NXEuMTUtLjIuNC0uMnQuNC4yTDExLjI1IDE2bC44MjUtMS4xTDUgNy44MjVWMTloMTEuMTc1ek0xMC42IDEzLjQiLz48L3N2Zz4=") !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: 36px !important;
    border: 1px solid var(--vscode-editorWidget-border) !important;
    z-index: 100 !important;
    border-radius: 4px !important;
  }
}

/* 添加分割线样式 */
hr {
  margin-top: 8px;
  margin-bottom: 8px;
  border: 0;
  height: 1px;
  background-color: var(--vscode-radio-inactiveBorder);
}

/* 添加行内元素间距优化 */
strong, em, a, code, kbd {
  margin: 0 0.1em;
}
/* 中文与英文、数字间增加空白 */
:lang(zh) {
  text-spacing: ideograph-alpha ideograph-numeric;
}
}
