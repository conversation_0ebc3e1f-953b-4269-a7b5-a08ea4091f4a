.kwaipilot-inline-code{
    --vscode-symbolIcon-arrayForeground: #cccccc;
    --vscode-symbolIcon-booleanForeground: #cccccc;
    --vscode-symbolIcon-classForeground: #ee9d28;
    --vscode-symbolIcon-colorForeground: #cccccc;
    --vscode-symbolIcon-constantForeground: #cccccc;
    --vscode-symbolIcon-constructorForeground: #b180d7;
    --vscode-symbolIcon-enumeratorForeground: #ee9d28;
    --vscode-symbolIcon-enumeratorMemberForeground: #75beff;
    --vscode-symbolIcon-eventForeground: #ee9d28;
    --vscode-symbolIcon-fieldForeground: #75beff;
    --vscode-symbolIcon-fileForeground: #cccccc;
    --vscode-symbolIcon-folderForeground: #cccccc;
    --vscode-symbolIcon-functionForeground: #b180d7;
    --vscode-symbolIcon-interfaceForeground: #75beff;
    --vscode-symbolIcon-keyForeground: #cccccc;
    --vscode-symbolIcon-keywordForeground: #cccccc;
    --vscode-symbolIcon-methodForeground: #b180d7;
    --vscode-symbolIcon-moduleForeground: #cccccc;
    --vscode-symbolIcon-namespaceForeground: #cccccc;
    --vscode-symbolIcon-nullForeground: #cccccc;
    --vscode-symbolIcon-numberForeground: #cccccc;
    --vscode-symbolIcon-objectForeground: #cccccc;
    --vscode-symbolIcon-operatorForeground: #cccccc;
    --vscode-symbolIcon-packageForeground: #cccccc;
    --vscode-symbolIcon-propertyForeground: #cccccc;
    --vscode-symbolIcon-referenceForeground: #cccccc;
    --vscode-symbolIcon-snippetForeground: #cccccc;
    --vscode-symbolIcon-stringForeground: #cccccc;
    --vscode-symbolIcon-structForeground: #cccccc;
    --vscode-symbolIcon-textForeground: #cccccc;
    --vscode-symbolIcon-typeParameterForeground: #cccccc;
    --vscode-symbolIcon-unitForeground: #cccccc;
    --vscode-symbolIcon-variableForeground: #75beff;
}