import { InternalLocalMessage } from "shared/lib/agent";
import { useComposerState } from "../../context/ComposerStateContext";
import { HumanMessageReadonly } from "./HumanMessageReadonly";
import { Box, BoxProps } from "@chakra-ui/react";
import { useRef, useEffect, useCallback } from "react";

import { withProviders } from "@udecode/cn";
import {
  RestoreAndSendDialog,
  RestoreAndSendDialogContext,
} from "./RestoreAndSendDialog";
import {
  RestoreConfirmDialog,
  RestoreConfirmDialogContext,
} from "./RestoreConfirmDialog";
import {
  SendConfirmDialog,
  SendConfirmDialogContext,
} from "./SendConfirmDialog";
import { useSubmit } from "../../useSubmit";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { McpStatusBar } from "../Mcp/McpStatusBar";
import { ModelSelector } from "../ModelSelector";
import { ComposerUserInputTextarea } from "../ComposerUserInputTextarea";
import { kwaiPilotBridgeAPI } from "@/bridge";

export const HumanMessage = withProviders(
  RestoreConfirmDialogContext,
  RestoreAndSendDialogContext,
  SendConfirmDialogContext,
)(function HumanMessage({
  data,
  ...rest
}: { data: InternalLocalMessage } & BoxProps) {
  const {
    editingMessageTs,
    isStreaming,
    sessionId,
  } = useComposerState();

  const isEditing = editingMessageTs === data.ts;
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isEditing) {
      setTimeout(() => {
        const container = containerRef.current;
        if (container) {
          (container as any).scrollIntoViewIfNeeded();
        }
      }, 0);
    }
  }, [isEditing]);

  const { doSubmit } = useSubmit({
    role: "conversation",
  });

  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  const moreOpt = (
    <>
      <McpStatusBar />
      {isDeveloperMode ? <ModelSelector /> : null}
    </>
  );

  const stopCurrentTask = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "stop",
      params: {
        sessionId,
      },
    });
  }, [sessionId]);

  return (
    <>
      <Box {...rest} role="group" ref={containerRef}>
        {isEditing
          ? (
              <ComposerUserInputTextarea
                localMessage={data}
                role="conversation"
                doSubmit={doSubmit}
                isStreaming={isStreaming}
                sessionId={sessionId}
                isContextConsumer={/* 历史消息, 只要在编辑态,就是 contextConsumer */true}
                moreOpt={moreOpt}
                doStop={stopCurrentTask}
              />
            )
          : (
              <HumanMessageReadonly data={data} />
            )}
      </Box>

      <RestoreConfirmDialog />
      <RestoreAndSendDialog />
      <SendConfirmDialog />
    </>
  );
});
