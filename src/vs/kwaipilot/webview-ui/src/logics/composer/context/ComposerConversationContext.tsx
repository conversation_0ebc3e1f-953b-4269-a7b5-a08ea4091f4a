import { throwNeverError, warnNeverType } from "@/utils/throwUnknownError";
import { createContext, useContext, useMemo } from "react";
import { InternalLocalMessage } from "shared/lib/agent";
import { useComposerState } from "./ComposerStateContext";

export interface ComposerConversationContextValue {
  /**
   * 这一轮对话是否结束了（不管是否异常，只要模型不再继续生成，就认为这一轮对话结束了）
   */
  isFinished: boolean;
  taskRows: InternalLocalMessage[];
  humanMessage: InternalLocalMessage;
}

export const ComposerConversationContext = createContext<ComposerConversationContextValue | null>(null);

export function ComposerConversationContextProvider({ children, taskRows, isLastConversation, humanMessage }: {
  children: React.ReactNode;
  taskRows: InternalLocalMessage[];
  humanMessage: InternalLocalMessage;
  /**
   * 是否是最后一轮对话
   */
  isLastConversation: boolean;
}) {
  const { currentTaskInterrupted } = useComposerState();
  const isFinished = useMemo(() => {
    const lastMessage = taskRows.at(-1);
    if (!lastMessage) {
      return false;
    }
    if (currentTaskInterrupted && isLastConversation) {
      // 异常推出，当做结束
      return true;
    }
    if (lastMessage.partial) {
      return false;
    }
    if (lastMessage.type === "say") {
      const say = lastMessage.say;
      if (!say) {
        return false;
      }
      switch (say) {
        case "completion_result":
          return true;
        case "task":
        case "tool":
        case "user_feedback":
        case "api_req_started":
        case "api_req_retried":
        case "api_req_finished":
        case "text":
        case "error":
        case "checkpoint_created":
        case "command":
        case "command_output":
        case "use_mcp_tool_result":
        case "edit_file_result":
        case "api_req_failed":
        case "tool_error":
          return false;
        default:
          warnNeverType(say);
          return false;
      }
    }
    else if (lastMessage.type === "ask") {
      const ask = lastMessage.ask;
      if (!ask) {
        return false;
      }
      switch (ask) {
        case "followup":
        case "command":
        case "tool":
        case "mistake_limit_reached":
        case "use_mcp_tool":
          return true;
        case "api_req_failed":
        case "command_output":
          return false;
        default:
          warnNeverType(ask);
          return false;
      }
    }
    else {
      throwNeverError(lastMessage.type);
    }
  }, [currentTaskInterrupted, isLastConversation, taskRows]);

  return (
    <ComposerConversationContext.Provider value={{ isFinished, taskRows, humanMessage }}>
      {children}
    </ComposerConversationContext.Provider>
  );
}

export function useComposerConversationContext() {
  const context = useContext(ComposerConversationContext);
  if (!context) {
    throw new Error("useComposerConversationContext must be used within a ComposerConversationContextProvider");
  }
  return context;
}
