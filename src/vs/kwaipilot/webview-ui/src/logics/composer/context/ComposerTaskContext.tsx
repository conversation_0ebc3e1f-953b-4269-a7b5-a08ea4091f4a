import { createContext, useContext, useMemo } from "react";
import { FileStateUIType, InternalLocalMessage, InternalLocalMessage_Tool_EditFile, WorkingSetEffect } from "shared/lib/agent";
import { useComposerState } from "./ComposerStateContext";
import { isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { useComposerConversationContext } from "./ComposerConversationContext";

export interface ComposerTaskContextValue {
  message: InternalLocalMessage;
  /**
   * 当前消息是否是最后一条消息(整个 session 中)
   */
  isLast: boolean;
  /**
   * tool 正在执行
   */
  isToolExecuting: boolean;
  workingSetEffect: WorkingSetEffect | null;
  isToolError: boolean;
  // message.workingSetEffect.status 存储的是可持久化的状态，这里会整合中间态
  workingSetFileUIState: FileStateUIType | null;
}

export const ComposerTaskContext = createContext<ComposerTaskContextValue | null>(null);

export interface ComposerTaskContextProviderProps {
  message: InternalLocalMessage;
  isLast: boolean;
  children: React.ReactNode;
}

export function ComposerTaskContextProvider({ message, isLast, children }: ComposerTaskContextProviderProps) {
  const { taskRows } = useComposerConversationContext();

  const { currentTaskInterrupted, indeterminatedWorkingSetEffects } = useComposerState();
  const isTool = useMemo(() => {
    return message.type === "say" && message.say === "tool";
  }, [message.say, message.type]);
  const isToolExecuting = useMemo(() => {
    return isTool && isLast && !currentTaskInterrupted;
  }, [isLast, isTool, currentTaskInterrupted]);

  const isToolError = useMemo(() => {
    if (!isTool) return false;
    const toolIdx = taskRows.findIndex(row => row.ts === message.ts);
    if (toolIdx !== -1) {
      const next = taskRows[toolIdx + 1];
      if (next && next.type === "say" && next.say === "tool_error") {
        return true;
      }
    }

    return false;
  }, [isTool, message.ts, taskRows]);

  const workingSetEffect = useMemo(() => {
    if (isToolEditFileMessage(message)) {
      return (message as InternalLocalMessage_Tool_EditFile).workingSetEffect;
    }
    return null;
  }, [message]);

  const workingSetFileUIState = useMemo(() => {
    if (!workingSetEffect) {
      return null;
    }
    const indeterminated = indeterminatedWorkingSetEffects.find(v => v.messageTs === message.ts);
    if (indeterminated) {
      return indeterminated.state;
    }
    return workingSetEffect.status;
  }, [indeterminatedWorkingSetEffects, message.ts, workingSetEffect]);

  const provided = useMemo<ComposerTaskContextValue>(() => ({
    message,
    isLast,
    isToolExecuting,
    workingSetEffect,
    isToolError,
    workingSetFileUIState,
  }), [message, isLast, isToolExecuting, workingSetEffect, workingSetFileUIState, isToolError]);

  return (
    <ComposerTaskContext.Provider value={provided}>
      {children}
    </ComposerTaskContext.Provider>
  );
}

export function useComposerTaskContext() {
  const context = useContext(ComposerTaskContext);
  if (!context) {
    throw new Error("useComposerTaskContext must be used within a ComposerTaskContextProvider");
  }
  return context;
}
