import { Tooltip } from "@/components/Union/chakra-ui";
import { Box, Flex, Text } from "@chakra-ui/react";
import { ComposerTaskRow } from "./ComposerTaskRow";
import { HumanMessage } from "./components/HumanMessage";
import { InternalLocalMessage } from "shared/lib/agent/types";
import { ComposerConversationContextProvider } from "./context/ComposerConversationContext";
import { ComposerAction } from "./ComposerAction";
import { useComposerState } from "./context/ComposerStateContext";
import { ComposerStatusIndicator } from "./components/ComposerStatusIndicator";
import { useCallback, useMemo } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Icon } from "@/components/Union/t-iconify";
import { ConversationDiagnosticStatus } from "./components/Diagnostic/ConversationDiagnosticStatus";

export interface ComposerConversationProps {
  humanMessage: InternalLocalMessage;
  taskRows: InternalLocalMessage[];
  className?: string;
  /**
   * 是否是最后一轮对话
   */
  isLastConversation: boolean;
}

function ReadonlyMask() {
  const { editingMessageTs } = useComposerState();
  const onReadonlyMaskClick = useCallback(() => {
    if (editingMessageTs) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [editingMessageTs]);

  return (
    <Box position="absolute" top={0} left={0} w="100%" h="100%" zIndex={2} onClick={onReadonlyMaskClick} />
  );
}

export function ComposerConversation({ humanMessage, taskRows, isLastConversation }: ComposerConversationProps) {
  const { messageScrollContainerHeight, editingMessageTs, localMessages, currentMessageTs } = useComposerState();

  // 从humanMessage中获取indexed状态
  const indexed = humanMessage.indexed ?? true;

  const editingMessageIndex = useMemo(
    () => localMessages.findIndex(message => message.ts === editingMessageTs),
    [localMessages, editingMessageTs]);
  const currentMessageIndex = useMemo(
    () => localMessages.findIndex(message => message.ts === currentMessageTs),
    [localMessages, currentMessageTs]);
  const humanMessageIndex = useMemo(
    () => localMessages.findIndex(message => message.ts === humanMessage.ts),
    [localMessages, humanMessage],
  );

  const shouldDisplayHumanMessageSlightly
  = (editingMessageIndex !== -1 && humanMessageIndex > editingMessageIndex)
  || (currentMessageIndex !== -1 && humanMessageIndex > currentMessageIndex);
  const shouldDisplayTaskRowsSlightly
   = (editingMessageIndex !== -1 && humanMessageIndex >= editingMessageIndex)
   || (currentMessageIndex !== -1 && humanMessageIndex >= currentMessageIndex - 1/* currentMessage 是 humanMessage 后一条的 checkpoint 消息 */);

  const handleConversationClick = useCallback(() => {
    if (editingMessageIndex !== -1 && editingMessageIndex !== humanMessageIndex) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [editingMessageIndex, humanMessageIndex]);

  return (
    <ComposerConversationContextProvider
      humanMessage={humanMessage}
      taskRows={taskRows}
      isLastConversation={isLastConversation}
    >
      <Flex
        direction="column"
        gap="8px"
        minH={isLastConversation ? messageScrollContainerHeight * 0.85 + "px" : undefined}
        onClick={handleConversationClick}
      >
        <Box position="relative" opacity={shouldDisplayHumanMessageSlightly ? 0.5 : 1}>
          <HumanMessage data={humanMessage} zIndex={1} position="relative" />
          {shouldDisplayHumanMessageSlightly && <ReadonlyMask />}
        </Box>
        <Box opacity={shouldDisplayTaskRowsSlightly ? 0.5 : 1} position="relative">
          {!indexed && (
            <Flex
              direction="row"
              gap="4px"
              alignItems="start"
              cursor="pointer"
              onClick={() => {
                kwaiPilotBridgeAPI.extensionSettings.$openSettings();
              }}
              marginBottom="12px"
            >
              <Icon icon="mingcute:warning-fill" className="inline-block flex-shrink-0 size-[14px] mt-[3px] text-[#3794FFff]" />
              <Tooltip placement="top" hasArrow label="当前索引构建未完成，这可能会影响后续回答输出的效果，点击查看构建进度">
                <Text color="var(--vscode-foreground)">
                  代码索引构建未完成
                </Text>
              </Tooltip>
            </Flex>
          )}

          <Flex direction="column">
            {taskRows.map((message, index) => (
              <ComposerTaskRow
                isLast={isLastConversation && index === taskRows.length - 1}
                key={message.ts}
                message={message}
              >
              </ComposerTaskRow>
            ))}
          </Flex>
          <ConversationDiagnosticStatus isLastConversation={isLastConversation} />
          <ComposerAction taskRows={taskRows} isLast={isLastConversation}></ComposerAction>

          {shouldDisplayTaskRowsSlightly && <ReadonlyMask />}
        </Box>
        {isLastConversation && <ComposerStatusIndicator />}
        {isLastConversation && (
          <div className=" flex-auto w-full relative">
            <ReadonlyMask />
          </div>
        )}
      </Flex>

    </ComposerConversationContextProvider>
  );
}
