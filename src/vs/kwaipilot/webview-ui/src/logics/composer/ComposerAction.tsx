import { SingleIcon } from "@/components/SingleIcon";
import DislikeIcon from "@/assets/dislike.svg?react";
import { useCallback, useState } from "react";
import CopyIcon from "@/assets/copy.svg?react";
import LikeIcon from "@/assets/like.svg?react";
import LineIcon from "@/assets/line.svg?react";
import { useComposerState } from "./context/ComposerStateContext";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { InternalLocalMessage } from "shared/lib/agent/types";
import { useComposerConversationContext } from "./context/ComposerConversationContext";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { BoxProps, Flex } from "@chakra-ui/react";

interface IProps extends BoxProps {
  taskRows: InternalLocalMessage[];
}

export const Action: React.FC<IProps> = (props) => {
  const { taskRows, ...rest } = props;
  const { localMessages: messages } = useComposerState();
  const id = messages[0].sessionId;

  const [likeStatus, setLikeStatus] = useState<"like" | "unlike" | "cancel">("cancel");

  const lickIconClick = useCallback(() => {
    setLikeStatus((state) => {
      const res = state === "like" ? "cancel" : "like";
      if (res === "like") {
        collectClick("VS_LIKE_BUTTON");
        const parms: ReportOpt<"like"> = {
          key: "like",
          type: undefined,
        };
        reportUserAction(parms, id);
      }
      return res;
    });
  }, [id]);

  const onLikeOrUnlike = useCallback(() => {
    setLikeStatus((state) => {
      const res = state === "unlike" ? "cancel" : "unlike";
      if (res === "unlike") {
        collectClick("VS_UNLIKE_BUTTON");
        const parms: ReportOpt<"dislike"> = {
          key: "dislike",
          type: undefined,
        };
        reportUserAction(parms, id);
      }
      return res;
    });
  }, [id]);

  const onCopy = useCallback(() => {
    let result = "";
    taskRows.forEach((message) => {
      if (message.ask === "tool" || message.say === "tool") {
        const tool = JSON.parse(message.text || "{}");
        switch (tool.tool) {
          case "readFile":
            result += `文件查看: ${tool.path}\n`;
            break;
          case "codebaseSearch":
            result += `代码搜索: ${tool.query}\n`;
            break;
          case "listFilesTopLevel":
          case "listFilesRecursive":
            result += `文件列表查询: ${tool.path}\n`;
            break;
          case "grepSearch":
            result += `正则搜索: ${tool.regex}\n`;
            break;
          case "editFile":
            result += "写入文件\n";
            break;
        }
      }
      else {
        if (message.ask === "command" || message.say === "command") {
          result += "命令行执行\n";
        }
        else {
          if (message.text) {
            result += message.text + "\n";
          }
        }
      }
    });
    kwaiPilotBridgeAPI.copyToClipboard(result);
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: "已复制",
    });
  }, [taskRows]);

  return (
    <Flex justify="end" pr={2} {...rest}>

      <div className="flex gap-[6px]">
        <SingleIcon
          title="喜欢"
          active={likeStatus === "like"}
          onClick={lickIconClick}
        >
          <LikeIcon className="translate-y-[-1px] text-icon-foreground" />
        </SingleIcon>
        <SingleIcon
          title="不喜欢"
          active={likeStatus === "unlike"}
          onClick={onLikeOrUnlike}
        >
          <DislikeIcon className="translate-y-[1px] text-icon-foreground" />
        </SingleIcon>
        <div className="flex items-center text-border-vertical">
          <LineIcon />
        </div>
        <SingleIcon onClick={onCopy} title="复制">
          <CopyIcon className="text-icon-foreground" />
        </SingleIcon>
      </div>
    </Flex>
  );
};

export const ComposerAction: React.FC<IProps & { isLast: boolean }> = (props) => {
  const { taskRows, isLast, ...rest } = props;
  const { isFinished } = useComposerConversationContext();
  const { isStreaming } = useComposerState();

  if (!isFinished || (isStreaming && isLast)) {
    return null;
  }

  return <Action taskRows={taskRows} {...rest}></Action>;
};
