import { Tooltip } from "@/components/Union/chakra-ui";

interface IProps {
  content: string;
  extra?: string;
}

export const SubTitle = (props: IProps) => {
  const { content, extra } = props;

  return (
    <div className="text-disabledForeground text-[13px] leading-[18px] truncate flex items-center gap-1">

      <Tooltip label={content}>
        <span className="truncate cursor-pointer">
          {content}
        </span>
      </Tooltip>
      {extra && (
        <div
          className="size-[3px] rounded-full"
          style={{
            background: `rgb(var(--colors-text-common-disable))`,
          }}
        >

        </div>
      )}
      <Tooltip label={extra}>
        <span className="truncate cursor-pointer">
          {extra}
        </span>
      </Tooltip>
    </div>
  );
};
