import IconCheck from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_correct";
import KidIcon from "@/components/Union/kid";

import { useComposerTaskContext } from "../context/ComposerTaskContext";

export function CompletionResult() {
  const { message } = useComposerTaskContext();
  return (
    <div className="text-[var(--vscode-charts-green)]">
      <div className="  flex items-center gap-1">
        <KidIcon config={IconCheck} size={14} color="inherit"></KidIcon>
        任务完成
      </div>
      <div>{message.text}</div>

    </div>
  );
}
