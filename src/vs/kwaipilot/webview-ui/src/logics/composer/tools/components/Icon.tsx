import SearchIcon from "@/assets/tools/tool-search.svg?react";
import EditIcon from "@/assets/tools/tool-edit.svg?react";
import ReadIcon from "@/assets/tools/tool-read.svg?react";

interface IconProps {
  type: "search" | "edit" | "read";
}

export const Icon = (props: IconProps) => {
  const className = "flex-shrink-0 translate-y-[0.8px] size-[14px] text-icon-foreground border rounded-[3.5px] border-commandCenter-inactiveBorder";
  const { type } = props;

  if (type === "search") {
    return <SearchIcon className={className} />;
  }
  else if (type === "edit") {
    return <EditIcon className={className} />;
  }
  else {
    return <ReadIcon className={className} />;
  }
};
