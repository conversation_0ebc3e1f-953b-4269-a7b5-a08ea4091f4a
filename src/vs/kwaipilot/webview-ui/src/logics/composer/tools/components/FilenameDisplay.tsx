import { vsCss } from "@/style/vscode";
import { Typography, ConfigProvider, TooltipProps } from "antd";
import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

const highlightStyle: React.CSSProperties = {
  fontFeatureSettings: `'clig' off, 'liga' off`,
  fontWeight: "500",
  color: vsCss.menuSelectionBackground,
};
function renderHighlightText({ text, highlight }: { text: string;highlight: string }): ReactNode[] {
  const splitTextIntoSmallChunks = (text: string, chunkSize: number) => {
    let cursor = 0;
    const chunks: string[] = [];
    while (cursor < text.length) {
      const chunk = text.slice(cursor, cursor + chunkSize);
      chunks.push(chunk);
      cursor += chunkSize;
    }
    return chunks;
  };
  // antd ellipsis 按 element 为最小单元做 ellipsis 一段不能过长 否则会整段用...省略
  const parts = text.split(new RegExp(`(${highlight})`, "gi"))
    .map((part, i) => ({
      part,
      isHighlight: i % 2 === 1, // 奇数为高亮部分
    }));

  const minimizedParts = parts.map(part => part.isHighlight ? part : splitTextIntoSmallChunks(part.part, 5).map(v => ({ part: v, isHighlight: false }))).flat();

  return minimizedParts.map((part, index) =>
  // 不区分大小写地检查是否为高亮部分
    part.isHighlight
      ? (
          <span key={index} style={highlightStyle}>
            {part.part}
          </span>
        )
      : (
          <span key={index}>
            {part.part}
          </span>
        ),
  );
}

/**
 * 展示文件名，主要是中间截断
 */
export function FilenameDisplay({
  filename,
  className,
  maxSuffixLenth,
  tooltip,
  highlight,
}: {
  filename: string;
  className?: string;
  maxSuffixLenth?: number;
  tooltip?: React.ReactNode | TooltipProps;
  highlight?: string;
}) {
  const filenameSuffixI = filename.length - Math.min(Math.floor(filename.length / 2), maxSuffixLenth || 15);
  const filenamePrefixPart = filename.slice(0, filenameSuffixI);
  const filenameSuffixPart = filename.slice(filenameSuffixI);

  return (
    <ConfigProvider theme={{
      token: {
        colorBgSpotlight: vsCss.editorHoverWidgetBackground,
        colorText: vsCss.editorHoverWidgetForeground,
        colorTextLightSolid: vsCss.editorHoverWidgetForeground,
        fontFamily: "var(--vscode-font-family)",
      },
      cssVar: true,
    }}
    >

      <Typography.Text
        ellipsis={{
          tooltip: tooltip ?? {
            title: (
              <span className=" text-[12px] leading-[18px]">
                {filename}
              </span>
            ),
            styles: {
              body: {
                border: `1px solid ${vsCss.editorHoverWidgetBorder}`,
              },
            },
            arrow: false,
          },
          suffix: highlight
            ? (renderHighlightText({
                text: filenameSuffixPart,
                highlight,
              })) as unknown as string
            : filenameSuffixPart,
        }}
        className={twMerge(" flex-auto text-[12px] text-foreground whitespace-nowrap", className)}
      >
        {highlight
          ? renderHighlightText({
              text: filenamePrefixPart,
              highlight: highlight,
            })
          : filenamePrefixPart}
      </Typography.Text>
    </ConfigProvider>
  );
}
