import clsx from "clsx";

interface Props {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export const Container: React.FC<Props> = (props: Props) => {
  const { children, className, onClick } = props;
  const handleClick = (_e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    // Only trigger onClick if we're not selecting text
    const selection = window.getSelection();
    if (selection && selection.toString().length === 0 && onClick) {
      onClick();
    }
  };
  return (
    <div
      className={clsx(
        "w-full h-[34px] items-center rounded-lg flex gap-1 px-3 border-radio-inactiveBorder border-[0.6px] bg-statusBarItem-remoteHoverBackground",
        className)}
      onClick={handleClick}
    >
      {children}
    </div>
  );
};
