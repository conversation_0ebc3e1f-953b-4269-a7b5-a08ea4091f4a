import { useMemo } from "react";

interface IProps {
  type: string;
}

export const Title = (props: IProps) => {
  const { type } = props;

  const title = useMemo(() => {
    switch (type) {
      case "readFile":
        return "文件查看";
      case "codebaseSearch":
        return "代码搜索";
      case "listFilesTopLevel":
        return "文件列表查询";
      case "listFilesRecursive":
        return "文件列表查询";
      case "grepSearch":
        return "正则搜索";
    }
  }, [type]);

  return <div className="text-foreground leading-[18px] text-[13px] whitespace-nowrap align-middle">{title}</div>;
};
