import { WebviewComposerShape } from "shared/lib/bridge/protocol";
import { ComposerState } from "shared/lib/agent";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { useComposerMessageConsumer } from "@/store/composerMessageConsumer";
import { getGlobalObject } from "@/utils/globalObject";

export class WebviewComposer implements WebviewComposerShape {
  $postComposerStateUpdate(_state: ComposerState): void {
    throw new Error("Method not implemented.");
  }

  async $addToComposerContext(node: MentionNodeV2Structure | null): Promise<void> {
    const router = getGlobalObject("router");
    const location = router.state.location;
    if (!location.pathname.startsWith("/composer-v2") && !location.pathname.startsWith("/chat")) {
      // 对话页面不跳转, 否则跳转到助理模式
      router.navigate("/composer-v2");
    }
    if (node) {
      useComposerMessageConsumer.getState().produceComposerContext(node);
    }
  }
}
