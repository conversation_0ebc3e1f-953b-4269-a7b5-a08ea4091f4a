// NOTE: ide 和插件的 bridge 的基类
import { CodeActionParams, CodeSection, EditorConfig, InlineChatInfo, UserInfo } from "@shared/types";
import {
  ArtifactPreviewBridgeData,
  ArtifactPreviewData,
  ArtifactProvider,
  ExtractNativeBridgeResult,
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
  WebviewBridgeParams,
} from "@shared/types/bridge";
import { IKwaiPilotBridge } from "./IKwaiPilotBridge";
import { BridgeEventType } from "./types";
import { UploadFile } from "@shared/types/textarea";
import { LoggerSupplementaryField, ReportKeys, ReportOpt } from "@shared/types/logger";
import { BriefSessionItem, QAItem, SessionItem } from "@shared/types/chatHistory";
import { ReadDirectoryResult } from "@shared/types/file";
import { ResponseBase } from "@shared/types/agent";
import { ObservableAPI, ResponseMessage } from "shared";
import { Observable } from "rxjs";
import { v4 as uuidv4 } from "uuid";
import { WorkspaceState } from "@/services/repo-chat";
import { PersistedComposerHistory } from "shared/lib/agent/storage";
import { DiffContent, InternalLocalMessage } from "shared/lib/agent";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { ExtensionContext, WebviewContext } from "shared/lib/bridge/protocol";
import { WebviewComposer } from "./WebviewComposer";
import { InputBlockCodePath, OutPutBlockCodePath, Position } from "shared/lib/misc/blockcode";

export abstract class BaseKwaiPilotBridgeAPI {
  protected bridge!: IKwaiPilotBridge;

  observableAPI!: ObservableAPI;

  constructor() {
    // 空构造函数，所有初始化工作由子类完成
  }

  // 抽象方法：子类需要实现的不同初始化逻辑
  protected abstract initializeRpcContext(): void;

  // 不同的用户信息获取实现
  public abstract getAndWatchUserInfo(callback: (userinfo: UserInfo) => void): void;

  // 不同的Observable API创建方式
  protected abstract createObservableAPI(): ObservableAPI;

  // 统一的桥接设置方法，供子类调用
  protected setupBridge(bridge?: any) {
    if (bridge) {
      // 对于需要外部bridge的场景
      this.bridge.setBridge?.(bridge);
    }
    this.initializeRpcContext();
    this.registerRpcServices();
  }

  // 将rpcContext改为可选，由子类决定初始化时机
  rpcContext!: IRPCProtocol;

  private registerRpcServices() {
    if (!this.rpcContext) {
      // 记录错误日志
      this.printLogger({
        level: "error",
        msg: "尝试注册 RPC 服务时 rpcContext 未初始化",
        scope: "BaseKwaiPilotBridgeAPI",
      });
      return;
    }
    this.rpcContext.set(WebviewContext.WebviewComposer, new WebviewComposer());
  }

  get extensionComposer() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionComposer)!;
  }

  get extensionMisc() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionMisc);
  }

  get extensionConfig() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionConfig)!;
  }

  get extensionDeveloper() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionDeveloper)!;
  }

  get extensionSettings() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionSettings)!;
  }

  get extensionToLoacl() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionToLoacl)!;
  }

  get extensionMCP() {
    return this.rpcContext.getProxy(ExtensionContext.ExtensionMCP)!;
  }

  get agent() {
    return {
      indexFile: (params: { file: string; action: "modify" | "delete" | "create" }) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.INDEX_FILE, {
          id: this.bridge.generateId(),
          event: BridgeEventType.INDEX_FILE,
          payload: params,
        });
      },
      indexBuild: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.INDEX_BUILD, {
          id: this.bridge.generateId(),
          event: BridgeEventType.INDEX_BUILD,
        });
      },
      indexPause: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.INDEX_PAUSE, {
          id: this.bridge.generateId(),
          event: BridgeEventType.INDEX_PAUSE,
        });
      },
      indexClearIndex: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.INDEX_CLEAR_INDEX, {
          id: this.bridge.generateId(),
          event: BridgeEventType.INDEX_CLEAR_INDEX,
        });
      },
      indexRepoIndex: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.INDEX_REPO_INDEX, {
          id: this.bridge.generateId(),
          event: BridgeEventType.INDEX_REPO_INDEX,
        });
      },
      getRepoIndexing: async (): Promise<boolean> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.REPO_INDEXING,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.REPO_INDEXING,
            },
            resolve,
          );
        });
      },
      stateCheckRepoState: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.STATE_CHECK_REPO_STATE, {
          id: this.bridge.generateId(),
          event: BridgeEventType.STATE_CHECK_REPO_STATE,
        });
      },
      searchSearch: (params: {
        query: string;
        chatHistory: {
          role: "user" | "assistant";
          content: string;
        }[];
        topK?: number;
        targetDirectory?: string[];
      }): Promise<ResponseBase<any>> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.SEARCH_SEARCH,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.SEARCH_SEARCH,
              payload: params,
            },
            resolve,
          );
        });
      },
    };
  }

  get editor() {
    return {
      insertCode: (params: { content: string }) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.CODE_INSERT, {
          id: this.bridge.generateId(),
          event: BridgeEventType.CODE_INSERT,
          payload: params,
        });
      },
      fileEdit: (params: {
        filename: string;
        modelOutput: string;
        sessionId: string;
        chatId: string;
        applyId: string;
      }): Promise<undefined> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.FILE_EDIT, {
            id: this.bridge.generateId(),
            event: BridgeEventType.FILE_EDIT,
            payload: params,
          }, resolve);
        });
      },
      showCodeDiff: (params: {
        content: string;
        section: CodeSection;
        fullPath: string;
      }) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.CODE_DIFF, {
          id: this.bridge.generateId(),
          event: BridgeEventType.CODE_DIFF,
          payload: params,
        });
      },
      getFilePathOfBlockCode: (content: string, sessionId: string, pos?: Position): Promise<OutPutBlockCodePath | undefined> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.GET_FILEPATH_OF_BLOCKCODE,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.GET_FILEPATH_OF_BLOCKCODE,
              payload: { content, sessionId, pos },
            },
            (data) => {
              resolve(data);
            },
          );
        });
      },

      /**
       * 打开文件到编辑区
       * @param filepath 文件相对工作区的路径
       */
      openFileToEditor: (filepath: string, startLine?: number, endLine?: number, range?: InputBlockCodePath["range"]) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR, {
          id: this.bridge.generateId(),
          event: BridgeEventType.OPEN_FILE_TO_EDITOR,
          payload: { filepath, startLine, endLine, range },
        });
      },
      openFileToEditorMaybeDiffEditor: (filepath: string) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR, {
          id: this.bridge.generateId(),
          event: BridgeEventType.OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR,
          payload: { filepath },
        });
      },
      acceptAllFileDiff: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ACCEPT_REJECT_DIFF, {
          id: this.bridge.generateId(),
          event: BridgeEventType.ACCEPT_REJECT_DIFF,
          payload: {
            accept: true,
          },
        });
      },
      rejectAllFileDiff: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ACCEPT_REJECT_DIFF, {
          id: this.bridge.generateId(),
          event: BridgeEventType.ACCEPT_REJECT_DIFF,
          payload: {
            accept: false,
          },
        });
      },
      acceptDiff: (filepath: string) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ACCEPT_REJECT_DIFF, {
          id: this.bridge.generateId(),
          event: BridgeEventType.ACCEPT_REJECT_DIFF,
          payload: {
            accept: true,
            filepath,
          },
        });
      },
      rejectDiff: (filepath: string) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ACCEPT_REJECT_DIFF, {
          id: this.bridge.generateId(),
          event: BridgeEventType.ACCEPT_REJECT_DIFF,
          payload: {
            accept: false,
            filepath,
          },
        });
      },
      keepDiff: (payload?: { filepath?: string; abortChat?: boolean }) => {
        return new Promise((resolve) => {
          this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK, {
            id: this.bridge.generateId(),
            event: BridgeEventType.COMPOSER_DIFF_FEEDBACK,
            payload: { type: "keep", ...payload },
          }, resolve);
        });
      },
      undoDiff: (payload?: { filepath?: string; abortChat?: boolean }) => {
        return new Promise((resolve) => {
          this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK, {
            id: this.bridge.generateId(),
            event: BridgeEventType.COMPOSER_DIFF_FEEDBACK,
            payload: { type: "undo", ...payload },
          }, resolve);
        });
      },
      saveDiff: (payload: { ts: number; filepath: string; diffContent: DiffContent | undefined }) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_SAVE, {
          id: this.bridge.generateId(),
          event: BridgeEventType.COMPOSER_DIFF_SAVE,
          payload,
        });
      },
      applyFile: (payload: { message: InternalLocalMessage }) => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_APPLY_FILE, {
          id: this.bridge.generateId(),
          event: BridgeEventType.COMPOSER_APPLY_FILE,
          payload,
        });
      },
      clearAllDiffState: () => {
        // 清除所有diff状态, 由子类实现
      },
    };
  }

  get terminal() {
    return {
      sendText: (text: string): Promise<{ success: boolean; message?: string }> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.TERMINAL_SEND_TEXT,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.TERMINAL_SEND_TEXT,
              payload: { text },
            },
            resolve,
          );
        });
      },
    };
  }

  get fs() {
    return {
      readDirectory: (
        path: string,
        options: { recursive: boolean; excludes: string[] },
      ): Promise<ReadDirectoryResult> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.READ_DIRECTORY,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.READ_DIRECTORY,
              payload: { path, options },
            },
            (value: any) => value && resolve(value),
          );
        });
      },
      // 读取文件
      readFile: (filePath: string): Promise<{ content: string }> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.READ_FILE,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.READ_FILE,
              payload: { filePath },
            },
            (value: any) => value && resolve(value),
          );
        });
      },
      uploadFile: (): Promise<{
        fileInfo: UploadFile[];
      }> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(
            NATIVE_BRIDGE_EVENT_NAME.UPLOAD_FILE,
            {
              id: this.bridge.generateId(),
              event: BridgeEventType.UPLOAD_FILE,
            },
            (value: any) => value && resolve(value),
          );
        });
      },
    };
  }

  get logger() {
    return {
      onReportUserAction: (callback: (params: ReportOpt<keyof ReportKeys>) => void) => {
        this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.REPORT_USER_ACTION, callback);
      },
    };
  }

  get config() {
    return {
      openSetting: () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.OPEN_SETTING, {
          id: this.bridge.generateId(),
          event: BridgeEventType.OPEN_SETTING,
        });
      },
      getThemeSettings: (themeId: string, themeKind: string): Promise<{ settings: Record<string, any>; id: string; name: string } | undefined> => {
        return new Promise((resolve) => {
          this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.GET_THEME_SETTINGS, {
            id: this.bridge.generateId(),
            event: NATIVE_BRIDGE_EVENT_NAME.GET_THEME_SETTINGS,
            payload: { themeId, themeKind },
          }, resolve);
        });
      },
    };
  }

  // 主题色更新时
  public onThemeChange(callback: ({ theme }: { theme: "dark" | "light" }) => void) {
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.SWITCH_THEME, callback);
  }

  public onActionForCode(callback: (params: CodeActionParams) => void) {
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.ACTION_FOR_CODE, callback);
  }

  public onActiveTextChange(
    callback: (param: {
      document: {
        fileName: string;
        relativePath: string;

        languageId: string;
      };
    }) => void,
  ) {
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.CHANGE_ACTIVE_TEXT_EDITOR, callback);
  }

  public onActiveProjectChange(callback: (params: { list: string[] }) => void) {
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.ACTIVE_PROJECTS_NAME, callback);
  }

  public onPredictionImage(
    callback: (params: {
      sourceBlockContent: string;
      targetBlockContent: string;
      languageId: string;
      editorConfig: EditorConfig;
    }) => Promise<{
      dataUrl: string;
      backgroundColor: string;
    }>,
  ) {
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.PREDICTION_IMAGE, callback);
  }

  public onInlineChat(callback: (params: InlineChatInfo | undefined) => void) {
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.INLINE_CHAT, callback);
  }

  // 以下是webview 调用native 的方法， 以上是native 调用webview 的方法
  public webviewBridgeReady() {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.WEBVIEW_BRIDGE_READY, {
      id: this.bridge.generateId(),
      event: BridgeEventType.WEBVIEW_BRIDGE_READY,
    });
  }

  // 打开URL
  public openUrl(url: string) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.OPEN_URL, {
      id: this.bridge.generateId(),
      event: BridgeEventType.OPEN_URL,
      payload: { url },
    });
  }

  // 显示Toast
  public showToast(info: { message: string; level: "error" | "info" }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.SHOW_TOAST, {
      id: this.bridge.generateId(),
      event: BridgeEventType.SHOW_TOAST,
      payload: info,
    });
  }

  // 复制到剪贴板
  public copyToClipboard(text: string) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COPY_TO_CLIPBOARD, {
      id: this.bridge.generateId(),
      event: BridgeEventType.COPY_TO_CLIPBOARD,
      payload: { text },
    });
  }

  // 获取系统信息
  public getSystemInfo(): Promise<{
    hostname: string;
    ideVersion: string;
    pluginVersion: string;
    platform: string;
    release: string;
    deviceId: string;
    ide: string;
    machine: string;
    arch: string;
    version: string;
  }> {
    return new Promise((resolve) => {
      const messageId = this.bridge.generateId();
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_SYSTEM_INFO,
        {
          id: messageId,
          event: BridgeEventType.GET_SYSTEM_INFO,
        },
        resolve,
      );
    });
  }

  // 获取git信息
  public getGitInfo() {
    return new Promise((resolve) => {
      const messageId = this.bridge.generateId();
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_GIT_INFO,
        {
          id: messageId,
          event: BridgeEventType.GET_GIT_INFO,
        },
        resolve,
      );
    });
  }

  // 获取并监听 编辑区配置
  public getAndWatchEditorConfig(callback: (editorConfig: EditorConfig) => void) {
    const messageId = this.bridge.generateId();
    this.bridge.postMessage(
      NATIVE_BRIDGE_EVENT_NAME.GET_AND_WATCH_EDITOR_CONFIG,
      {
        id: messageId,
        event: BridgeEventType.GET_AND_WATCH_EDITOR_CONFIG,
      },
      callback,
    );
    this.bridge.onMessage(WEBVIEW_BRIDGE_EVENT_NAME.GET_AND_WATCH_EDITOR_CONFIG_CALLBACK, callback);
  }

  public getActiveEditor(): Promise<ExtractNativeBridgeResult<NATIVE_BRIDGE_EVENT_NAME.GET_ACTIVE_EDITOR>> {
    return new Promise((resolve) => {
      const messageId = this.bridge.generateId();
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_ACTIVE_EDITOR,
        {
          id: messageId,
          event: BridgeEventType.GET_ACTIVE_EDITOR,
        },
        resolve,
      );
    });
  }

  // 获取当前选中的代码
  public getSelectionContent() {
    return new Promise<ExtractNativeBridgeResult<NATIVE_BRIDGE_EVENT_NAME.GET_SELECTION_CONTENT>>((resolve) => {
      const messageId = this.bridge.generateId();
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_SELECTION_CONTENT,
        {
          id: messageId,
          event: BridgeEventType.GET_SELECTION_CONTENT,
        },
        resolve,
      );
    });
  }

  // 执行命令
  public executeCmd(cmd: string) {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.EXECUTE_CMD,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.EXECUTE_CMD,
          payload: { cmd },
        },
        resolve,
      );
    });
  }

  public getWorkspaceStorageUri(): Promise<{ result: string }> {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_WORKSPACE_STORAGE_URI,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_WORKSPACE_STORAGE_URI,
        },
        resolve,
      );
    });
  }

  // 获取工作区路径
  public getWorkspaceUri(): Promise<{ result: string }> {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_WORKSPACE_URI,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_WORKSPACE_URI,
        },
        resolve,
      );
    });
  }

  // 获取state
  public getState<T>(key: string): Promise<{ value: T }> {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_STATE,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_STATE,
          // TODO: types
          payload: { key: key as WorkspaceState },
        },
        resolve,
      );
    });
  }

  // 更新state
  public updateState<T>(key: string, value: T) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.UPDATE_STATE, {
      id: this.bridge.generateId(),
      event: BridgeEventType.UPDATE_STATE,
      // TODO: types
      payload: { key: key as WorkspaceState, value },
    });
  }

  // 获取文件状态
  public getFileStatus(filePath: string): Promise<{ status: any }> {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_FILE_STATUS,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_FILE_STATUS,
          payload: { filePath },
        },
        (value: any) => value && resolve(value),
      );
    });
  }
  // 上传文件

  // 打印日志
  public printLogger(data: {
    level: "silly" | "debug" | "verbose" | "info" | "warn" | "error";
    msg: string;
    scope: string;
    tags?: LoggerSupplementaryField;
  }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.PRINT_LOGGER, {
      id: this.bridge.generateId(),
      event: BridgeEventType.PRINT_LOGGER,
      payload: data,
    });
  }

  // 设置当前仓库
  public setActiveProject(name: string) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.SET_ACTIVE_PROJECT_NAME, {
      id: this.bridge.generateId(),
      event: BridgeEventType.SET_ACTIVE_PROJECT_NAME,
      payload: { name },
    });
  }

  // 获取历史记录
  public getSession<T extends { sessionId: string } | { page: number; pageSize: number; timeRange: string }>(
    data: T,
  ): Promise<
      T extends { sessionId: string }
        ? SessionItem
        : {
            sessionList: BriefSessionItem[];
          }
    > {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_SESSION,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_SESSION,
          payload: data,
        },
        resolve,
      );
    });
  }

  public addSession(data: SessionItem) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ADD_SESSION, {
      id: this.bridge.generateId(),
      event: BridgeEventType.ADD_SESSION,
      payload: data,
    });
  }

  public addMessage(data: { item: QAItem; sessionId: string; chatId: string }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ADD_MESSAGE, {
      id: this.bridge.generateId(),
      event: BridgeEventType.ADD_MESSAGE,
      payload: data,
    });
  }

  public updateSessionInfo(data: {
    sessionId: string;
    sessionName?: string;
    clearContextIndex?: number[];
    expiredIndex?: number[];
  }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.UPDATE_SESSION, {
      id: this.bridge.generateId(),
      event: BridgeEventType.UPDATE_SESSION,
      payload: data,
    });
  }

  public updateComposerSessionName(data: { sessionId: string; name: string }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_UPDATE_SESSION_NAME, {
      id: this.bridge.generateId(),
      event: BridgeEventType.COMPOSER_UPDATE_SESSION_NAME,
      payload: data,
    });
  }

  public composerToggleTerminal() {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_TOGGLE_TERMINAL, {
      id: this.bridge.generateId(),
      event: BridgeEventType.COMPOSER_TOGGLE_TERMINAL,
    });
  }

  public deleteSession(sessionId: string) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.DELETE_SESSION, {
      id: this.bridge.generateId(),
      event: BridgeEventType.DELETE_SESSION,
      payload: { sessionId },
    });
  }

  public clearSession() {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.CLEAR_SESSION, {
      id: this.bridge.generateId(),
      event: BridgeEventType.CLEAR_SESSION,
    });
  }

  public getOpenTabFiles(): Promise<{ list: string[] }> {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_OPEN_TAB_FILES,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_OPEN_TAB_FILES,
        },
        resolve,
      );
    });
  }

  public login() {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.LOGIN, {
      id: this.bridge.generateId(),
      event: BridgeEventType.LOGIN,
    });
  }

  public getConfig<T>(key: string): Promise<{ value: T }> {
    return new Promise((resolve) => {
      this.bridge.postMessage(
        NATIVE_BRIDGE_EVENT_NAME.GET_CONFIG,
        {
          id: this.bridge.generateId(),
          event: BridgeEventType.GET_CONFIG,
          payload: { key },
        },
        resolve,
      );
    });
  }

  /** 内联聊天消息 */
  public streamDiffMessage({
    filename, message, autoCreate, autoOpen,
  }: {
    filename?: string;
    message: string;
    autoCreate?: boolean;
    autoOpen?: boolean;
  }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.STREAM_DIFF_MESSAGE, {
      id: this.bridge.generateId(),
      event: BridgeEventType.STREAM_DIFF_MESSAGE,
      payload: {
        filename,
        message,
        autoCreate,
        autoOpen,
      },
    });
  }

  public clearInlineInfo() {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.CLEAR_INLINE_INFO, {
      id: this.bridge.generateId(),
      event: BridgeEventType.CLEAR_INLINE_INFO,
    });
  }

  public getComposerHistory(): Promise<PersistedComposerHistory> {
    return new Promise((resolve) => {
      this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_HISTORY_GET, {
        id: this.bridge.generateId(),
        event: BridgeEventType.COMPOSER_HISTORY_GET,
      }, resolve);
    });
  }

  public clearTask() {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_CLEAR_TASK, {
      id: this.bridge.generateId(),
      event: BridgeEventType.COMPOSER_CLEAR_TASK,
    });
  }

  public previewArtifact(data: ArtifactPreviewData) {
    this.bridge.postMessage(
      NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW,
      {
        id: this.bridge.generateId(),
        event: NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW,
        payload: {
          eventType: "start",
          data,
        } satisfies ArtifactPreviewBridgeData,
      },
      () => {
        this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW, {
          id: this.bridge.generateId(),
          event: NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW,
          payload: {
            eventType: "end",
            provider: data.provider,
          } satisfies ArtifactPreviewBridgeData,
        });
      },
    );
  }

  public createArtifactPreviewStream(): ArtifactStreamHandler {
    let started = false;
    const { bridge } = this;
    let currentProvider: ArtifactProvider | null = null;
    return {
      sendData(data) {
        if (!currentProvider) {
          currentProvider = data.provider;
        }
        else if (currentProvider !== data.provider) {
          throw new Error("Artifact provider not match");
        }
        const eventType: ArtifactPreviewBridgeData["eventType"] = started ? "data" : "start";
        started = true;

        const payload: ArtifactPreviewBridgeData = {
          eventType,
          data,
        };
        bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW, {
          id: bridge.generateId(),
          event: NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW,
          payload,
        });
      },
      end(error) {
        if (!currentProvider) {
          throw new Error("Artifact provider not match");
        }
        const payload: ArtifactPreviewBridgeData = error
          ? {
              eventType: "error",
              error,
              provider: currentProvider,
            }
          : {
              eventType: "end",
              provider: currentProvider,
            };
        bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW, {
          id: bridge.generateId(),
          event: NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW,
          payload,
        });
      },
    };
  }

  /*
  * 添加无状态消息监听器，注：可以重复监听哦！
  *
  * Q：为什么要有这么扯淡的多余封装？
  *
  * A：之前的监听方式`this.bridge.onMessage`, 要求只能调用一次，即只能有一个监听函数，多次调用，后面的会把之前的覆盖掉。
  * 估计设计 bridgeAPI，而不是让外部直接调用 bridge.onMessage, 也是出于这样的考虑：将 onMessage 调用收敛到内部，防止多次调用
  *
  * 而新版新增了addMessageListener，可以重复监听，bridgeAPI 意义不大了，但还是先遵循原先的设计架构，暴露统一的出口给外部
  */
  public addMessageListener<T extends WEBVIEW_BRIDGE_EVENT_NAME>(
    event: T,
    listener: (payload: WebviewBridgeParams[T]) => unknown,
  ): () => void {
    return this.bridge.addMessageListener(event, listener);
  }
}

export interface ArtifactStreamHandler {
  sendData(data: ArtifactPreviewData): void;
  end(error?: unknown): void;
}

/**
 * Send a message and return an Observable that will emit the responses.
 */
export function callExtensionAPI<T>(messageAPI: IKwaiPilotBridge, method: string, args: unknown[]): Observable<T> {
  return new Observable<T>((observer) => {
    const streamId = uuidv4();

    // Stream state
    let finished = false;

    // Set up a listener for the messages in the response stream.
    function messageListener({ streamId: responseStreamId, streamEvent, data }: ResponseMessage): void {
      // If the message is on the stream for this call, emit it.
      if (responseStreamId === streamId) {
        switch (streamEvent) {
          case "next":
            observer.next(data as T);
            break;
          case "error":
            observer.error(data);
            break;
          case "complete":
            finished = true;
            observer.complete();
            break;
        }
      }
    }
    const disposeListener = messageAPI.addMessageListener(
      WEBVIEW_BRIDGE_EVENT_NAME.OBSERVABLE_RESPONSE,
      messageListener,
    );

    messageAPI.postOneWayMessage(NATIVE_BRIDGE_EVENT_NAME.OBSERVABLE_REQUEST, { streamId, method, args });

    return () => {
      disposeListener();
      if (!finished) {
        // Send abort message to peer if the observable is unsubscribed before completion.
        // logRPCMessage("W->X", () => `aborting stream ${streamId}`);
        messageAPI.postOneWayMessage(NATIVE_BRIDGE_EVENT_NAME.OBSERVABLE_REQUEST, {
          streamIdToAbort: streamId,
        });
      }
    };
  });
}
/**
 * Create a proxy for an extension API method.
 */
export function proxyExtensionAPI<M extends keyof ObservableAPI>(messageAPI: IKwaiPilotBridge, method: M): ObservableAPI[M] {
  return (...args: any[]): Observable<any> => {
    // logRPCMessage("X->W", () => `call method=${method} args=${JSON.stringify(args)}`);
    return callExtensionAPI(messageAPI, method, args);
  };
}
