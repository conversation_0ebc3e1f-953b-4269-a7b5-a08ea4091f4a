export const ext2IconName: Record<string, string> = {
  ts: "typescript",
  tsx: "typescript",
  js: "javascript",
  jsx: "javascript",
  vue: "vue",
  html: "html",
  css: "css",
  csv: "csv",
  c: "cpp",
  cpp: "cpp",
  json: "json",
  sass: "sass",
  sh: "shell",
  rs: "rust",
  less: "less",
};

export const ext2LanguageId: Record<string, string> = {
  "abap": "ABAP",
  "bat": "Windows Batch",
  "bibtex": "BibTeX",
  "clojure": "Clojure",
  "coffeescript": "CoffeeScript",
  "c": "C",
  "cpp": "C++",
  "csharp": "C#",
  "dockercompose": "Compose",
  "css": "CSS",
  "cuda-cpp": "CUDA C++",
  "d": "D",
  "pascal": "Delphi",
  "diff": "Diff",
  "dockerfile": "Dockerfile",
  "erlang": "Erlang",
  "fsharp": "F#",
  "git-commit": "Git Commit",
  "git-rebase": "Git Rebase",
  "go": "Go",
  "groovy": "Groovy",
  "handlebars": "Handlebars",
  "haml": "Haml",
  "haskell": "Haskell",
  "html": "HTML",
  "ini": "Ini",
  "java": "Java",
  "javascript": "JavaScript",
  "javascriptreact": "JavaScript JSX",
  "json": "JSON",
  "jsonc": "JSON with Comments",
  "julia": "Julia",
  "latex": "LaTeX",
  "less": "Less",
  "lua": "Lua",
  "makefile": "Makefile",
  "markdown": "Markdown",
  "objective-c": "Objective-C",
  "objective-cpp": "Objective-C++",
  "ocaml": "OCaml",
  "perl": "Perl",
  "perl6": "Perl 6",
  "php": "PHP",
  "plaintext": "Plain Text",
  "powershell": "PowerShell",
  "jade": "Pug",
  "pug": "Pug",
  "python": "Python",
  "r": "R",
  "razor": "Razor (cshtml)",
  "ruby": "Ruby",
  "rust": "Rust",
  "scss": "SCSS",
  "sass": "Sass",
  "shellscript": "Shell Script (Bash)",
  "slim": "Slim",
  "sql": "SQL",
  "stylus": "Stylus",
  "svelte": "Svelte",
  "swift": "Swift",
  "tex": "TeX",
  "vb": "Visual Basic",
  "vue": "Vue",
  "vue-html": "Vue HTML",
  "xml": "XML",
  "xsl": "XSL",
  "yaml": "YAML",
  "ts": "TypeScript",
  "tsx": "TypeScript JSX",
  "js": "JavaScript",
  "jsx": "JavaScript JSX",
  "htm": "HTML",
  "h": "C",
  "hpp": "C++",
  "cs": "C#",
  "py": "Python",
  "rb": "Ruby",
  "rs": "Rust",
  "md": "Markdown",
  "yml": "YAML",
  "sh": "Shell Script (Bash)",
  "bash": "Shell Script (Bash)",
};
export const DEFAULT_MODEL_TYPE = "kwaipilot_pro_32k";

export const DEFAULT_PROXY_URL = "https://kwaipilot.corp.kuaishou.com";
