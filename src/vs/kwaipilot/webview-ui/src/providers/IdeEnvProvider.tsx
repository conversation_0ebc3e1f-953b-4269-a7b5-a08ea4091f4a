import React, { createContext, useContext } from "react";
import { VSCodeServicesAccessor } from "../mount-export";

interface IdeEnvContextType {
  isKwaiPilotIDE?: boolean;
  accessor?: VSCodeServicesAccessor;
}

export const IdeEnvContext = createContext<IdeEnvContextType | undefined>(
  undefined,
);

interface IdeEnvProviderProps {
  children: React.ReactNode;
  value?: IdeEnvContextType;
}

export const IdeEnvProvider: React.FC<IdeEnvProviderProps> = ({
  children,
  value,
}) => {
  return (
    <IdeEnvContext.Provider value={value}>{children}</IdeEnvContext.Provider>
  );
};

export const useIdeEnv = () => {
  const context = useContext(IdeEnvContext);
  if (context === undefined) {
    throw new Error("useIdeEnv must be used within an IdeEnvProvider");
  }
  return context;
};
