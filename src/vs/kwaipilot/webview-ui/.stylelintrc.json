{"plugins": ["./stylelint-plugin-no-body-selector.js"], "rules": {"selector-disallowed-list": ["body", "html", ":root"], "rule-empty-line-before": null, "at-rule-empty-line-before": null, "comment-empty-line-before": null, "custom/no-body-selector": true}, "reportDisallowedSelector": {"body": "不要使用 body，需要考虑在ide里原生渲染情况，请使用 [data-element-type=\"body\"] 代替", "html": "不要使用 html，需要考虑在ide里原生渲染情况，请使用 [data-element-type=\"root\"] 代替", ":root": "不要使用 :root，需要考虑在ide里原生渲染情况，请使用 [data-element-type=\"root\"] 代替"}, "ignoreSelectors": {"custom/no-body-selector": ["[data-element-type=\"body\"]"]}}