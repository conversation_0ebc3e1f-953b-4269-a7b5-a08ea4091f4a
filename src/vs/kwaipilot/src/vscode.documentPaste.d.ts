import * as vscode from "vscode";

declare module "vscode" {

  /**
   * Provides additional metadata about how a {@linkcode DocumentPasteEditProvider} works.
   */
  export interface DocumentPasteProviderMetadata {
    /**
     * List of {@link DocumentDropOrPasteEditKind kinds} that the provider may return in {@linkcode DocumentPasteEditProvider.provideDocumentPasteEdits provideDocumentPasteEdits}.
     *
     * This is used to filter out providers when a specific {@link DocumentDropOrPasteEditKind kind} of edit is requested.
     */
    readonly providedPasteEditKinds: readonly DocumentDropOrPasteEditKind[];

    /**
     * Mime types that {@linkcode DocumentPasteEditProvider.prepareDocumentPaste prepareDocumentPaste} may add on copy.
     */
    readonly copyMimeTypes?: readonly string[];

    /**
     * Mime types that {@linkcode DocumentPasteEditProvider.provideDocumentPasteEdits provideDocumentPasteEdits} should be invoked for.
     *
     * This can either be an exact mime type such as `image/png`, or a wildcard pattern such as `image/*`.
     *
     * Use `text/uri-list` for resources dropped from the explorer or other tree views in the workbench.
     *
     * Use `files` to indicate that the provider should be invoked if any {@link DataTransferFile files} are present in the {@linkcode DataTransfer}.
     * Note that {@linkcode DataTransferFile} entries are only created when pasting content from outside the editor, such as
     * from the operating system.
     */
    readonly pasteMimeTypes?: readonly string[];
  }

  /**
   * The reason why paste edits were requested.
   */
  export enum DocumentPasteTriggerKind {
    /**
     * Pasting was requested as part of a normal paste operation.
     */
    Automatic = 0,

    /**
     * Pasting was requested by the user with the `paste as` command.
     */
    PasteAs = 1,
  }
  /**
   * Additional information about the paste operation.
   */
  export interface DocumentPasteEditContext {

    /**
     * Requested kind of paste edits to return.
     *
     * When a explicit kind if requested by {@linkcode DocumentPasteTriggerKind.PasteAs PasteAs}, providers are
     * encourage to be more flexible when generating an edit of the requested kind.
     */
    readonly only: DocumentDropOrPasteEditKind | undefined;

    /**
     * The reason why paste edits were requested.
     */
    readonly triggerKind: DocumentPasteTriggerKind;
  }

  /**
   * Identifies a {@linkcode DocumentDropEdit} or {@linkcode DocumentPasteEdit}
   */
  export class DocumentDropOrPasteEditKind {
    static readonly Empty: DocumentDropOrPasteEditKind;

    /**
     * The root kind for basic text edits.
     *
     * This kind should be used for edits that insert basic text into the document. A good example of this is
     * an edit that pastes the clipboard text while also updating imports in the file based on the pasted text.
     * For this we could use a kind such as `text.updateImports.someLanguageId`.
     *
     * Even though most drop/paste edits ultimately insert text, you should not use {@linkcode Text} as the base kind
     * for every edit as this is redundant. Instead a more specific kind that describes the type of content being
     * inserted should be used instead. For example, if the edit adds a Markdown link, use `markdown.link` since even
     * though the content being inserted is text, it's more important to know that the edit inserts Markdown syntax.
     */
    static readonly Text: DocumentDropOrPasteEditKind;

    /**
     * Root kind for edits that update imports in a document in addition to inserting text.
     */
    static readonly TextUpdateImports: DocumentDropOrPasteEditKind;

    /**
     * Use {@linkcode DocumentDropOrPasteEditKind.Empty} instead.
     */
    private constructor(value: string);

    /**
     * The raw string value of the kind.
     */
    readonly value: string;

    /**
     * Create a new kind by appending additional scopes to the current kind.
     *
     * Does not modify the current kind.
     */
    append(...parts: string[]): DocumentDropOrPasteEditKind;

    /**
     * Checks if this kind intersects `other`.
     *
     * The kind `"text.plain"` for example intersects `text`, `"text.plain"` and `"text.plain.list"`,
     * but not `"unicorn"`, or `"textUnicorn.plain"`.
     *
     * @param other Kind to check.
     */
    intersects(other: DocumentDropOrPasteEditKind): boolean;

    /**
     * Checks if `other` is a sub-kind of this `DocumentDropOrPasteEditKind`.
     *
     * The kind `"text.plain"` for example contains `"text.plain"` and `"text.plain.list"`,
     * but not `"text"` or `"unicorn.text.plain"`.
     *
     * @param other Kind to check.
     */
    contains(other: DocumentDropOrPasteEditKind): boolean;
  }

  /**
   * An edit the applies a paste operation.
   */
  export class DocumentPasteEdit {
    /**
     * Human readable label that describes the edit.
     */
    title: string;

    /**
     * {@link DocumentDropOrPasteEditKind Kind} of the edit.
     */
    kind: DocumentDropOrPasteEditKind;

    /**
     * The text or snippet to insert at the pasted locations.
     *
     * If your edit requires more advanced insertion logic, set this to an empty string and provide an {@link DocumentPasteEdit.additionalEdit additional edit} instead.
     */
    insertText: string | SnippetString;

    /**
     * An optional additional edit to apply on paste.
     */
    additionalEdit?: WorkspaceEdit;

    /**
     * Controls ordering when multiple paste edits can potentially be applied.
     *
     * If this edit yields to another, it will be shown lower in the list of possible paste edits shown to the user.
     */
    yieldTo?: readonly DocumentDropOrPasteEditKind[];

    /**
     * Create a new paste edit.
     *
     * @param insertText The text or snippet to insert at the pasted locations.
     * @param title Human readable label that describes the edit.
     * @param kind {@link DocumentDropOrPasteEditKind Kind} of the edit.
     */
    constructor(insertText: string | SnippetString, title: string, kind: DocumentDropOrPasteEditKind);
  }

  /**
   * Provider invoked when the user copies or pastes in a {@linkcode TextDocument}.
   */
  export interface DocumentPasteEditProvider<T extends DocumentPasteEdit = DocumentPasteEdit> {

    /**
     * Optional method invoked after the user copies from a {@link TextEditor text editor}.
     *
     * This allows the provider to attach metadata about the copied text to the {@link DataTransfer}. This data
     * transfer is then passed back to providers in {@linkcode provideDocumentPasteEdits}.
     *
     * Note that currently any changes to the {@linkcode DataTransfer} are isolated to the current editor window.
     * This means that any added metadata cannot be seen by other editor windows or by other applications.
     *
     * @param document Text document where the copy took place.
     * @param ranges Ranges being copied in {@linkcode document}.
     * @param dataTransfer The data transfer associated with the copy. You can store additional values on this for
     * later use in {@linkcode provideDocumentPasteEdits}. This object is only valid for the duration of this method.
     * @param token A cancellation token.
     *
     * @return Optional thenable that resolves when all changes to the `dataTransfer` are complete.
     */
    prepareDocumentPaste?(document: TextDocument, ranges: readonly Range[], dataTransfer: DataTransfer, token: CancellationToken): void | Thenable<void>;

    /**
     * Invoked before the user pastes into a {@link TextEditor text editor}.
     *
     * Returned edits can replace the standard pasting behavior.
     *
     * @param document Document being pasted into
     * @param ranges Range in the {@linkcode document} to paste into.
     * @param dataTransfer The {@link DataTransfer data transfer} associated with the paste. This object is only
     * valid for the duration of the paste operation.
     * @param context Additional context for the paste.
     * @param token A cancellation token.
     *
     * @return Set of potential {@link DocumentPasteEdit edits} that can apply the paste. Only a single returned
     * {@linkcode DocumentPasteEdit} is applied at a time. If multiple edits are returned from all providers, then
     * the first is automatically applied and a widget is shown that lets the user switch to the other edits.
     */
    provideDocumentPasteEdits?(document: TextDocument, ranges: readonly Range[], dataTransfer: DataTransfer, context: vscode.DocumentPasteEditContext, token: CancellationToken): ProviderResult<T[]>;

    /**
     * Optional method which fills in the {@linkcode DocumentPasteEdit.additionalEdit} before the edit is applied.
     *
     * This is called once per edit and should be used if generating the complete edit may take a long time.
     * Resolve can only be used to change {@linkcode DocumentPasteEdit.insertText} or {@linkcode DocumentPasteEdit.additionalEdit}.
     *
     * @param pasteEdit The {@linkcode DocumentPasteEdit} to resolve.
     * @param token A cancellation token.
     *
     * @returns The resolved paste edit or a thenable that resolves to such. It is OK to return the given
     * `pasteEdit`. If no result is returned, the given `pasteEdit` is used.
     */
    resolveDocumentPasteEdit?(pasteEdit: T, token: CancellationToken): ProviderResult<T>;
  }

  export namespace languages {

    /**
     * Registers a new {@linkcode DocumentPasteEditProvider}.
     *
     * Multiple providers can be registered for a language. All registered providers for a language will be invoked
     * for copy and paste operations based on their handled mimetypes as specified by the {@linkcode DocumentPasteProviderMetadata}.
     *
     * For {@link DocumentPasteEditProvider.prepareDocumentPaste copy operations}, changes to the {@linkcode DataTransfer}
     * made by each provider will be merged into a single {@linkcode DataTransfer} that is used to populate the clipboard.
     *
     * For {@link DocumentPasteEditProvider.providerDocumentPasteEdits paste operations}, each provider will be invoked
     * and can return one or more {@linkcode DocumentPasteEdit DocumentPasteEdits}. The edits are sorted using
     * the {@linkcode DocumentPasteEdit.yieldTo} property. By default the first edit will be applied
     * and the rest of the edits will be shown to the user as selectable paste options in the paste widget.
     *
     * @param selector A selector that defines the documents this provider applies to.
     * @param provider A paste editor provider.
     * @param metadata Additional metadata about the provider.
     *
     * @returns A {@linkcode Disposable} that unregisters this provider when disposed of.
     */
    export const registerDocumentPasteEditProvider: ((selector: DocumentSelector, provider: DocumentPasteEditProvider, metadata: DocumentPasteProviderMetadata) => Disposable) | undefined;
  }
}
