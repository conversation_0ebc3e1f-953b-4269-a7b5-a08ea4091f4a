import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { LoggerManager } from "../../base/logger";
import { AgentModule } from "../agent";
import { ConfigManager, WorkspaceStateManager } from "../../base/state-manager";
import { Config, StateReturnType } from "../../base/state-manager/types";
import { Bridge } from "@bridge";
import { createExtensionRpcContext } from "../../base/bridge/ExtensionRpcContext";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { ExtensionSettingsShape } from "shared/lib/bridge/protocol";
import { SettingPage } from "shared/lib/customSettingPanel";
import { SettingWebview } from "../../base/webview/settings";

export class SettingPanelModule extends CoreModule implements ExtensionSettingsShape {
  private readonly loggerScope = "SettingPanelModule";
  private settingWebview: SettingWebview;
  rpcContext: IRPCProtocol;
  constructor(readonly ext: ContextManager) {
    super(ext);
    this.settingWebview = this.getBase(SettingWebview);
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.openBasicsManagement", () => {
        this.settingWebview.openCustomSettingPanel("basics");
      }),
      vscode.commands.registerCommand("kwaipilot.openFunctionManagement", () => {
        this.settingWebview.openCustomSettingPanel("function");
      }),
      vscode.commands.registerCommand("kwaipilot.openCodeIndexManagement", () => {
        this.settingWebview.openCustomSettingPanel("fileIndex");
      }),
      vscode.commands.registerCommand("kwaipilot.openRulesManagement", () => {
        this.settingWebview.openCustomSettingPanel("rules");
      }),
      vscode.commands.registerCommand("kwaipilot.openMCPManagement", (query) => {
        this.settingWebview.openCustomSettingPanel("mcp", query);
      }),
    );
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data, source) => {
            if (source !== this.settingWebview.panel?.webview) {
              return;
            }
            listener(data);
          });
        },
        send: (message) => {
          const webview = this.settingWebview.panel?.webview;
          if (!webview) {
            throw new Error("no webview");
          }
          this.getBase(Bridge).postOneWayMessage(webview, WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
      },
    });
  }

  $getSettings(): StateReturnType["config"] {
    return {
      proxy: this.config.get(Config.PROXY_URL),
      [Config
        .MODEL_TYPE]: this.config.get(Config.MODEL_TYPE),
      [Config.ENABLE]: this.config.get(Config.ENABLE),
      [Config.COMMENT_COMPLETION_ENABLE]: this.config.get(Config.COMMENT_COMPLETION_ENABLE),
      [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: this.config.get(Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION),
      [Config.CODE_COMPLETION_DELAY]: this.config.get(Config.CODE_COMPLETION_DELAY),
      [Config.ENABLE_CODE_BLOCK_ACTION]: this.config.get(Config.ENABLE_CODE_BLOCK_ACTION),
      [Config.PREDICTION_ENABLE]: this.config.get(Config.PREDICTION_ENABLE),
      [Config
        .ENABLE_LOCAL_AGENT]: this.config.get(Config.ENABLE_LOCAL_AGENT),
      [Config.AGENT_PREFERENCE]: this.config.get(Config.AGENT_PREFERENCE),
      [Config.ENABLE_DIAGNOSTICS_CHECK]: this.config.get(Config.ENABLE_DIAGNOSTICS_CHECK),
    };
  }

  $updateSetting(key: keyof StateReturnType["config"], value: StateReturnType["config"][typeof key]): void {
    this.config.update(key, value);
  }

  $openSettings(activePage?: SettingPage, query?: string) {
    this.settingWebview.openCustomSettingPanel(activePage, query);
  }

  $login(host?: "ide" | "plugin") {
    let cmd = "kwaipilot.login";
    if (host === "ide") {
      cmd = "kwaipilot.loginIDE";
    }
    return vscode.commands.executeCommand(cmd);
  }

  $logout(host?: "ide" | "plugin") {
    let cmd = "kwaipilot.logout";
    if (host === "ide") {
      cmd = "kwaipilot.logoutIDE";
    }
    return vscode.commands.executeCommand(cmd);
  }

  /**
   *【调用须知】 这个方法只有在 Kwaipilot IDE 里会提供，其他 IDE 不提供
   * @param productName
   */
  $importSettings(productName: string) {
    // 必须返回，因为需要 await 效果
    return vscode.commands.executeCommand("kwaipilot.importSettings", productName, false);
  }

  $openIdeShortcutSettings() {
    vscode.commands.executeCommand("workbench.action.openGlobalKeybindings");
  }

  $openIdeUserSettings() {
    vscode.commands.executeCommand("workbench.action.openSettings");
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get agent() {
    return this.getCore(AgentModule);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }
}
