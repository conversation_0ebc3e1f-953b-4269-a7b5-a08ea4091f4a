import { EventEmitter } from "events";
import { BaseModule } from "../base";
import { ContextManager } from "../base/context-manager";
import { Ctor } from "../base/context-manager/manager";
import { ServiceModule } from "../services";

type EventMap<T> = Record<keyof T, any[]> | DefaultEventMap;
    type DefaultEventMap = [never];

export class CoreModule<E extends EventMap<E> = DefaultEventMap> extends EventEmitter<E> {
  constructor(readonly ext: ContextManager) {
    super();
  }

  protected getBase<T extends BaseModule>(key: Ctor<T>) {
    return this.ext.getBase(key);
  }

  protected getCore<T extends CoreModule>(key: Ctor<T>) {
    return this.ext.getCore<T>(key);
  }

  // FIXME: 重构
  /**
     * 获取某个 service
     * 2025.3.20 @liupenghao03 主要为了解决监听获取 composer history，需要调用ComposerHistoryStorageService。
     * service 和 base 应当可以互相调用。
     * 为什么是 dangerously。 因为
     *
     * 1. 现在的通信架构下，只能注册一个监听器，如果把监听功能暴露在 service 中，会导致难以排查问题
     * 2. 现在的服务注册发现机制没有解决循环依赖的问题
     * @param key
     * @returns
     */
  protected dangerouslyGetService<T extends ServiceModule>(key: Ctor<T>) {
    return this.ext.getService<T>(key);
  }

  protected get context() {
    return this.ext.context;
  }
}
