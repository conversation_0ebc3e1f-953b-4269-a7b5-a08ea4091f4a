import * as vscode from "vscode";

const getDecorationColors = () => {
  const isDark = vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.Dark;
  return {
    fadedOverlay: isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)",
    activeLine: isDark ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.1)",
  };
};

const createDecorationTypes = () => {
  const colors = getDecorationColors();
  return {
    fadedOverlay: vscode.window.createTextEditorDecorationType({
      backgroundColor: colors.fadedOverlay,
      isWholeLine: true,
    }),
    activeLine: vscode.window.createTextEditorDecorationType({
      backgroundColor: colors.activeLine,
      opacity: "1",
      isWholeLine: true,
    }),
  };
};

let decorationTypes = createDecorationTypes();

// 监听主题变化
vscode.window.onDidChangeActiveColorTheme(() => {
  // 清除旧的装饰器类型
  Object.values(decorationTypes).forEach(type => type.dispose());
  // 创建新的装饰器类型
  decorationTypes = createDecorationTypes();
});

type DecorationType = "fadedOverlay" | "activeLine";

export class DecorationController {
  private decorationType: DecorationType;
  private editor: vscode.TextEditor;
  private ranges: vscode.Range[] = [];

  constructor(decorationType: DecorationType, editor: vscode.TextEditor) {
    this.decorationType = decorationType;
    this.editor = editor;
  }

  getDecoration() {
    switch (this.decorationType) {
      case "fadedOverlay":
        return decorationTypes.fadedOverlay;
      case "activeLine":
        return decorationTypes.activeLine;
    }
  }

  addLines(startIndex: number, numLines: number) {
    // Guard against invalid inputs
    if (startIndex < 0 || numLines <= 0) {
      return;
    }

    const lastRange = this.ranges[this.ranges.length - 1];
    if (lastRange && lastRange.end.line === startIndex - 1) {
      this.ranges[this.ranges.length - 1] = lastRange.with(undefined, lastRange.end.translate(numLines));
    }
    else {
      const endLine = startIndex + numLines - 1;
      this.ranges.push(new vscode.Range(startIndex, 0, endLine, Number.MAX_SAFE_INTEGER));
    }

    this.editor.setDecorations(this.getDecoration(), this.ranges);
  }

  clear() {
    this.ranges = [];
    this.editor.setDecorations(this.getDecoration(), this.ranges);
  }

  updateOverlayAfterLine(line: number, totalLines: number) {
    // Remove any existing ranges that start at or after the current line
    this.ranges = this.ranges.filter(range => range.end.line < line);

    // Add a new range for all lines after the current line
    if (line < totalLines - 1) {
      this.ranges.push(
        new vscode.Range(new vscode.Position(line + 1, 0), new vscode.Position(totalLines - 1, Number.MAX_SAFE_INTEGER)),
      );
    }

    // Apply the updated decorations
    this.editor.setDecorations(this.getDecoration(), this.ranges);
  }

  setActiveLine(line: number) {
    this.ranges = [new vscode.Range(line, 0, line, Number.MAX_SAFE_INTEGER)];
    this.editor.setDecorations(this.getDecoration(), this.ranges);
  }
}
