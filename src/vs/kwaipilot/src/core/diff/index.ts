import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { LoggerManager } from "../../base/logger";

export class DiffModule extends CoreModule {
  private loggerScope = "DiffModule";
  constructor(ext: ContextManager) {
    super(ext);
  }

  private lineTrimmedFallbackMatch(originalContent: string, searchContent: string, startIndex: number): [number, number] | false {
    // Split both contents into lines
    const originalLines = originalContent.split("\n");
    const searchLines = searchContent.split("\n");

    // Trim trailing empty line if exists (from the trailing \n in searchContent)
    if (searchLines[searchLines.length - 1] === "") {
      searchLines.pop();
    }

    // Find the line number where startIndex falls
    let startLineNum = 0;
    let currentIndex = 0;
    while (currentIndex < startIndex && startLineNum < originalLines.length) {
      currentIndex += originalLines[startLineNum].length + 1; // +1 for \n
      startLineNum++;
    }

    // For each possible starting position in original content
    for (let i = startLineNum; i <= originalLines.length - searchLines.length; i++) {
      let matches = true;

      // Try to match all search lines from this position
      for (let j = 0; j < searchLines.length; j++) {
        const originalTrimmed = originalLines[i + j].trim();
        const searchTrimmed = searchLines[j].trim();

        if (originalTrimmed !== searchTrimmed) {
          matches = false;
          break;
        }
      }

      // If we found a match, calculate the exact character positions
      if (matches) {
        // Find start character index
        let matchStartIndex = 0;
        for (let k = 0; k < i; k++) {
          matchStartIndex += originalLines[k].length + 1; // +1 for \n
        }

        // Find end character index
        let matchEndIndex = matchStartIndex;
        for (let k = 0; k < searchLines.length; k++) {
          matchEndIndex += originalLines[i + k].length + 1; // +1 for \n
        }

        return [matchStartIndex, matchEndIndex];
      }
    }

    return false;
  }

  private blockAnchorFallbackMatch(originalContent: string, searchContent: string, startIndex: number): [number, number] | false {
    const originalLines = originalContent.split("\n");
    const searchLines = searchContent.split("\n");

    // Only use this approach for blocks of 3+ lines
    if (searchLines.length < 3) {
      return false;
    }

    // Trim trailing empty line if exists
    if (searchLines[searchLines.length - 1] === "") {
      searchLines.pop();
    }

    const firstLineSearch = searchLines[0].trim();
    const lastLineSearch = searchLines[searchLines.length - 1].trim();
    const searchBlockSize = searchLines.length;

    // Find the line number where startIndex falls
    let startLineNum = 0;
    let currentIndex = 0;
    while (currentIndex < startIndex && startLineNum < originalLines.length) {
      currentIndex += originalLines[startLineNum].length + 1;
      startLineNum++;
    }

    // Look for matching start and end anchors
    for (let i = startLineNum; i <= originalLines.length - searchBlockSize; i++) {
      // Check if first line matches
      if (originalLines[i].trim() !== firstLineSearch) {
        continue;
      }

      // Check if last line matches at the expected position
      if (originalLines[i + searchBlockSize - 1].trim() !== lastLineSearch) {
        continue;
      }

      // Calculate exact character positions
      let matchStartIndex = 0;
      for (let k = 0; k < i; k++) {
        matchStartIndex += originalLines[k].length + 1;
      }

      let matchEndIndex = matchStartIndex;
      for (let k = 0; k < searchBlockSize; k++) {
        matchEndIndex += originalLines[i + k].length + 1;
      }

      return [matchStartIndex, matchEndIndex];
    }

    return false;
  }

  async constructNewFileContent(diffContent: string, originalContent: string, isFinal: boolean): Promise<string> {
    let result = "";
    let lastProcessedIndex = 0;

    let currentSearchContent = "";
    let inSearch = false;
    let inReplace = false;

    let searchMatchIndex = -1;
    let searchEndIndex = -1;

    const lines = diffContent.split("\n");

    // If the last line looks like a partial marker but isn't recognized,
    // remove it because it might be incomplete.
    const lastLine = lines[lines.length - 1];
    if (
      lines.length > 0
      && (lastLine.startsWith("<") || lastLine.startsWith("=") || lastLine.startsWith(">"))
      && lastLine !== "<<<<<<< SEARCH"
      && lastLine !== "======="
      && lastLine !== ">>>>>>> REPLACE"
    ) {
      lines.pop();
    }

    for (const line of lines) {
      if (line === "<<<<<<< SEARCH") {
        inSearch = true;
        currentSearchContent = "";
        continue;
      }

      if (line === "=======") {
        inSearch = false;
        inReplace = true;

        if (!currentSearchContent) {
          // Empty search block
          if (originalContent.length === 0) {
            // New file scenario: nothing to match, just start inserting
            searchMatchIndex = 0;
            searchEndIndex = 0;
          }
          else {
            // Complete file replacement scenario: treat the entire file as matched
            searchMatchIndex = 0;
            searchEndIndex = originalContent.length;
          }
        }
        else {
          // Exact search match scenario
          const exactIndex = originalContent.indexOf(currentSearchContent, lastProcessedIndex);
          if (exactIndex !== -1) {
            searchMatchIndex = exactIndex;
            searchEndIndex = exactIndex + currentSearchContent.length;
          }
          else {
            // Attempt fallback line-trimmed matching
            const lineMatch = this.lineTrimmedFallbackMatch(originalContent, currentSearchContent, lastProcessedIndex);
            if (lineMatch) {
              ;[searchMatchIndex, searchEndIndex] = lineMatch;
            }
            else {
              // Try block anchor fallback for larger blocks
              const blockMatch = this.blockAnchorFallbackMatch(originalContent, currentSearchContent, lastProcessedIndex);
              if (blockMatch) {
                ;[searchMatchIndex, searchEndIndex] = blockMatch;
              }
              else {
                // TODO: 这里需要优化，如果 search 内容不匹配，应该让用户知道
                throw new Error(
                  `The SEARCH block:\n${currentSearchContent.trimEnd()}\n...does not match anything in the file.`,
                );
              }
            }
          }
        }

        // Output everything up to the match location
        result += originalContent.slice(lastProcessedIndex, searchMatchIndex);
        continue;
      }

      if (line === ">>>>>>> REPLACE") {
        // Finished one replace block

        // Advance lastProcessedIndex to after the matched section
        lastProcessedIndex = searchEndIndex;

        // Reset for next block
        inSearch = false;
        inReplace = false;
        currentSearchContent = "";
        searchMatchIndex = -1;
        searchEndIndex = -1;
        continue;
      }

      // Accumulate content for search or replace
      if (inSearch) {
        currentSearchContent += line + "\n";
      }
      else if (inReplace && searchMatchIndex !== -1) {
        // Output replacement lines immediately if we know the insertion point
        result += line + "\n";
      }
    }

    // If this is the final chunk, append any remaining original content
    if (isFinal && lastProcessedIndex < originalContent.length) {
      result += originalContent.slice(lastProcessedIndex);
    }

    return result;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
