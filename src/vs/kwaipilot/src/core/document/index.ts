import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import path from "path";
import CommonDocument from "../../utils/recentDocument";

export class DocumentManager extends CoreModule {
  private recentlyOpenedDoc: CommonDocument[] = [];
  constructor(ext: ContextManager) {
    super(ext);
    this.init();
    this.registryListener();
  }

  /**
   * 获取最近在窗口打开的文件
   * @param num 最近打开的文件个数
   * @returns 最近打开的文件列表
   */
  public getRecentlyDocs(num: number = 3) {
    return this.recentlyOpenedDoc.slice(-num);
  }

  /**
   * 获取在编辑区打开的文档
   */
  public getOpenedDocument() {
    const openedDocuments = vscode.workspace.textDocuments;
    return openedDocuments;
  }

  /**
   * 获取当前活跃文档
   */
  public getActiveEditor() {
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      return activeEditor;
    }
    return null;
  }

  private registryListener() {
    this.context.subscriptions.push(
      vscode.window.onDidChangeActiveTextEditor((editor) => {
        if (editor) {
          this.addRecentlyOpened(
            editor.document,
            editor?.selection?.active?.character,
          );
        }
      }),
    );
  }

  private init() {
    this.getOpenedDocument().forEach((document) => {
      this.addRecentlyOpened(document, undefined);
    });
    const activeEditor = this.getActiveEditor();
    if (activeEditor) {
      this.addRecentlyOpened(
        activeEditor.document,
        activeEditor.selection.active.character,
      );
    }

    // const openedDocuments = vscode.workspace.textDocuments;
    // for (const document of openedDocuments) {
    //   const textEditor = vscode.window.visibleTextEditors.find((editor) => {
    //     return editor.document.uri.fsPath === document.uri.fsPath;
    //   });

    //   // 获取TextEditor对象的selection属性，即最后的光标位置
    //   const selection = textEditor?.selection;

    //   this.addRecentlyOpened(document, selection?.active?.character);
    // }
  }

  /**
   * @todo 修改为lru的模式
   * @param document
   * @param offset
   * @returns
   */
  private addRecentlyOpened(
    document: vscode.TextDocument,
    offset: number | undefined,
  ) {
    if (!this.isValidDocument(document)) {
      return;
    }
    for (let i = 0; i < this.recentlyOpenedDoc.length; i++) {
      if (this.recentlyOpenedDoc[i].fileName == document.fileName) {
        this.recentlyOpenedDoc.splice(i, 1);
        break;
      }
    }
    // 只保留最新打开的三个文档
    if (this.recentlyOpenedDoc.length >= 100) {
      // 删除最旧的元素
      this.recentlyOpenedDoc.shift();
    }
    // 添加最新的元素 - 需保证语言类型一致
    this.recentlyOpenedDoc.push(
      new CommonDocument(
        document.fileName,
        document.languageId,
        document.getText(),
        offset ? offset : 0,
      ),
    );
  }

  private isValidDocument(document: vscode.TextDocument): boolean {
    const extName = path.extname(document.uri.fsPath);
    if (extName == ".git") {
      // 针对git做特殊处理
      return false;
    }
    const supportedLanguages = [
      "sql",
      "xml",
      "yaml",
      "scss",
      "rust",
      "ruby",
      "php",
      "cpp",
      "go",
      "c",
      "csharp",
      "javascript",
      "typescript",
      "python",
      "java",
      "typescriptreact",
      "shellscript",
      "dockerfile",
      "markdown",
      "less",
      "css",
      "html",
    ]; // 支持的语言列表
    let isSupport = supportedLanguages.includes(
      document.languageId.toLowerCase(),
    );
    if (!isSupport) {
      const supportedExtNames = [
        ".sql",
        ".xml",
        ".yaml",
        ".scss",
        ".rust",
        ".ruby",
        ".php",
        ".cpp",
        ".go",
        ".c",
        ".js",
        ".ts",
        ".py",
        ".java",
        ".tsx",
        ".sh",
        ".dockerfile",
        ".md",
        ".less",
        ".css",
        ".html",
      ]; // 支持的文件后缀
      isSupport = supportedExtNames.includes(extName);
    }
    return isSupport;
  }
}
