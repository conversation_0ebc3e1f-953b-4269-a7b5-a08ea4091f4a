// fix https://team.corp.kuaishou.com/task/********
export class BufferDataHandle {
  private _unfinishedLine = Buffer.alloc(0);
  // 100MB 限制
  private static readonly MAX_BUFFER_SIZE = 1024 * 1024 * 100;

  handleData(data: Buffer, onLine: (line: string) => void) {
    // 安全检查：确保输入数据有效
    if (!Buffer.isBuffer(data)) {
      return;
    }

    if (data.length === 0) {
      return;
    }

    // 检查缓冲区大小限制
    if (this._unfinishedLine.length + data.length > BufferDataHandle.MAX_BUFFER_SIZE) {
      console.error("Buffer size exceeds limit, clearing unfinished line");
      this._unfinishedLine = Buffer.alloc(0);
      return;
    }

    const chunks: Buffer[] = [];
    let start = 0;

    // 处理未完成的行
    if (this._unfinishedLine.length > 0) {
      data = Buffer.concat([this._unfinishedLine, data]);
      this._unfinishedLine = Buffer.alloc(0);
    }

    // 查找所有的 \r\n = [13, 10]，处理所有的完整行
    for (let i = 0; i < data.length - 1; i++) {
      if (data[i] === 13 && data[i + 1] === 10) {
        const chunk = data.subarray(start, i);
        chunks.push(chunk);
        start = i + 2;
        i++;
      }
    }

    // 处理剩余的数据（不完整的行）
    if (start < data.length) {
      // 检查最后是否以 \r\n 结尾
      if (data.length >= 2 && data[data.length - 2] === 13 && data[data.length - 1] === 10) {
        // 如果以 \r\n 结尾，且还有数据，将其作为完整的行
        const remainingChunk = data.subarray(start, data.length - 2);
        if (remainingChunk.length > 0) {
          chunks.push(remainingChunk);
        }
      }
      else {
        // 如果不以 \r\n 结尾，保存为未完成的行
        // 使用 Buffer.from 确保数据复制
        this._unfinishedLine = Buffer.from(data.subarray(start));
      }
    }

    // 处理所有完整的行
    for (const chunk of chunks) {
      // 验证是否为有效的 UTF-8
      if (this.isValidUtf8(chunk)) {
        const line = chunk.toString("utf8");
        if (line.length > 0) {
          onLine(line);
        }
      }
      else {
        console.error("Invalid UTF-8 sequence detected, skipping chunk", chunk);
      }
    }
  }

  // 验证 Buffer 是否包含有效的 UTF-8 序列
  private isValidUtf8(buffer: Buffer): boolean {
    try {
      // 尝试解码
      const decoder = new TextDecoder("utf-8", { fatal: true });
      decoder.decode(buffer);
      return true;
    }
    catch {
      return false;
    }
  }

  // 添加一个方法来获取未完成的数据，用于测试和调试
  getUnfinishedLine(): Buffer {
    return Buffer.from(this._unfinishedLine);
  }

  // 添加一个方法来清理未完成的数据，用于重置状态
  clear(): void {
    this._unfinishedLine = Buffer.alloc(0);
  }
}
