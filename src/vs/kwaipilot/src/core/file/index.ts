import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { Webview } from "@webview";
import fs from "fs";
import { upload, UploadFile } from "../../api/upload";
import { LoggerManager } from "../../base/logger";
import { Bridge } from "@bridge";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { ConfigManager } from "../../base/state-manager";
import { Config } from "shared/lib/state-manager/types";
import { DefaultBaseUrl } from "../../const";

let fileSize = 0;
/**
 * @todo 文件系统的边界划分
 */
export class File extends CoreModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.registryListener();
  }

  registryListener() {
    this.getBase(Bridge).registerHandler(NATIVE_BRIDGE_EVENT_NAME.UPLOAD_FILE, async () => {
      return await this.openFileDialog();
    });
  }

  public async openFileDialog() {
    const files = await vscode.window
      .showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: true,
        filters: {
          Code: [
            "pdf", "docx", "txt", "doc",
            "js", "ts", "jsx", "tsx", // JavaScript/TypeScript
            "html", "css", "scss", "less", "sass", // Web
            "py", // Python
            "java", // Java
            "c", "cpp", // C/C++
            "cs", // C#
            "rb", // Ruby
            "go", // Go
            "rs", // Rust
            "swift", // Swift
            "php", // PHP
            "kt", // Kotlin
            "json", "xml", "yml", "yaml", // Data formats
            "sh", // Shell scripts
            "asm", // Assembly
            "bat", "cmd", // Batch
            "lisp", "el", "scm", "ss", "rkt", // Lisp
            "m", // MATLAB or Objective-C
            "pas", // Pascal
            "pl", "pm", "t", // Perl
            "r", "rdata", "rds", // R
            "scala", // Scala
            "sql", // SQL
            "tcl", // Tcl
            "vhd", "vhdl", // VHDL
            "v", "sv", // Verilog
            "Dockerfile", // Docker
            "Makefile", "makefile", "mk", // Makefile
            "lua", // Lua
            "hs", // Haskell
            "erl", "hrl", // Erlang
            "ex", "exs", // Elixir
            "rkt", // Racket
            "sass", // Sass
            "less", // Less
            "clj", "cljs", "cljc", // Clojure
            "coffee", // CoffeeScript
            "cr", // Crystal
            "dart", // Dart
            "fs", "fsi", "fsx", // F#
            "f", "f90", "f95", // Fortran
            "groovy", "gvy", "gy", "gsh", // Groovy
            "hx", // Haxe
            "jl", // Julia
            "ml", "mli", // OCaml
            "sol", // Solidity
            "vim", // Vim script
          ],
        },
      });
    if (!files) return;
    return {
      fileInfo: (await this.uploadFiles(files)) || [],
    };
  }

  public async uploadFiles(files: vscode.Uri[]): Promise<UploadFile[] | undefined> {
    let addFileSize = 0;
    const res = [];

    for (let i = 0; i < files.length; i++) {
      await new Promise((resolve) => {
        fs.stat(files[i].path, (err, stats) => {
          if (err) {
            console.error(err);
            return;
          }
          addFileSize = addFileSize + stats.size;
          resolve(addFileSize);
        });
      });
    }
    const totalSize = fileSize + addFileSize;
    // 最大 3MB
    if (totalSize > 3145728) {
      this.logger.warn("file total size", "file", {
        value: totalSize,
      });
      vscode.window.showErrorMessage("上传文件总大小超过 3MB 上限，请调整后重试");
      return;
    }
    fileSize = fileSize + addFileSize;
    for (let i = 0; i < files.length; i++) {
      const f = await new Promise<UploadFile>((resolve) => {
        upload(this.getBase(ConfigManager).get(Config.PROXY_URL) || DefaultBaseUrl, {
          file: files[i],
          onStart: () => {
          },
          onProgress: () => {
          },
          onSuccess: (data) => {
            resolve(data);
          },
          onFailed: (file) => {
            console.error(file);
          },
        });
      });
      res.push(f);
    }
    return res;
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
