import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { parseVue3 } from "./parser/vue3";
import { parseTS } from "./parser/typescript";
import { CodeSection } from "../../shared/types";

export class Ast extends CoreModule {
  private codeSelection: CodeSection[] = [];
  private activeFile: string = "";
  constructor(ext: ContextManager) {
    super(ext);

    // 当打开文件时、文本内容改变时，更新ast
    this.ext.context.subscriptions.push(
      vscode.window.onDidChangeActiveTextEditor(async (e) => {
        e && this.setActiveAst(e.document);
      }),
      vscode.workspace.onDidChangeTextDocument(async (e) => {
        this.setActiveAst(e.document);
      }),
    );
  }

  getAstByActiveTextEditor() {
    if (vscode.window.activeTextEditor) {
      const { document } = vscode.window.activeTextEditor;
      if (document.uri.fsPath === this.activeFile) {
        return this.codeSelection;
      }
      else {
        return this.setActiveAst(document);
      }
    }
    return [];
  }

  getAstByDocument(document: vscode.TextDocument) {
    if (document.uri.fsPath === this.activeFile) {
      return this.codeSelection;
    }
    return this.formatAstByDocument(document);
  }

  private setActiveAst(document: vscode.TextDocument) {
    try {
      this.activeFile = document.uri.fsPath;
      const ast = this.formatAstByDocument(document);
      this.codeSelection = ast;
    }
    catch (error) {
      this.codeSelection = [];
    }
    return this.codeSelection;
  }

  private formatAstByDocument(document: vscode.TextDocument) {
    const text = document.getText();
    const filePath = document.uri.fsPath;
    const language = document.languageId || "";
    return this.formatAstByText(text, language, filePath);
  }

  /**
   * 根据语言格式化代码，返回统一的抽象语法树
   * @todo 修改为tree-sitter
   */
  private formatAstByText(text: string, language: string, filePath: string) {
    const supportLanguage = [
      "javascript",
      "typescript",
      "javascriptreact",
      "typescriptreact",
      "vue",
    ];
    if (!supportLanguage.includes(language)) {
      return [];
    }
    const { sectionList }
      = language === "vue" ? parseVue3(text, filePath) : parseTS(text, filePath);
    return sectionList;
  }
}
