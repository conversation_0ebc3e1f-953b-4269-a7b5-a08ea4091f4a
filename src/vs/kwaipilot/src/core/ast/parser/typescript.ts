import * as ts from "typescript";
import { CodeSection } from "../../../shared/types";

type FuncNode =
  | ts.FunctionDeclaration
  | ts.FunctionExpression
  | ts.ArrowFunction
  | ts.MethodDeclaration;
type ClassNode = ts.ClassDeclaration | ts.ClassExpression;

function getNameByFuncOrClass(node: FuncNode | ClassNode) {
  // 获取箭头函数名
  // @ts-expect-error 箭头函数没有 name 属性
  return node.name?.getText() || node.parent?.name?.getText() || "anonymous";
}
function parse(sourceCode: string, filepath: string) {
  let kind = ts.ScriptKind.TS;
  if (filepath.endsWith(".js")) {
    kind = ts.ScriptKind.JS;
  }
  else if (filepath.endsWith(".tsx")) {
    kind = ts.ScriptKind.TSX;
  }
  else if (filepath.endsWith(".jsx")) {
    kind = ts.ScriptKind.JSX;
  }

  // 创建ast根结点
  const rootNode = ts.createSourceFile(
    "index.ts",
    sourceCode,
    ts.ScriptTarget.ES2015,
    /* setParentNodes */ true,
    kind,
  );
  const importedFiles: string[] = [];
  const sectionList: Array<CodeSection> = [];

  function traverse(
    rootNode: ts.Node,
    sourceFile: ts.SourceFile,
    prefix: string[] = [],
  ) {
    ts.forEachChild(rootNode, (node) => {
      // import xxx from 'xxx'
      if (ts.isImportDeclaration(node)) {
        const importPath = node.moduleSpecifier
          .getText()
          .replace(/^["']/, "")
          .replace(/["']$/, "");
        importedFiles.push(importPath);
        return;
      }
      let name = "";
      if (
        ts.isFunctionDeclaration(node)
        || ts.isFunctionExpression(node)
        || ts.isArrowFunction(node)
        || ts.isMethodDeclaration(node)
      ) {
        name = getNameByFuncOrClass(node);
        addSection(node, sourceFile, "function", prefix);
      }

      if (ts.isClassDeclaration(node) || ts.isClassExpression(node)) {
        name = getNameByFuncOrClass(node);
        addSection(node, sourceFile, "class", prefix);
      }
      name && prefix.push(name);
      traverse(node, sourceFile, prefix);
      name && prefix.pop();
    });
  }

  function addSection(
    node: FuncNode | ClassNode,
    sourceFile: ts.SourceFile,
    type: "function" | "class",
    prefix: string[] = [],
  ) {
    const {
      line: startLine,
      character: offsetStart,
    } = sourceFile.getLineAndCharacterOfPosition(node.getStart());
    const {
      line: endLine,
      character: offsetEnd,
    } = sourceFile.getLineAndCharacterOfPosition(node.getEnd());

    const kind = ts.SyntaxKind[node.kind];
    const name = getNameByFuncOrClass(node);

    sectionList.push({
      type, // 使用传入的类型
      name: [...prefix, name].join(" > "),
      kind,
      offsetStart,
      offsetEnd,
      lineStart: startLine + 1,
      lineEnd: endLine + 1,
      content: node.getText(),
      blockStart: 0, // This may need further logic to be meaningful
      blockEnd: 0, // This may need further logic to be meaningful
    });
  }
  traverse(rootNode, rootNode);

  return {
    importedFiles,
    sectionList,
  };
}

export function parseTS(sourceCode: string, filepath: string) {
  try {
    return parse(sourceCode, filepath);
  }
  catch (e) {
    console.error(e);
    return {
      importedFiles: [],
      sectionList: [],
    };
  }
}
