import { parse, SFCScriptBlock } from "@vue/compiler-sfc";
import { parseTS } from "./typescript";
import { CodeSection } from "../../../shared/types";

export function parseVue3(sourceCode: string, filepath: string) {
  const { descriptor } = parse(sourceCode);
  const { script, scriptSetup } = descriptor;

  const sectionList: CodeSection[] = [];
  const importedFiles: string[] = [];

  function parseScript(scriptBlock: SFCScriptBlock) {
    const { start } = scriptBlock.loc;
    const res = parseTS(scriptBlock.content, filepath);
    res.sectionList.forEach((s) => {
      s.lineStart += start.line - 1;
      s.lineEnd += start.line - 1;
      // vue 里 script 开始是一个换行符，所以多偏移一个
      s.offsetStart += start.offset;
      s.offsetEnd += start.offset;
      s.blockStart += start.line - 1;
      s.blockEnd += start.line - 1;
    });
    sectionList.push(...res.sectionList);
    importedFiles.push(...res.importedFiles);
  }

  if (scriptSetup) {
    parseScript(scriptSetup);
  }
  if (script) {
    parseScript(script);
  }

  return {
    // vue 里不需要考虑 hoc
    sectionList: sectionList.filter(s => s.type !== "hoc"),
    importedFiles,
  };
}
