export enum PageEnum {
  QUESTION_ANSWERING, // ("知识问答")
  EDITOR// ("编辑器")
  , FEEDBACK // ("反馈")
  , PR_COMMIT // ("PR Commit")
  , CODE_HELP // ("代码助手")
  , CODE_COMPLETION, // ("代码补全");
}

export enum ElementEnum {
  QA_SEND // QA发送消息
  , CODE_HELP_SEND // 代码助手发送消息
  , FEEDBACK_BUTTON// 反馈按钮
  , COMPLETION_TRIGGER // 补全请求
  , COMPLETION_APPLY // 补全应用
  , COMPLETION_SHOW // 补全展示
  , ANSWER_IS_LIKE // 不喜欢
  , CODE_COMPLETION_SEND, // 代码续写发送
}

export const TaskEventType = {
  UNKNOWN_TYPE: 0,
  USER_OPERATION: 1,
  STAY_LENGTH_STAT_EVENT: 2,
  BACKGROUND_TASK_EVENT: 3,
};

export const OperationType = {
  UNKNOWN_OPERATION: 0, // 未知操作
  CLICK: 1, // 点击
  DOUBLE_CLICK: 2, // 双击
  TRIPLE_CLICK: 3, // 三击
  LONG_PRESS: 4, // 长按
  PULL: 5, // 滑动
  DRAG: 6, // 拖拽
  SCALE: 7, // 双指缩放
  PULL_DOWN: 8, // 下拉刷新
  PULL_UP: 9, // 上拉加载更多
};

export const ElementType = {
  UNKNOWN_ELEMENT_TYPE: 0, // 未知
  BUTTON: 1, // 按钮
  LABEL: 2, // 标签
  ICON: 3, // 图标, 暂时废弃不用!
  IMAGE: 4, // 图片, 例: 作品封面
  MENU: 5, // 菜单条目
  LINK: 6, // 链接
  TAB: 7, // Tab
  INPUT_BOX: 8, // 输入框
  OPTION_BOX: 9, // 选择框
  NOTIFICATION: 10, // 通知栏
  CARD: 11, // 卡片, 暂时废弃不用!
  VIEW: 12, // 视图
  PAGE: 13, // 页面
  VIDEO: 14, // 视频
  CELL: 15, // 列表中一个元素
  BANNER: 16, // Banner
  BUBBLE: 17, // 引导气泡
  POPUP_WINDOW: 18, // 浮层
};
