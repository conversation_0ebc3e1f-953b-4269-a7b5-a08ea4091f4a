import { getWorkspace } from "./vscode";
import * as child_process from "child_process";
import * as path from "path";
/**
 *
 * @returns 获取被Git管理的目录 应用Git标准忽略
 */
export function getGitManageDir(): string[] {
  const workspace = getWorkspace();
  if (!workspace) return [];
  try {
    const list = child_process.execSync(`git -C ${workspace} ls-tree -d -r --name-only HEAD`).toString().trim().split("\n");

    return list;
  }
  catch (error) {
    return [];
  }
}

/**
 *
 * @returns 获取未被Git管理的文件 应用Git标准忽略
 */
export function getGitOthersStandardFiles(): string[] {
  const workspace = getWorkspace();
  if (!workspace) return [];
  try {
    const list = child_process.execSync(`git -C ${workspace} ls-files --others --exclude-standard`).toString().trim().split("\n");
    return list;
  }
  catch (error) {
    return [];
  }
}

/**
 *
 * @returns 获取未被Git管理的目录 应用Git标准忽略
 */
export function getGitOthersStandardFilesDir(): string[] {
  const files = getGitOthersStandardFiles();
  return Array.from(new Set(files.map(file => path.dirname(file))));
}

/**
 *
 * @returns 返回是不是一个快手的Git仓库
 */
export function getGitRepo(): boolean {
  const workspace = getWorkspace();
  if (!workspace) return false;
  const remoteUrl = child_process.execSync(`git -C ${workspace} remote get-url origin`)
    .toString().trim();
  return remoteUrl?.startsWith("*************************") || remoteUrl?.startsWith("https://git.corp.kuaishou.com") || false;
}
