export type Deferred<T = void> = {
  id?: unknown;
  promise: Promise<T>;
  resolve: (d: T) => void;
  reject: (d: any) => void;
};

export function createDeferred<T = void>(): Deferred<T> {
  let resolve: ((d: T) => void) | null = null;
  let reject: ((d: unknown) => void) | null = null;
  const promise = new Promise<T>((r, j) => {
    resolve = r;
    reject = j;
  });

  return {
    resolve: resolve!,
    reject: reject!,
    promise,
  };
}
