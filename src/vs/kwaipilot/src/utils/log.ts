import { window } from "vscode";

export const OutputChannel = window.createOutputChannel("kwaipilot", {
  log: true,
});

export class Logger {
  private readonly category: string;

  constructor(category: string) {
    this.category = category;
  }

  info(msg: string) {
    OutputChannel.appendLine(`${this.category} ${msg}`);
  }

  warn(msg: string) {
    OutputChannel.appendLine(`${this.category} ${msg}`);
  }

  error(msg: string) {
    OutputChannel.appendLine(`${this.category} ${msg}`);
  }
}
