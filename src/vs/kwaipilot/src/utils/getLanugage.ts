import * as vscode from "vscode";

export function getLanguage(fileUri: vscode.Uri): string {
  const ext = fileUri.path.split(".").pop();

  switch (ext) {
    case "js":
      return "javascript";
    case "ts":
      return "typescript";
    case "py":
      return "python";
    case "java":
      return "java";
    case "cpp":
      return "cpp";
    case "c":
      return "c";
    case "cs":
      return "csharp";
    case "go":
      return "go";
    case "php":
      return "php";
    case "rb":
      return "ruby";
    case "swift":
      return "swift";
    case "kt":
      return "kotlin";
    default:
      return "plaintext";
  }
}
