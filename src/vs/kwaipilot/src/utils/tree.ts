import { promises as fs } from "fs";
import * as path from "path";
import * as vscode from "vscode";
/**
 *
 * @param dirPath 获取某个目录下所有文件 排除必须忽略的目录
 * @returns
 */
export async function getAllFilesNonRecursive(
  dirPath: string,
  WORKSPACE_FILE_EXCLUDE_DIR: string[] = [],
  dept?: number,
) {
  const pathSegments = dirPath.split(path.sep);

  if (WORKSPACE_FILE_EXCLUDE_DIR.some(excludeDirName => pathSegments.includes(excludeDirName))) {
    return [];
  }
  const stack = [dirPath];
  const arrayOfFiles = [];

  while (stack.length > 0) {
    if (dept !== undefined && arrayOfFiles.length >= dept) {
      return arrayOfFiles;
    }

    const currentPath = stack.pop()!;
    try {
      const dirents = await fs.readdir(currentPath, { withFileTypes: true });
      for (const dirent of dirents) {
        const fullPath = path.join(currentPath, dirent.name);

        if (dirent.isDirectory()) {
          const pathSegments = fullPath.split(path.sep);
          if (WORKSPACE_FILE_EXCLUDE_DIR.some(excludeDirName => pathSegments.includes(excludeDirName))) {
            continue;
          }
          stack.push(fullPath);
        }
        else {
          if (dirent.name === ".DS_Store") {
            continue;
          }
          arrayOfFiles.push(vscode.workspace.asRelativePath(fullPath));
        }
      }
    }
    catch (error) {
      console.error(`Error reading directory ${currentPath}:`, error);
    }
  }

  return arrayOfFiles;
}
