export enum SseMessageEnum {
  HEART_BEAT = "HEART_BEAT",
  CODE_CHAT = "CodeChat",
  GPT = "gpt",
  KWAI_CHAT = "kwaiChat",
  CHAT_GPT = "ChatGPT",
  AUTO_IDENTIFY = "intelligentChat",
  MESSAGE_DONE = "MESSAGE_DONE",
  ERROR = "ERROR",
}

export enum SsePostTypeEnum {
  RECEIVE_MESSAGE = "RECEIVE_MESSAGE",
  GENERATE_COMMENT_MESSAGE = "GENERATE_COMMENT_MESSAGE",
  GENERATE_UNIT_TEST_MESSAGE = "GENERATE_UNIT_TEST_MESSAGE",
  RECEIVE_MESSAGE_DONE = "RECEIVE_MESSAGE_DONE",
}
