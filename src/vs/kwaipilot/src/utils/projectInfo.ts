import * as vscode from "vscode";
import { execSync } from "child_process";

export class ProjectInfo {
  name: string | undefined; // 项目名称，实际上是项目文件夹名称
  rootDir: string; // 工作区根路径
  gitRepo: boolean;
  gitRemote: string;
  currentBranchName: string;
  username: string;
  userEmail: string;

  constructor(name: string, rootDir: string, gitRepo = false, gitRemote: string, currentBranchName: string, username: string, userEmail: string) {
    this.name = name;
    this.rootDir = rootDir;
    this.gitRepo = gitRepo;
    this.gitRemote = gitRemote;
    this.currentBranchName = currentBranchName;
    this.username = username;
    this.userEmail = userEmail;
  }
}

export class ProjectGitInfo {
  gitRepo: boolean;
  gitRemote: string;
  currentBranchName: string;
  username: string;
  userEmail: string;

  constructor(gitRepo = false, gitRemote: string, currentBranchName: string, username: string, userEmail: string) {
    this.gitRepo = gitRepo;
    this.gitRemote = gitRemote;
    this.currentBranchName = currentBranchName;
    this.username = username;
    this.userEmail = userEmail;
  }
}
const curProjectInfos = getProjectInfo();
export default curProjectInfos;

export function getProjectInfo(): ProjectInfo[] | undefined {
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (workspaceFolders) {
    const results: ProjectInfo[] = [];
    workspaceFolders.forEach((folder) => {
      const gitInfo = getProjectGitInfo(folder.uri.fsPath);
      results.push(new ProjectInfo(folder.name, folder.uri.fsPath, gitInfo.gitRepo, gitInfo.gitRemote, gitInfo.currentBranchName, gitInfo.username, gitInfo.userEmail));
    });
    return results;
  }
  return undefined;
}
export function getProjectGitInfo(path: string | undefined) {
  try {
    if (!path) {
      return new ProjectGitInfo(false, "", "", "", "");
    }
    const gitRemote = execSync(`cd ${path} && git remote -v | awk '{print $2}' | sort | uniq`).toString().trim();
    const currentBranchName = execSync(`cd ${path} && git rev-parse --abbrev-ref HEAD`).toString().trim();
    const username = execSync(`cd ${path} && git config user.name`).toString().trim();
    const userEmail = execSync(`cd ${path} && git config user.email`).toString().trim();
    return new ProjectGitInfo(true, gitRemote, currentBranchName, username, userEmail);
  }
  catch (err: any) {
    // console.log(err);
    return new ProjectGitInfo(false, "", "", "", "");
  }
}
