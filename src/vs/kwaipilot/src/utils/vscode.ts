import * as vscode from "vscode";
import { getGitManageDir, getGitOthersStandardFilesDir } from "./git";
import * as path from "path";
import { GIT_HARD_IGNORE_DID, WORKSPACE_FILE_EXCLUDE_DIR } from "../common/const";
import { getAllFilesNonRecursive } from "./tree";

/**
 *
 * @returns 返回vscode工作区绝对路径
 * @example /Users/<USER>/kwaipilot-applications
 */
export function getWorkspace(): string | undefined {
  const workspace = vscode.workspace.workspaceFolders?.[0].uri.fsPath;
  return workspace;
}

/**
 *
 * @returns 获取工作区所有的目录 排除Git标准忽略
 */
export function getWorkspaceDir(): string[] {
  const gitManage = getGitManageDir();
  const gitOthersStandard = getGitOthersStandardFilesDir();
  return gitManage.concat(gitOthersStandard).filter((dir) => {
    if (dir === ".") return false;
    const pathSegments = dir.split(path.sep);

    return !GIT_HARD_IGNORE_DID.some(excludeDirName => pathSegments.includes(excludeDirName));
  });
}

/**
 *
 * @returns 获取工作区所有文件 排除一些固定文件
 */
export async function getWorkspaceFiles(): Promise<string[]> {
  const workspace = getWorkspace();
  if (!workspace) return [];
  const files = await getAllFilesNonRecursive(workspace, WORKSPACE_FILE_EXCLUDE_DIR);
  return files;
}

export function getWorkspaceTreeAbsolute(relativePath: string) {
  const workspace = getWorkspace();
  if (!workspace) {
    throw new Error("getWorkspaceTreeAbsolute: workspace is undefined");
  }
  return path.join(workspace, relativePath);
}

/** 获取打开的文件列表 */
export const getOpenFiles = () => {
  const rawData = vscode.window.tabGroups.all;
  const tabs: string[] = [];
  rawData.forEach((grow) => {
    const innerTab = grow?.tabs || [];

    const _tabs = innerTab.filter((tab) => {
      if (typeof tab === "string") {
        return false;
      }
      const input = tab?.input as any;
      if (input?.uri?.scheme !== "file") {
        return false;
      }

      return true;
    });
    const formatTab: string[] = _tabs.map((tab) => {
      const input = tab?.input as any;
      const fsPath = input?.uri?.fsPath;
      const k = vscode.workspace.asRelativePath(fsPath);
      return k;
    });
    tabs.push(...formatTab);
  });

  return Array.from(new Set(tabs));
};

/** 获取打开的文件的目录列表 */
export const getOpenDir = (fi?: string[]) => {
  const files = fi || getOpenFiles();
  const dir = files.map((i) => {
    return path.dirname(vscode.workspace.asRelativePath(i));
  });
  return Array.from(new Set(dir));
};
