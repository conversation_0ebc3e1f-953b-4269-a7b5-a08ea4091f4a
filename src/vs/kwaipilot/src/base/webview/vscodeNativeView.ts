import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import * as vscode from "vscode";

import { StateManager } from "../../common/state";
import { LoggerManager } from "../logger";
import { Bridge } from "@bridge";
import { ReportKeys, ReportOpt } from "shared/lib/misc/logger";
import { WebloggerManager } from "../weblogger";
import { createExtensionRpcContext } from "../bridge/ExtensionRpcContext";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";

export class Webview extends BaseModule {
  _view: vscode.WebviewView;
  rpcContext: IRPCProtocol;
  private readonly _onDidChangeVisibility = new vscode.EventEmitter<void>();
  // todo 暂时先mock，后续看业务逻辑调整为有效 onDidChangeVisibility
  public readonly onDidChangeVisibility = this._onDidChangeVisibility.event;
  private _visible = false;

  constructor(ext: ContextManager) {
    super(ext);
    this._view = {
      webview: this,
      visible: this._visible,
      onDidChangeVisibility: this.onDidChangeVisibility,
    } as unknown as vscode.WebviewView;
    StateManager.initInstance(this.context);
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        send: (message) => {
          this.getBase(Bridge).postOneWayMessage(this as unknown as vscode.Webview, WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data, source) => {
            console.log("kwaipilot onMessage", data);
            if (source !== this._view?.webview) {
              return;
            }
            listener(data);
          });
        },
      },
    });

    vscode.commands.registerCommand(
      "kwaipilot.bridge.postMessageFromUI",
      (message) => {
        if (message.protocol === "callHandler") {
          this.getBase(Bridge).callNativeHandler(
            this as unknown as vscode.Webview,
            message.name,
            message.data,
            message.callbackId,
          );
        }
        else if (message.protocol === "callback") {
          this.getBase(Bridge).handleCallback(message.callbackId, message.data);
        }
        else if (message.protocol === "message") {
          this.getBase(Bridge).handleOneWayMessage(this as unknown as vscode.Webview, message.data);
        }
      },
    );
  }

  /**
   * 显示并聚焦 webivew 页面
   * @returns Promise
   */
  public focus(form: ReportKeys["sidebar_show"]) {
    const param: ReportOpt<"sidebar_show"> = {
      key: "sidebar_show",
      type: form,
    };
    this.getBase(WebloggerManager)?.$reportUserAction(param);
    vscode.commands.executeCommand("kwaiPilotChatWebView.focus");
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  public postMessage(message: any) {
    const commandId = "kwaipilot.bridge.postMessageFromExtension";

    vscode.commands.executeCommand(commandId, message);
  }

  public get visible(): boolean {
    return this._visible;
  }

  public set visible(value: boolean) {
    if (this._visible !== value) {
      this._visible = value;
      this._onDidChangeVisibility.fire();
    }
  }
}
