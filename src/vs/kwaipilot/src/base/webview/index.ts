import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import * as vscode from "vscode";
import { getNonce } from "../../utils/getNonce";

import { StateManager } from "../../common/state";
import { LoggerManager } from "../logger";
import { Bridge } from "@bridge";
import { ConfigManager, WorkspaceStateManager } from "../state-manager";
import { Config, WorkspaceState } from "../state-manager/types";
import { ReportKeys, ReportOpt } from "shared/lib/misc/logger";
import { getThemeInitConfig } from "../../services/theme";
import { WebloggerManager } from "../weblogger";
import { createExtensionRpcContext } from "../bridge/ExtensionRpcContext";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";

// FIXME: 第一次会从代码层面调用打开webview页面，所以忽略第一次上报
let flag = false;

export class Webview extends BaseModule implements vscode.WebviewViewProvider {
  _view?: vscode.WebviewView;
  public provider?: vscode.Disposable;

  rpcContext: IRPCProtocol;

  constructor(ext: ContextManager) {
    super(ext);
    this.registerWebview();
    StateManager.initInstance(this.context);
    setTimeout(() => {
      vscode.commands
        .executeCommand("workbench.view.extension.kwaipilot_sidebar")
        .then(() => {
          // 在cloudev内，自动打开kwaipilot侧边栏
          if (process.env.CLOUDDEV_CONTAINER === "1") {
            flag = true;
            return;
          }
          // 确保 webview 被隐藏
          this.show(false);
          // 切换回文件树视图
          vscode.commands.executeCommand(
            "workbench.files.action.focusFilesExplorer",
          );
        });
    }, 100);
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data, source) => {
            if (source !== this._view?.webview) {
              return;
            }
            listener(data);
          });
        },
        send: (message) => {
          const webview = this._view!.webview;
          this.getBase(Bridge).postOneWayMessage(webview, WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
      },
    });
  }

  /**
   * 判断 webivew 是否初始化完成
   * @returns boolean
   */
  public isReady() {
    return !!this._view;
  }

  public resolveWebviewView(view: vscode.WebviewView) {
    this._view = view;
    view.onDidChangeVisibility(() => {
      if (view.visible && flag) {
        const param: ReportOpt<"sidebar_show"> = {
          key: "sidebar_show",
          type: "ext_click",
        };
        flag = true;
        this.getBase(WebloggerManager)?.$reportUserAction(param);
      }
    });
    view.webview.options = {
      enableScripts: true,
      localResourceRoots: [this.context.extensionUri],
    };
    view.webview.html = this._getHtmlForWebview(
      view.webview,
      this.context.extensionUri,
    );

    view.webview.onDidReceiveMessage((message) => {
      if (message.protocol === "callHandler") {
        this.getBase(Bridge).callNativeHandler(
          view.webview,
          message.name,
          message.data,
          message.callbackId,
        );
      }
      else if (message.protocol === "callback") {
        this.getBase(Bridge).handleCallback(message.callbackId, message.data);
      }
      else if (message.protocol === "message") {
        this.getBase(Bridge).handleOneWayMessage(view.webview, message.data);
      }
    });
  }

  public revive(panel: vscode.WebviewView) {
    this._view = panel;
  }

  /**
   * 显示 webview
   */
  public show(v: boolean) {
    if (!this._view) {
      console.log("show webview 未初始化");
      return;
    }
    this._view.show(v);
  }

  /**
   * 显示并聚焦 webivew 页面
   * @returns Promise
   */
  public focus(form: ReportKeys["sidebar_show"]) {
    const param: ReportOpt<"sidebar_show"> = {
      key: "sidebar_show",
      type: form,
    };
    this.getBase(WebloggerManager)?.$reportUserAction(param);
    return vscode.commands.executeCommand("kwaiPilotChatWebView.focus");
  }

  public postMessage(message: any) {
    // this.send(`_OLD_${message.type}`, message);
    this._view?.webview.postMessage(message);
  }

  private registerWebview() {
    this.provider = vscode.window.registerWebviewViewProvider(
      "kwaiPilotChatWebView",
      this,
      { webviewOptions: { retainContextWhenHidden: true } },
    );
    this.context.subscriptions.push(this.provider);

    // 注册打开侧边栏命令
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.Open Kwaipilot Panel", () => {
        vscode.commands.executeCommand(
          "workbench.view.extension.kwaipilot_sidebar",
        );
      }),
    );
  }

  /**
   * 获取webView视图
   * @param webview webView组件
   * @returns
   */
  private _getHtmlForWebview(
    webview: vscode.Webview,
    extensionUri: vscode.Uri,
  ) {
    const inDevelopmentMode
      = this.context.extensionMode === vscode.ExtensionMode.Development;

    const vscMediaUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "webview-ui/assets"))
      .toString();

    const jsUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "bridge/index.js"))
      .toString();

    let scriptUri: string;
    let styleMainUri: string;
    if (!inDevelopmentMode) {
      scriptUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "webview-ui/build/assets/index.js"),
        )
        .toString();
      styleMainUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "webview-ui/build/assets/index.css"),
        )
        .toString();
    }
    else {
      scriptUri = "http://localhost:5173/src/index.tsx";
      styleMainUri = "http://localhost:5173/src/App.css";
    }
    this.logger.info("init webview", "webview", {
      value: {
        scriptUri,
        styleMainUri,
        vscMediaUrl,
      },
    });
    const nonce = getNonce();
    const proxyUrl = this.getBase(ConfigManager).get(Config.PROXY_URL);

    const injectTheme = getThemeInitConfig() ?? {};
    const composerSessionId = this.getBase(WorkspaceStateManager).get(WorkspaceState.ACTIVE_COMPOSER_SESSION_ID) ?? "";

    return `<!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleMainUri}" rel="stylesheet">
        <script>window.vscMediaUrl = "${vscMediaUrl}"</script>
        <script>window.proxyUrl = "${proxyUrl}"</script>
        <script>window.composerSessionId = "${composerSessionId}"</script>
        <script>window.ide = "vscode"</script>
        <script>window.__injectTheme__ = ${JSON.stringify(injectTheme)}</script>
        <script>window.colorThemeName = "dark"</script>
        <script nonce="${nonce}" src="${jsUrl}"></script>

        <title>Continue</title>
      </head>
      <body data-element-type="body">
        <div id="root" data-element-type="root"></div>
        ${
          inDevelopmentMode
            ? `<script type="module">
          import RefreshRuntime from "http://localhost:5173/@react-refresh"
          RefreshRuntime.injectIntoGlobalHook(window)
          window.$RefreshReg$ = () => {}
          window.$RefreshSig$ = () => (type) => type
          window.__vite_plugin_react_preamble_installed__ = true
          </script>
          <script type="module">
            import { createHotContext } from "http://localhost:5173/@vite/client"
            window.__vite_hot_context__ = createHotContext()
          </script>`
            : ""
        }
        <script type="module" nonce="${nonce}" src="${scriptUri}"></script>
      </body>
    </html>`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
