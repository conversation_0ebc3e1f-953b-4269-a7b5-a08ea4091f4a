import * as vscode from "vscode";
import { ExtractNativeBridgePayload, ExtractNativeBridgeResult, NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME, WebviewBridgeParams } from "../../shared/types/bridge";

/**
 * Bridge接口定义了本地代码与Webview之间通信的核心方法
 */
export interface IBridge {
  /**
   * 注册处理程序，用于处理来自Webview的请求
   * @param handlerName 处理程序名称
   * @param handler 处理函数
   */
  registerHandler<T extends NATIVE_BRIDGE_EVENT_NAME>(
    handlerName: T,
    handler: (data?: ExtractNativeBridgePayload<T>) => ExtractNativeBridgeResult<T> | Promise<ExtractNativeBridgeResult<T>>
  ): void;

  /**
   * 注册单向消息处理程序
   * @param handlerName 处理程序名称
   * @param handler 处理函数
   */
  registerOneWayMessageHandler<T extends NATIVE_BRIDGE_EVENT_NAME>(
    handlerName: T,
    handler: (data: ExtractNativeBridgePayload<T>) => void
  ): void;

  // 插件调用视图的handler
  callHandler<T extends WEBVIEW_BRIDGE_EVENT_NAME>(webview: vscode.Webview, handlerName: T, data: WebviewBridgeParams[T], callback?: (data: any) => void): void;

  // 视图调用插件handler
  callNativeHandler(webview: vscode.Webview, handlerName: string, data: any, callbackId: string): Promise<void>;

  /**
   * 发送单向消息给Webview
   * @param event 事件名称
   * @param payload 传递的数据
   */
  postOneWayMessage(
    webview: vscode.Webview,
    event: WEBVIEW_BRIDGE_EVENT_NAME,
    payload: WebviewBridgeParams[WEBVIEW_BRIDGE_EVENT_NAME]
  ): void;

  handleOneWayMessage<T extends NATIVE_BRIDGE_EVENT_NAME>(
    webview: vscode.Webview,
    data: { event: T; payload: ExtractNativeBridgePayload<T> },
  ): void;

}
