import { debounceTime, map, Observable, share, startWith, switchMap, of } from "rxjs";
import * as vscode from "vscode";
import { getSelectionOrFileContext } from "./selection";
import { displayPathBasename, expandToLineRange, SelectionOrFileContext } from "shared";
import { WebviewView } from "vscode";
import EventEmitter from "events";

export const NO_INITIAL_VALUE = Symbol("noInitialValue");
/**
 * Create an Observable from a VS Code event. If {@link getInitial} is provided, the Observable will
 * emit the initial value upon subscription (unless it's {@link NO_INITIAL_VALUE}).
 */
export function fromVSCodeEvent<T>(
  event: vscode.Event<T>,
  getInitialValue?: () => T | typeof NO_INITIAL_VALUE | Promise<T | typeof NO_INITIAL_VALUE>,
): Observable<T> {
  return new Observable((observer) => {
    if (getInitialValue) {
      const initialValue = getInitialValue();
      if (initialValue instanceof Promise) {
        initialValue.then((value) => {
          if (value !== NO_INITIAL_VALUE) {
            observer.next(value);
          }
        });
      }
      else {
        if (initialValue !== NO_INITIAL_VALUE) {
          observer.next(initialValue);
        }
      }
    }

    let disposed = false;
    const eventDisposable = event((value) => {
      if (!disposed) {
        observer.next(value);
      }
    });

    return () => {
      disposed = true;
      eventDisposable.dispose();
      observer.complete();
    };
  });
}

export function fromEventEmitter<T>(
  eventEmitter: EventEmitter,
  eventName: string,
  getInitialValue?: () => T | typeof NO_INITIAL_VALUE | Promise<T | typeof NO_INITIAL_VALUE>,
): Observable<T> {
  return new Observable((observer) => {
    if (getInitialValue) {
      const initialValue = getInitialValue();
      if (initialValue instanceof Promise) {
        initialValue.then((value) => {
          if (value !== NO_INITIAL_VALUE) {
            observer.next(value);
          }
        });
      }
      else {
        if (initialValue !== NO_INITIAL_VALUE) {
          observer.next(initialValue);
        }
      }
    }

    let disposed = false;
    const eventHandler = (value: T) => {
      if (!disposed) {
        observer.next(value);
      }
    };

    eventEmitter.on(eventName, eventHandler);

    return () => {
      disposed = true;
      eventEmitter.off(eventName, eventHandler);
      observer.complete();
    };
  });
}

const activeTextEditor = fromVSCodeEvent(
  vscode.window.onDidChangeActiveTextEditor,
  () => vscode.window.activeTextEditor,
).pipe(share());

// const x: numeber = "sdf";

export function getCurrentFileOrSelection(): Observable<SelectionOrFileContext | null> {
  /**
   * If the active text editor changes, this observable immediately emits.
   *
   * If *only* the active selection changes, it debounces 200ms before emitting so we don't spam a
   * bunch of minor updates as the user is actively moving their cursor or changing their
   * selection.
   */
  const selectionOrFileChanged = activeTextEditor.pipe(
    switchMap(() =>
      fromVSCodeEvent(vscode.window.onDidChangeTextEditorSelection).pipe(
        debounceTime(200),
        startWith(undefined),
      ),
    ),
  );
  const selectionOrFileContext = selectionOrFileChanged.pipe(
    switchMap(() => getSelectionOrFileContext()),
  );

  return selectionOrFileContext.pipe(
    switchMap(
      (selectionOrFileContext): Observable<SelectionOrFileContext | null> => {
        if (selectionOrFileContext) {
          // Always add the current file item
          return of({
            ...selectionOrFileContext,
            type: "file",
            description: displayPathBasename(selectionOrFileContext.uri),
            range: selectionOrFileContext.range ? expandToLineRange(selectionOrFileContext.range) : undefined,
          });
        }
        return of(null);
      },
    ),
  );
}

export function getVisibilityOfWebviewView(webview: WebviewView): Observable<boolean> {
  return fromVSCodeEvent<boolean | void>(webview.onDidChangeVisibility, () => webview.visible).pipe(
    map(() => webview.visible),
  );
}
