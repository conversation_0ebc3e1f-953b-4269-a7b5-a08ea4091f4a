import { WorkspaceConfiguration, Memento, workspace } from "vscode";

import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import { Config, GlobalState, StateReturnType, configScope } from "./types";
import { LoggerManager } from "../logger";
import { DefaultBaseUrl } from "../../const";
import fetch from "node-fetch";
import { ExtensionConfigShape } from "shared/lib/bridge/protocol";
import { defaultConfig } from "shared/lib/state-manager/types";

type WatchOption = {
  immediate?: boolean;
};

const configsuffix = "kwaipilot_HnUcVrusC_setting_";

class StateManager<T extends keyof StateReturnType> extends BaseModule {
  state: WorkspaceConfiguration | Memento;
  private watcherMap: Map<string, Set<() => any>> = new Map();
  private scope = "";

  constructor(ext: ContextManager, type: T) {
    super(ext);
    if (type === "config") {
      this.scope = configsuffix;
    }
    this.state = type === "config" ? this.context.globalState : type === "globalState" ? this.context.globalState : this.context.workspaceState;
  }

  get<K extends keyof StateReturnType[T]>(key: K, defaultValue: StateReturnType[T][K]): StateReturnType[T][K];
  get<K extends keyof StateReturnType[T]>(key: K): StateReturnType[T][K] | undefined;
  get<K extends keyof StateReturnType[T]>(key: K, defaultValue?: StateReturnType[T][K]): StateReturnType[T][K] | undefined {
    if (typeof key !== "string") {
      this.logger.error(`get value error`, "state-manager", { reason: `not a vaild key: ${key.toString()}` });
      throw Error(`getStateError key-${key.toString()} is not vaild key`);
    }
    const fullKey = this.scope + key;
    if (key === Config.PROXY_URL) {
      return this.state.get(fullKey) === "" ? (DefaultBaseUrl as any) : this.state.get(fullKey) === undefined ? defaultValue : this.state.get(fullKey);
    }
    return this.state.get(fullKey) === undefined ? defaultValue : this.state.get(fullKey);
  }

  async update<K extends keyof StateReturnType[T]>(key: K, value: StateReturnType[T][K]) {
    if (typeof key !== "string") {
      return;
    }
    const fullKey = this.scope + key;
    const oldVal = this.state.get(fullKey);
    if (!this.scope && JSON.stringify(oldVal) === JSON.stringify(value)) {
      return;
    }
    if (key === Config.PROXY_URL && !value) {
      await this.state.update(fullKey, DefaultBaseUrl);
    }
    else {
      await this.state.update(fullKey, value);
    }
    // 对外感知无前缀
    this.emit(key, value, oldVal);
  }

  watch<K extends keyof StateReturnType[T]>(
    key: K,
    cb: (val?: StateReturnType[T][K], oldVal?: StateReturnType[T][K]) => void,
    opt: WatchOption = {},
  ) {
    if (typeof key !== "string") {
      this.logger.error(`watch error`, "state-manager", { reason: `not a vaild key: ${key.toString()}` });
      throw Error(`getStateError key-${key.toString()} is not support key`);
    }

    this.removeListener(key as string, cb);

    this.on(key as string, cb);

    if (!this.watcherMap.has(key as string)) {
      this.watcherMap.set(key as string, new Set());
    }
    this.watcherMap.get(key as string)?.add(cb);

    if (opt?.immediate) {
      cb(this.get(key));
    }

    return () => {
      this.removeListener(key as string, cb);
      this.watcherMap.get(key as string)?.delete(cb);
    };
  }

  get logger() {
    return this.getBase(LoggerManager);
  }
}

export class GlobalStateManager extends StateManager<"globalState"> implements ExtensionConfigShape {
  constructor(ext: ContextManager) {
    super(ext, "globalState");
    this.setMaxListeners(20);
  }
}
export class WorkspaceStateManager extends StateManager<"workspaceState"> implements ExtensionConfigShape {
  constructor(ext: ContextManager) {
    super(ext, "workspaceState");
  }
}
export class ConfigManager extends StateManager<"config"> {
  constructor(ext: ContextManager) {
    super(ext, "config");

    // 初始化时检查一次 PROXY_URL
    this.initProxyUrl();
    if (this.getBase(GlobalStateManager).get(GlobalState.TO_SYNC, true)) {
      this.syncConfig();
      this.getBase(GlobalStateManager).update(GlobalState.TO_SYNC, false);
    }

    workspace.onDidChangeConfiguration((e) => {
      if (e.affectsConfiguration(configScope)) {
        const config = workspace.getConfiguration(configScope);
        Object.entries(config.settings).forEach(([key, value]) => {
          const fullKey = `${configsuffix}${key}`;
          this.emit(fullKey, value);
        });
      }
    });
  }

  get<K extends keyof StateReturnType["config"]>(key: K): StateReturnType["config"][K] {
    if (typeof key !== "string") {
      this.logger.error(`get value error`, "state-manager", { reason: `not a vaild key: ${key}` });
      throw Error(`getStateError key-${key} is not vaild key`);
    }
    const fullKey = configsuffix + key;

    return this.state.get(fullKey) === undefined ? defaultConfig[key] : this.state.get(fullKey) as StateReturnType["config"][K];
  }

  private syncConfig() {
    const config = workspace.getConfiguration(configScope);
    if (!config.settings) {
      this.update(Config.PROXY_URL, DefaultBaseUrl);
      return;
    }
    Object.entries(config.settings).forEach(([key, value]) => {
      this.update(key as any, value);
    });
  }

  private async initProxyUrl() {
    // const config = workspace.getConfiguration(configScope);
    // const currentUrl = config.get(Config.PROXY_URL);
    try {
      const response = await fetch(DefaultBaseUrl);
      const data = await response.json();
      if (data.error_code === 40314) {
        await this.update(Config.PROXY_URL, "https://kinsight.corp.kuaishou.com");
        return;
      }
    }
    catch (error: any) {
      this.logger.warn(`Failed to check DefaultBaseUrl: ${DefaultBaseUrl} ${error.message}`, "config-manager", { err: error });
    }
  }

  get logger() {
    return this.getBase(LoggerManager);
  }
}
