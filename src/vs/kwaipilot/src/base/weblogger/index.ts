import { Weblog } from "@ks/weblogger/lib/log.core";
import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import pkg from "../../../package.json";
import { LoggerManager } from "../logger";
import { version, ExtensionMode } from "vscode";
import { LoggerCustomEventStructure, ReportKeys } from "shared/lib/misc/logger";
import * as os from "os";
import { ExtensionWebloggerShape } from "shared/lib/bridge/protocol";
import { GlobalStateManager } from "../state-manager";
import { GlobalState } from "../state-manager/types";
import * as vscode from "vscode";

export class WebloggerManager extends BaseModule implements ExtensionWebloggerShape {
  private _weblogger: any;
  constructor(ext: ContextManager) {
    super(ext);
    this.initWeblogger();
  }

  $reportUserAction<T extends keyof ReportKeys>(param: {
    key: T;
    type?: ReportKeys[T];
    chatId?: string;
    sessionId?: string;
    content?: string;
    operator?: string;
    subType?: string;
    applyId?: string;
  }) {
    try {
      const { key, type, chatId, sessionId, content, operator, subType, applyId } = param;
      this._logger.info(
        "report user action", "weblogger", {
          value: {
            key,
            type,
          },
        },
      );
      this._weblogger?.collect("CUSTOM", {
        key,
        value: {
          conversationId: sessionId || "",
          time: new Date().valueOf(),
          operator: operator || "",
          type,
          content: content || "",
          subType: subType || "",
          chatId: chatId || "",
          applyId: applyId || "",
          ...this.getEnvironmentInfoForCustomEvent(),
        } satisfies LoggerCustomEventStructure,
      });
    }
    catch (error) {
      console.error("reportUserAction error:", error);
    }
  }

  isInWorkspace = Boolean(vscode.workspace.workspaceFile);

  getEnvironmentInfoForCustomEvent(): Pick<LoggerCustomEventStructure, "platform" | "release" | "arch" | "machine" | "hostname" | "ideVersion" | "pluginVersion" | "device" | "isInWorkspace"> {
    let machine = "";
    try {
      machine = os.machine();
    }
    catch (error) {
      // os.machine() 可能在旧版本 Node.js 中不存在
      machine = "unknown";
    }
    return {
      platform: os.platform(),
      release: os.release(),
      arch: os.arch(),
      machine: machine,
      hostname: os.hostname(),
      ideVersion: version,
      pluginVersion: pkg.version,
      device: "kwaipilot-vscode",
      isInWorkspace: this.isInWorkspace,
    };
  }

  public updateWeblogUserInfo(userId: string) {
    this._weblogger.updateCommonPackage({
      user_id: userId,
    });
  }

  public sendNodeClick(action: string, params?: any) {
    this._logger.info(`sendNodeClick: ${action}`, "weblogger", {
      value: {
        action,
        params,
      },
    });
    this._weblogger?.collect("CLICK", {
      action: action,
      params: params,
    });
  }

  public sendNodePV(page: string, params?: any) {
    this._weblogger?.collect("PV", {
      type: "enter",
      page: page,
      params: params,
    });
  }

  /**
   * @todo 改为 ext 获取logger
   */
  private get _logger() {
    return this.getBase(LoggerManager);
  }

  private initWeblogger() {
    if (this._weblogger) {
      return;
    }
    const userInfo = this.context.globalState.get("userSsoInfo");
    const _deviceId
      = this.getBase(GlobalStateManager).get(GlobalState.DEVICE_ID);

    const isDev = this.context.extensionMode === ExtensionMode.Development;
    this._weblogger = new Weblog(
      // 初始化参数配置
      {
        env: isDev ? "logger" : "production", // 生产环境埋点
      },
      // 埋点基础信息配置
      {
        product_name: "data_kwaipilot", // 当前埋点产品的 product_name
        user_id: (userInfo as any)?.name || "KwaiStaff",
        device_id: _deviceId,
      },
    );
  }
}
