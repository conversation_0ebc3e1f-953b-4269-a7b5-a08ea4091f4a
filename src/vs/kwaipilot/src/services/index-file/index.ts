import { ExtensionIndexFileShape } from "shared/lib/bridge/protocol";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";

import * as vscode from "vscode";
import { FromIdeProtocol } from "shared/lib/LocalService";
import { LocalService } from "../../core/localService";
import { GlobalStateManager, WorkspaceStateManager } from "../../base/state-manager";
import { GlobalState, WorkspaceState } from "shared/lib/state-manager/types";
import { DEFAULT_INDEX_SPACE } from "shared/lib/const/index";
import { LoggerManager } from "../../base/logger";
import * as fs from "fs";
import * as path from "path";
import { Project } from "../../core/project";
import { IndexState } from "shared";

export class IndexFileService extends ServiceModule implements ExtensionIndexFileShape {
  private readonly loggerScope = "IndexFileService";
  private pauseIndexManual = false;
  indexState = this.workspaceState.get(WorkspaceState.INDEX_STATE, {
    indexed: false,
    indexing: false,
    indexingProgress: 0,
    indexingMessage: "",
    lastBuildTime: "",
    pauseIndexManual: false,
    status: "paused",
  });

  constructor(ext: ContextManager) {
    super(ext);
    this.registryLocalServiceListener();
  }

  $openSystemSettings(): void {
    vscode.commands.executeCommand("workbench.action.openSettings", "@ext:kuaishou.kwaipilot");
  }

  $openIndexIgnore() {
    const workspaceFolder = vscode.workspace.workspaceFolders;
    if (!workspaceFolder || workspaceFolder.length === 0) {
      vscode.window.showErrorMessage("No workspace folder found");
      return;
    }
    const rootPath = workspaceFolder[0].uri.fsPath;
    const kwaipilotDir = path.join(rootPath, ".kwaipilot");
    const indexIgnoreFile = path.join(kwaipilotDir, ".indexignore");
    const gitignoreFile = path.join(rootPath, ".gitignore");

    // Check if .kwaipilot directory exists, if not create it
    if (!fs.existsSync(kwaipilotDir)) {
      fs.mkdirSync(kwaipilotDir, { recursive: true });
    }

    // Check if .indexignore file exists, if not create it and write default content
    if (!fs.existsSync(indexIgnoreFile)) {
      fs.writeFileSync(indexIgnoreFile, `# 添加需要忽略构建索引的文件目录或文件类型（例如 bin/a.js 或者 *.csv），并通过换行分隔；
# 如需仅构建指定目录如/src，可以使用 * 和 !/src 的组合`);
    }

    // Check if .indexignore is already in .gitignore, if not add it
    if (fs.existsSync(gitignoreFile)) {
      const gitignoreContent = fs.readFileSync(gitignoreFile, "utf-8");
      if (!gitignoreContent.includes(".kwaipilot/.indexignore")) {
        fs.appendFileSync(gitignoreFile, "\n.kwaipilot/.indexignore");
      }
    }
    else {
      fs.writeFileSync(gitignoreFile, ".kwaipilot/.indexignore");
    }

    // Open the .indexignore file
    vscode.workspace.openTextDocument(indexIgnoreFile).then((doc) => {
      vscode.window.showTextDocument(doc);
    });
  }

  $getIsRepo() {
    return !!this.project.getRepoPath();
  }

  private registryLocalServiceListener() {
    this.localService.onMessage("index/progress", ({ data }) => {
      if (this.pauseIndexManual) {
        return undefined;
      }
      this.updateProgress(data);
      return undefined;
    });
    this.localService.onMessage("state/sendNotification", ({ data }) => {
      const { type, message } = data;
      switch (type) {
        case "error":
          // 通知webview
          vscode.window.showErrorMessage(message);
          break;
        case "warning":
          vscode.window.showWarningMessage(message);
          break;
        case "info":
          vscode.window.showInformationMessage(message);
          break;
      }
      return undefined;
    });
  }

  async updateProgress(data: { progress: number; action?: "INDEX_FILE" | "PROCESS_FILE" | "ERROR"; message?: string }) {
    // 创建一个新的对象而不是修改当前对象
    const newIndexState: IndexState = {
      ...this.indexState,
      indexingProgress: data.progress,
      indexing: data.progress < 1 && data?.action !== "ERROR",
      indexed: data.progress === 1,
      indexingMessage: data.message || "",
      status: data.action === "ERROR" ? "error" : data.progress === 1 ? "indexed" : "indexing",
    };

    if (newIndexState.indexed) {
      newIndexState.lastBuildTime = new Date().toLocaleString().replace(/\//g, "-");
    }

    await this.workspaceState.update(WorkspaceState.INDEX_STATE, newIndexState);

    this.indexState = newIndexState;
  }

  $getIndexState() {
    return this.indexState;
  }

  $startBuildIndex() {
    this.$rebuildIndex();
  }

  async $deleteIndex() {
    if (!(await this.confirm("删除代码索引将影响仓库代码问答准确性，确定删除代码索引？"))) {
      return false;
    }
    this._deleteIndex();

    const newIndexState: IndexState = {
      ...this.indexState,
      indexing: false,
      indexed: false,
      lastBuildTime: "",
      indexingProgress: 0,
      status: "paused",
    };

    // 先更新状态管理器
    await this.workspaceState.update(WorkspaceState.INDEX_STATE, newIndexState);

    // 然后再更新本地状态
    this.indexState = newIndexState;

    return true;
  }

  async $rebuildIndex() {
    this._deleteIndex();
    this._repoIndex();

    const newIndexState: IndexState = {
      ...this.indexState,
      pauseIndexManual: false,
      indexing: true,
      status: "indexing",
    };

    await this.workspaceState.update(WorkspaceState.INDEX_STATE, newIndexState);

    this.indexState = newIndexState;
  }

  async $stopIndex() {
    this._pauseIndex();

    const newIndexState: IndexState = {
      ...this.indexState,
      pauseIndexManual: true,
      indexing: false,
      status: "paused",
    };

    await this.workspaceState.update(WorkspaceState.INDEX_STATE, newIndexState);

    this.indexState = newIndexState;
  }

  async confirm(message: string) {
    const res = await vscode.window.showWarningMessage(message, { modal: true }, "确认");
    return res === "确认";
  }

  private _deleteIndex() {
    this.localService.request("index/clearIndex", undefined);
  }

  private _repoIndex() {
    this.pauseIndexManual = false;
    this.localService.sendMessage("index/repoIndex", undefined);
  }

  private _pauseIndex() {
    this.pauseIndexManual = true;
    this.localService.sendMessage("index/pause", undefined);
  }

  private _startIndex() {
    this.localService.sendMessage("index/build", undefined);
  }

  $setMaxSpaceSize(maxIndexSpace: number) {
    this.globalState.update(GlobalState.MAX_INDEX_SPACE, maxIndexSpace);
  }

  $getMaxSpaceSize() {
    return this.globalState.get(GlobalState.MAX_INDEX_SPACE, DEFAULT_INDEX_SPACE);
  }

  /**
   * 透传webview消息到local-service
   * @param messageType 消息类型
   * @param data 消息数据
   * @returns 返回local-service的处理结果
   */
  async $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]> {
    try {
      // 使用request方法发送请求并等待响应
      const result = await this.localService.request(messageType, data);
      return result;
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`透传消息到local-service失败: ${error.message || error}`);
      throw error;
    }
  }

  private get localService() {
    return this.ext.getCore(LocalService);
  }

  private get workspaceState() {
    return this.ext.getBase(WorkspaceStateManager);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get project() {
    return this.getCore(Project);
  }
}
