import * as vscode from "vscode";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { Ast } from "../../core/ast";
import { ConfigManager } from "../../base/state-manager";
import { Config } from "../../base/state-manager/types";
import { LoggerManager } from "../../base/logger";

export class CodeBlockActionProvider extends ServiceModule implements vscode.CodeLensProvider {
  private codeLenses: vscode.CodeLens[] = [];
  private _eventEmitter: vscode.EventEmitter<void>
    = new vscode.EventEmitter<void>();

  onDidChangeCodeLenses: vscode.Event<void> = this._eventEmitter.event;

  constructor(ext: ContextManager) {
    super(ext);
    vscode.workspace.onDidChangeConfiguration((_) => {
      this._eventEmitter.fire();
    });
  }

  private async parseAndGenerate(document: vscode.TextDocument) {
    const userInfo = this.context.globalState.get<{ name: string }>("userSsoInfo");
    if (!userInfo) {
      this.logger.info("not login", "code-action:codeLens");
      return [];
    }

    const filePath = document.uri.fsPath;
    const language = document.languageId;
    const supportFileType = ["javascript", "typescript", "vue", "typescriptreact", "javascriptreact"];
    if (!supportFileType.includes(language)) {
      return [];
    }

    const sectionList = this.ast.getAstByDocument(document);
    sectionList.filter(({ lineEnd, lineStart }) => lineEnd - lineStart + 1 > 22).forEach((section) => {
      const range = new vscode.Range(section.lineStart - 1, 0, section.lineEnd - 1, 0);
      const codeLens1 = new vscode.CodeLens(range);
      const codeLens2 = new vscode.CodeLens(range);
      const codeLens3 = new vscode.CodeLens(range);
      const codeLens4 = new vscode.CodeLens(range);
      const codeLens5 = new vscode.CodeLens(range);

      codeLens1.command = {
        title: "函数注释",
        tooltip: "点击生成函数注释",
        command: "kwaipilot.generateFunctionComment",
        arguments: [section, filePath],
      };
      codeLens2.command = {
        title: "行间注释",
        tooltip: "点击生成行间注释",
        command: "kwaipilot.generateLineComment",
        arguments: [section, filePath],
      };
      codeLens3.command = {
        title: "代码解释",
        tooltip: "点击进行代码解释",
        command: "kwaipilot.generateCodeExplanation",
        arguments: [section, filePath],
      };
      codeLens4.command = {
        title: "函数拆分",
        tooltip: "点击进行函数拆分",
        command: "kwaipilot.generateFunctionSplit",
        arguments: [section, filePath],
      };
      codeLens5.command = {
        title: "调优建议",
        tooltip: "点击进行调优建议",
        command: "kwaipilot.generateTuningSuggestion",
        arguments: [section, filePath],
      };
      // 计算函数的行数
      const funcLineCnt = section.lineEnd - section.lineStart;
      // 计算函数的代码字符数
      const funcCodeCharCnt = section.offsetEnd - section.offsetStart;
      if (funcLineCnt > 20 && funcCodeCharCnt < 4500) {
        this.codeLenses.push(codeLens1, codeLens2, codeLens3, codeLens4, codeLens5);
      }
      else {
        this.codeLenses.push(codeLens1, codeLens2, codeLens3, codeLens5);
      }
    });
    return this.codeLenses;
  }

  public provideCodeLenses(document: vscode.TextDocument, _token: vscode.CancellationToken): vscode.CodeLens[] | Thenable<vscode.CodeLens[]> {
    this.codeLenses = [];
    if (!this.config.get(Config.ENABLE_CODE_BLOCK_ACTION)) {
      return [];
    }

    const filePath = document.uri.fsPath;

    if (
      filePath.endsWith(".ts")
      || filePath.endsWith(".js")
      || filePath.endsWith(".mjs")
      || filePath.endsWith(".jsx")
      || filePath.endsWith(".tsx")
      || filePath.endsWith(".vue")
    ) {
      return this.parseAndGenerate(document);
    }
    return this.codeLenses;
  }

  public resolveCodeLens(codeLens: vscode.CodeLens, _token: vscode.CancellationToken) {
    if (!this.config.get(Config.ENABLE_CODE_BLOCK_ACTION)) {
      this.logger.info("code action not open", "code-action:codeLens");
      return null;
    }
    return codeLens;
  }

  get ast() {
    return this.getCore(Ast);
  }

  get config() {
    return this.getBase(ConfigManager);
  }

  get logger() {
    return this.getBase(LoggerManager);
  }
}
