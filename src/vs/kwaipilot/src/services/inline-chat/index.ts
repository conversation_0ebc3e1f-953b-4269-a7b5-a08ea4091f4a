import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { Webview } from "@webview";
import { WebloggerManager } from "../../base/weblogger";
import { LoggerManager } from "../../base/logger";
import { GlobalStateManager } from "../../base/state-manager";
import * as vscode from "vscode";
import { GlobalState } from "../../base/state-manager/types";
import { Project } from "../../core/project";
import { setupInlineTips } from "./tips";
import { InlineChatInfo } from "../../shared/types";
import { DiffModule } from "../../core/diff";
import { Bridge } from "@bridge";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { WriteToFileService } from "../write-to-file";
import { InlineChatMessageEndTag } from "../../shared/constant";

import fs from "fs/promises";
import { DIFF_VIEW_URI_SCHEME } from "../../core/diff/diffViewProvider";

// 当前活跃的消息处理器
let activeMessageProcessor: MessageProcessor | null = null;

export class InlineChatService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.globalState.update(GlobalState.INLINE_CHAT_INFO, undefined);
    this.registryListener(ext.context);
    setupInlineTips(ext.context);
  }

  registryListener(context: vscode.ExtensionContext) {
    // 取消自动更新inlinechat
    // context.subscriptions.push(this.getDisposable());
    context.subscriptions.push(this.getDisposableInlineChatFileChange(context));
    context.subscriptions.push(
      vscode.commands.registerCommand(
        "kwaipilot.inlineChat",
        (param?: InlineChatInfo) => this.handleInlineChat(param),
      ),
    );

    // 监听消息事件
    this.bridge.on(
      NATIVE_BRIDGE_EVENT_NAME.STREAM_DIFF_MESSAGE,
      ({ message, _autoCreate, _autoOpen }) => {
        const inlineChatInfo = this.globalState.get(
          GlobalState.INLINE_CHAT_INFO,
        );
        if (!inlineChatInfo) {
          return;
        }

        // 如果没有活跃的处理器，创建一个新的
        if (!activeMessageProcessor) {
          activeMessageProcessor = new MessageProcessor(
            inlineChatInfo.filepath,
            this.getService(WriteToFileService),
            inlineChatInfo,
          );
        }

        // 如果是结束标记，标记处理完成
        if (message.includes(InlineChatMessageEndTag)) {
          if (activeMessageProcessor) {
            activeMessageProcessor.markAsComplete();
          }
          return;
        }

        // 添加消息到处理器
        activeMessageProcessor.addMessage(message);
      },
    );
  }

  getDisposable() {
    return vscode.window.onDidChangeActiveTextEditor((editor) => {
      const inlineChatInfo = this.globalState.get(GlobalState.INLINE_CHAT_INFO);
      if (!inlineChatInfo) {
        return;
      }

      // 检查是否所有编辑器都已关闭
      if (!editor) {
        if (this.project.getOpenTabsCount() === 0) {
          this.cleanupInlineChat();
        }
        return;
      }
      if (editor.document.uri.scheme === "output" || editor.document.uri.scheme === DIFF_VIEW_URI_SCHEME) {
        return;
      }
      if (editor.document.uri.path !== inlineChatInfo.filepath) {
        this.logger.info("active file change", "inline-chat");
        const initialInlineChatInfo: InlineChatInfo = {
          filepath: editor.document.uri.path,
          startLine: 1,
          endLine: 1,
          content: "",
          uri: editor.document.uri.toString(),
          relativePath: vscode.workspace.asRelativePath(editor.document.uri.fsPath),
        };
        this.globalState.update(
          GlobalState.INLINE_CHAT_INFO,
          initialInlineChatInfo,
        );

        // 只在插件面板可见时更新视图数据
        if (this.webview._view?.visible) {
          this.bridge.setInlineChat(initialInlineChatInfo);
        }
      }
    });
  }

  private cleanupInlineChat() {
    // 清理当前处理器
    activeMessageProcessor = null;
    this.globalState.update(GlobalState.INLINE_CHAT_INFO, undefined);
  }

  getDisposableInlineChatFileChange(context: vscode.ExtensionContext) {
    /** inline chat时行号变化 */
    return vscode.workspace.onDidChangeTextDocument((event) => {
      const inlineChatInfo = this.globalState.get(GlobalState.INLINE_CHAT_INFO);
      if (!inlineChatInfo) {
        return;
      }

      if (event.document.uri.path === inlineChatInfo.filepath) {
        const change = event.contentChanges;
        let contextEndline = inlineChatInfo.endLine;
        for (const c of change) {
          const startIndex = c.range.start.line;

          const endIndex = c.range.end.line;
          const text = c.text;
          if (
            startIndex + 1 >= inlineChatInfo.startLine
            && startIndex + 1 <= contextEndline
          ) {
            // 删除
            if (text === "") {
              const deleteLineNumber = endIndex - startIndex;
              contextEndline = contextEndline - deleteLineNumber;
              if (contextEndline >= inlineChatInfo.startLine) {
                context.globalState.update("inlineChatInfo", {
                  ...inlineChatInfo,
                  endLine: contextEndline,
                });
              }
            }
            // 新增
            if (endIndex === startIndex) {
              const addLineNumber = text.split("\n").length - 1;
              contextEndline = contextEndline + addLineNumber;
              context.globalState.update("inlineChatInfo", {
                ...inlineChatInfo,
                endLine: contextEndline,
              });
            }
          }
        }
      }
    });
  }

  async handleInlineChat(param?: InlineChatInfo) {
    const userInfo = this.globalState.get(GlobalState.USER_INFO);
    if (!userInfo) {
      vscode.window.showErrorMessage("Kwaipilot：请在登录后使用相关功能！");
      return;
    }
    vscode.workspace
      .getConfiguration("kwaipilot")
      .update(
        "settings.showInlineTip",
        false,
        vscode.ConfigurationTarget.Global,
      );
    if (!param?.startLine) {
      // NOTE: pram 为空时，快捷键触发， param.content为空时，右键触发
      this.webview.focus("key_binding");
      this.weblogger.$reportUserAction({
        key: "inline_chat",
        type: "window_show",
      });
      const editor = vscode.window.activeTextEditor;
      if (editor) {
        const selection = editor.selection;
        const text = editor.document.getText(selection);
        // NOTE: 这里行号是从0️⃣开始的，所以需要+1
        const startIndex = selection.start.line;
        const startLine = startIndex + 1;
        const endIndex = selection.end.line;
        const endLine = endIndex + 1;
        const filePath = editor.document.uri.fsPath;
        // 前闭后开,所以 suffix是从 endIndex + 1开始
        const prefix = editor.document.getText(
          new vscode.Range(
            new vscode.Position(0, 0),
            new vscode.Position(startIndex, 0),
          ),
        );
        const suffix = editor.document.getText(
          new vscode.Range(
            new vscode.Position(endIndex + 1, 0),
            new vscode.Position(editor.document.lineCount, 0),
          ),
        );
        const inlineChatInfo = {
          filepath: filePath,
          content: text,
          startLine,
          endLine,
          prefix,
          suffix,
          uri: editor.document.uri.toString(),
          relativePath: vscode.workspace.asRelativePath(filePath),
        };
        this.bridge.setInlineChat(inlineChatInfo);
        this.globalState.update(GlobalState.INLINE_CHAT_INFO, inlineChatInfo);
        // 清理上一个会话的处理器
        activeMessageProcessor = null;
      }
    }
    else {
      const inlineChatInfo = {
        filepath: param.filepath,
        content: param.content,
        startLine: param.startLine,
        endLine: param.endLine,
        prefix: param.prefix,
        suffix: param.suffix,
        uri: param.uri,
        relativePath: param.relativePath,
      };
      this.globalState.update(GlobalState.INLINE_CHAT_INFO, inlineChatInfo);
      this.bridge.setInlineChat(inlineChatInfo);
      // 清理上一个会话的处理器
      activeMessageProcessor = null;
    }
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get project() {
    return this.getCore(Project);
  }

  private get diff() {
    return this.getCore(DiffModule);
  }

  private get bridge() {
    return this.getBase(Bridge);
  }
}

/**
 * 消息处理器 - 处理单个文件的内联聊天消息
 */
class MessageProcessor {
  private message: string = "";
  private prefix: string = "";
  private suffix: string = "";

  constructor(
    private filePath: string,
    private writeToFileService: WriteToFileService,
    private inlineChatInfo: InlineChatInfo,
  ) {
    this.initialize();
  }

  /**
   * 初始化前缀和后缀
   */
  private async initialize() {
    try {
      // 从InlineChatInfo读取前缀和后缀
      if (this.inlineChatInfo.prefix && this.inlineChatInfo.suffix) {
        this.prefix = this.inlineChatInfo.prefix;
        this.suffix = this.inlineChatInfo.suffix;
      }
      else {
        // 从文件中读取
        const content = await fs.readFile(this.filePath, "utf-8");
        const lines = content.split("\n");
        this.prefix = lines.slice(0, this.inlineChatInfo.startLine - 1).join("\n");
        this.suffix = lines.slice(this.inlineChatInfo.endLine).join("\n");
      }
    }
    catch (error) {
      console.error("初始化内联聊天消息处理器失败", error);
    }
  }

  /**
   * 添加消息
   */
  public addMessage(message: string) {
    this.message += message;
  }

  /**
   * 标记处理完成
   */
  public markAsComplete() {
    const relPath = vscode.workspace.asRelativePath(this.filePath);

    // 写入文件
    this.writeToFileService.editFile({
      path: relPath,
      content: `${this.prefix}${this.message}${this.suffix}`,
      isFinal: true,
    });
    activeMessageProcessor = null;
  }
}
