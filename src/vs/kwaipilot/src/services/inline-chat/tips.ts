import * as vscode from "vscode";

let inlineTipDecoration: vscode.TextEditorDecorationType;

function createInlineTipDecoration() {
  return vscode.window.createTextEditorDecorationType({
    after: {
      contentText: `⌘+I 对话式生成代码`,
      color: "rgba(153,153,153,0.35)",
      // 移除左边距
      margin: "0 0 0 1rem",
    },
  });
}

function showInlineTip() {
  return false;
}

function handleSelectionChange(e: vscode.TextEditorSelectionChangeEvent) {
  const selection = e.selections[0];
  const editor = e.textEditor;

  if (editor.document.uri.toString().startsWith("output:")) {
    return;
  }
  if (showInlineTip() === false || selection.isEmpty) {
    editor.setDecorations(inlineTipDecoration, []);
    return;
  }
  const line = selection.active.line;
  const lineText = editor.document.lineAt(line).text;
  const hoverMarkdown = new vscode.MarkdownString(
    `[关闭](command:kwaipilot.hideInlineTip) 行内提示`,
  );
  hoverMarkdown.isTrusted = true;

  editor.setDecorations(inlineTipDecoration, [
    {
      range: new vscode.Range(
        new vscode.Position(line, lineText.length),
        new vscode.Position(line, lineText.length),
      ),
      hoverMessage: [hoverMarkdown],
    },
  ]);
}

let selectionChangeDebounceTimer: NodeJS.Timeout | undefined;

function hideInlineTip() {
  vscode.workspace.getConfiguration("kwaipilot").update("settings.showInlineTip", false, vscode.ConfigurationTarget.Global);
  vscode.window.showInformationMessage("对话式生成代码提示已被禁用。");
  // 清除所有编辑器中的装饰器
  vscode.window.visibleTextEditors.forEach((editor) => {
    editor.setDecorations(inlineTipDecoration, []);
  });
}

export function setupInlineTips(context: vscode.ExtensionContext) {
  inlineTipDecoration = createInlineTipDecoration();

  context.subscriptions.push(
    vscode.commands.registerCommand("kwaipilot.hideInlineTip", hideInlineTip),
    vscode.window.onDidChangeTextEditorSelection((e) => {
      if (selectionChangeDebounceTimer) {
        clearTimeout(selectionChangeDebounceTimer);
      }
      selectionChangeDebounceTimer = setTimeout(() => {
        handleSelectionChange(e);
      }, 200);
    }),
  );
}
