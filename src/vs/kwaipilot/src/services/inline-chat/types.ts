export interface RequestBody {
  username: string;
  platform: string;
  prefix: string;
  suffix: string;
  middle: string;
  language: string;
  prompt: string;
  projectName: string;
  gitRemote: string;
  branchName: string;
  openedFilePath: string;
}

// inline chat用户意图识别
export enum InlineChatUserIntention {
  TEXT,
  EXPLAIN,
  CODE,
}
export interface InlineChatInfo {
  filepath: string;
  startLine: number;
  endLine: number;
  content: string;
  prefix: string;
  suffix: string;
}
