import { Disposable } from "vscode";
import { ArtifactPreviewData } from "../../../shared/types/bridge";

export type NaviStreamEvent = {
  type: "Kwaipilot2Navi-start" | "Kwaipilot2Navi-pending" ;
  data?: ArtifactPreviewData;
} | {
  type: "Kwaipilot2Navi-end";
} | {
  type: "Kwaipilot2Navi-error";
  error?: string;
};

/**
一次流式输出的过程:
1. Kwaipilot2Navi-start, data
2. Kwaipilot2Navi-pending, data
3. Kwaipilot2Navi-pending, data
4. ...
n-1 Kwaipilot2Navi-pending, data
n. Kwaipilot2Navi-end

一次非流式输出的过程(一次性返回所有内容):
1. Kwaipilot2Navi-start, data
2. Kwaipilot2Navi-end
 */

/**
 * Defines the interface for a communication service that can handle stream events.
 */
export interface CommunicationService {

  // 事件监听
  onStreamEvent(listener: (event: NaviStreamEvent) => void): Disposable;
  // 事件发送
  sendStreamEvent(event: NaviStreamEvent): void;
}

export interface ExtensionNaviApi {
  communication: CommunicationService;
}
