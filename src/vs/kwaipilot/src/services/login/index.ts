import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { ConfigManager, GlobalStateManager } from "../../base/state-manager";
import { Config, GlobalState } from "../../base/state-manager/types";
import { WebloggerManager } from "../../base/weblogger";
import { Webview } from "@webview";
import { UserInfo } from "../../shared/types";
import TaskRunner from "../../utils/TaskRunner";
import * as vscode from "vscode";
import fetch from "node-fetch";
import { Bridge } from "@bridge";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { LocalService } from "../../core/localService";

import { v4 as uuidv4 } from "uuid";

export class LoginService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.registryListener();
    this.checkStatus();
  }

  private registryListener() {
    this.getBase(Bridge).registerHandler(NATIVE_BRIDGE_EVENT_NAME.LOGIN, this.login.bind(this));
    vscode.commands.registerCommand("kwaipilot.login", this.login.bind(this));
    vscode.commands.registerCommand("kwaipilot.logout", this.logout.bind(this));
    vscode.commands.registerCommand("kwaipilot.syncUserInfoFromIDE", this.syncUserInfoFromIDE.bind(this));
  }

  private getSSOUrl(deviceId: string) {
    const proxyUrl = this.config.get(Config.PROXY_URL);
    const SSO_URL = "https://sso.corp.kuaishou.com/cas/login?service=";
    const service = proxyUrl.replace("https", "http") + "/eapi/kwaipilot/auth/sso/callback?uuid=" + deviceId + "&redirect=";
    const encodeParam = encodeURIComponent(service) + "/";
    return SSO_URL + encodeParam;
  }

  async login() {
    let deviceId: string | undefined = this.globalState.get(GlobalState.DEVICE_ID);
    if (!deviceId) {
      deviceId = uuidv4();
      this.globalState.update(GlobalState.DEVICE_ID, deviceId);
    }
    const SSOUrl = this.getSSOUrl(deviceId);
    // 登录是通过device_id 和用户绑定方式来实现的
    if (!this.context.globalState.get("kwaipilot.loginDeviceId")) {
      // 未登录过需要执行一次绑定，已登录过直接更新用户信息即可
      // @ts-expect-error openExternal实际支持string地址；对于uri会有自己encode逻辑处理
      vscode.env.openExternal(SSOUrl);
    }
    else {
      this.getUserTokenInfo(this.context).catch(() => {
        // @ts-expect-error openExternal实际支持string地址；对于uri会有自己encode逻辑处理
        vscode.env.openExternal(SSOUrl);
      });
    }

    this.context.globalState.update("kwaipilot.noLoginMessage", undefined);

    const onResolve = (result: UserInfo | undefined) => {
      if (result) {
        // deviceId 绑定无时间限制，绑定过程执行一次即可
        this.context.globalState.update(
          "kwaipilot.loginDeviceId",
          this.context.globalState.get("kwaipilot.loginDeviceId"),
        );
        vscode.commands.executeCommand("setContext", "kwaipilot.isLogin", true);
        vscode.window.showInformationMessage("登录成功！");
        this.globalState.update(GlobalState.USER_INFO, result);
        this.getCore(LocalService).sendMessage("state/userLogin", { username: result.name });
      }
    };

    const onReject = (
      error: any,
      context: { times: number; stop: () => void },
    ) => {
      console.log(`Failure: ${error}, times: ${context.times}`);
    };

    const checkLoginStatusTaskRunner = new TaskRunner<UserInfo | undefined>({
      task: () => this.getUserTokenInfo(this.context),
      onResolve,
      onReject,
      isFirstSuccessStop: true,
      isFirstFailStop: false,
      times: 3,
      timeGap: 4000,
    });

    // 登录之后插件更新登录状态
    checkLoginStatusTaskRunner.run();
  }

  private logout() {
    this.globalState.update(GlobalState.USER_INFO, undefined);
    this.globalState.update(GlobalState.DEVICE_ID, undefined);
    vscode.commands.executeCommand("setContext", "kwaipilot.isLogin", false);
    vscode.window.showInformationMessage("注销成功！");
  }

  async checkStatus() {
    let userInfo: UserInfo | undefined = undefined;
    if (process.env.CLOUDDEV_CONTAINER === "1") {
      userInfo = { name: process.env.CLOUDDEV_USER } as UserInfo;
    }
    else {
      userInfo = this.globalState.get(GlobalState.USER_INFO) || (await this.getUserTokenInfo(this.context));
    }
    if (userInfo) {
      // 已登录，展示用户头像
      this.globalState.update(GlobalState.USER_INFO, userInfo);
      vscode.commands.executeCommand("setContext", "kwaipilot.isLogin", true);
      this.weblogger.updateWeblogUserInfo(userInfo.name);
    }
    else {
      this.showLoginMessage();
    }
  }

  async showLoginMessage() {
    console.log("----checkUserLoginStatus failed----");
    if (this.context.globalState.get("kwaipilot.noLoginMessage")) {
      return;
    }
    const selection = await vscode.window.showInformationMessage("Kwaipilot：请在登录后使用相关功能！", "登录", "不再提示");
    if (selection === "登录") {
      this.login();
    }
    else if (selection === "不再提示") {
      this.context.globalState.update("kwaipilot.noLoginMessage", true);
    }
  }

  private async getUserTokenInfo(
    _context: any,
  ): Promise<UserInfo | undefined> {
    const deviceId = this.globalState.get(GlobalState.DEVICE_ID);
    const proxyUrl = this.config.get(Config.PROXY_URL);
    try {
      const url
        = proxyUrl + "/eapi/kwaipilot/auth/user" + "?uuid=" + deviceId;
      const response = await fetch(url);
      if (response.ok) {
        const data: any = await response.json();
        if (data.data) {
          this.globalState.update(GlobalState.USER_INFO, data.data);
          console.log("Login Succeed");
          return data.data;
        }
        else {
          console.log("Login Fail with response ok", data);
          throw data;
        }
      }
      else {
        console.log("Login Fail", response.status, response.statusText);
        throw response;
      }
    }
    catch (error) {
      console.log(error);
      throw error;
    }
  };

  private syncUserInfoFromIDE(userInfo: UserInfo | undefined) {
    if (userInfo === undefined) {
      this.globalState.update(GlobalState.USER_INFO, undefined);
      this.globalState.update(GlobalState.DEVICE_ID, undefined);
    }
    else {
      this.globalState.update(GlobalState.USER_INFO, userInfo);
    }
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }
}
