import { MessageParam } from "shared/lib/agent/types_copied_from_agent";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";

export class ApiConversationHistoryStorageService extends ServiceModule {
  kwaipilotKV: MemoryCachedKwaipilotKV;
  constructor(ext: ContextManager) {
    super(ext);

    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  async getApiConversationHistory(sessionId: string): Promise<MessageParam[]> {
    const storageKey = this.apiConversationHistoryPersistedStorageKey(sessionId);
    return this.kwaipilotKV.getObject<MessageParam[]>(storageKey, []);
  }

  async setApiConversationHistory(sessionId: string, apiConversationHistory: MessageParam[]) {
    const storageKey = this.apiConversationHistoryPersistedStorageKey(sessionId);
    this.kwaipilotKV.set(storageKey, apiConversationHistory);
  }

  async deleteApiConversationHistory(sessionId: string) {
    const storageKey = this.apiConversationHistoryPersistedStorageKey(sessionId);
    this.kwaipilotKV.delete(storageKey);
  }

  private apiConversationHistoryPersistedStorageKey(sessionKey: string) {
    return `apiConversationHistory:${sessionKey}`;
  }
}
