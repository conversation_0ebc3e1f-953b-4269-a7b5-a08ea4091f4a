import { FilePersistedStateType, InternalLocalMessage_Human, InternalLocalMessage_Tool_EditFile } from "shared/lib/agent/types";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import { ComposerState, LocalMessage } from "shared/lib/agent";
import { isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";
import { OutPutBlockCodePath } from "shared/lib/misc/blockcode";

export type PersistedLocalMessage = InternalLocalMessage_Human
  | Omit<InternalLocalMessage_Tool_EditFile, "workingsetEffect"> & {
    workingsetEffect: Omit<InternalLocalMessage_Tool_EditFile["workingSetEffect"], "status"> & {
      // 不必存储中间状态
      status: FilePersistedStateType;
    };
  }
  | Omit<LocalMessage, "role"> & {
    role: undefined;
  };

// 会话缓存路径信息类型
export type SessionCachePathInfo = OutPutBlockCodePath & {
  order: number;
  cacheKey?: string;
};

export interface PersistedComposerSessionData extends Pick<ComposerState,
  | "currentMessageTs"
  | "editingMessageTs"
  | "sessionId"
  | "workspaceUri"
> {
  localMessages: PersistedLocalMessage[];
  indexed?: boolean;
  // 添加缓存路径信息，在会话加载时预加载
  cachePathInfos?: SessionCachePathInfo[];
  // 缓存路径信息的最后刷新时间戳
  cachePathInfosLastRefresh?: number;
}

export function isPersistedToolEditFileMessage(message: PersistedLocalMessage): message is InternalLocalMessage_Tool_EditFile {
  return isToolEditFileMessage(message);
}

export class ComposerSessionStorageService extends ServiceModule {
  kwaipilotKV: MemoryCachedKwaipilotKV;
  constructor(ext: ContextManager) {
    super(ext);
    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  async getComposerSessionData(sessionId: string): Promise<PersistedComposerSessionData | undefined> {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    return this.kwaipilotKV.getObject<PersistedComposerSessionData>(storageKey);
  }

  /**
   * 获取包含缓存路径信息的会话数据
   * @param sessionId 会话ID
   * @returns 包含缓存路径信息的会话数据
   */
  async getComposerSessionDataWithCache(sessionId: string): Promise<PersistedComposerSessionData | undefined> {
    const sessionData = await this.getComposerSessionData(sessionId);
    if (!sessionData) {
      return undefined;
    }

    // 检查缓存数据的新鲜度
    const shouldRefreshCache = this.shouldRefreshCachePathInfos(sessionData);

    if (!shouldRefreshCache && sessionData.cachePathInfos) {
      // 缓存数据足够新鲜，直接返回
      return sessionData;
    }

    // 需要刷新缓存数据
    try {
      const { BlockCodeService } = await import("./BlockCodeService");
      const blockCodeService = this.getService(BlockCodeService);
      const latestCachePathInfos = await blockCodeService.getAllSessionCachePathInfo({ sessionId });

      // 智能合并新旧缓存数据
      const mergedCachePathInfos = this.mergeCachePathInfos(
        sessionData.cachePathInfos || [],
        latestCachePathInfos as SessionCachePathInfo[],
      );

      // 更新会话数据，包含合并后的缓存路径信息和刷新时间戳
      const updatedSessionData: PersistedComposerSessionData = {
        ...sessionData,
        cachePathInfos: mergedCachePathInfos,
        cachePathInfosLastRefresh: Date.now(), // 添加刷新时间戳
      };

      // 异步保存更新后的数据，不阻塞返回
      this.setComposerSessionData(sessionId, updatedSessionData).catch(error =>
        console.warn(`Failed to update session data with cache info:`, error),
      );

      return updatedSessionData;
    }
    catch (error) {
      console.warn(`Failed to load cache path info for session ${sessionId}:`, error);
      // 如果获取失败，返回原数据（可能包含旧的缓存信息，总比没有好）
      return sessionData;
    }
  }

  /**
   * 判断是否需要刷新缓存路径信息
   * @param sessionData 会话数据
   * @returns 是否需要刷新
   */
  private shouldRefreshCachePathInfos(sessionData: PersistedComposerSessionData): boolean {
    // 如果没有缓存数据，需要刷新
    if (!sessionData.cachePathInfos || sessionData.cachePathInfos.length === 0) {
      return true;
    }

    // 如果没有刷新时间戳，需要刷新
    if (!sessionData.cachePathInfosLastRefresh) {
      return true;
    }

    // 如果距离上次刷新超过5分钟，需要刷新
    const CACHE_REFRESH_INTERVAL = 5 * 60 * 1000; // 5分钟
    const now = Date.now();
    if (now - sessionData.cachePathInfosLastRefresh > CACHE_REFRESH_INTERVAL) {
      return true;
    }

    return false;
  }

  /**
   * 智能合并新旧缓存路径信息
   * @param oldCachePathInfos 旧的缓存路径信息
   * @param newCachePathInfos 新的缓存路径信息
   * @returns 合并后的缓存路径信息
   */
  private mergeCachePathInfos(
    oldCachePathInfos: SessionCachePathInfo[],
    newCachePathInfos: SessionCachePathInfo[],
  ): SessionCachePathInfo[] {
    // 如果新数据为空，保留旧数据
    if (!newCachePathInfos || newCachePathInfos.length === 0) {
      console.log("New cache path infos is empty, keeping old data");
      return oldCachePathInfos;
    }

    // 如果旧数据为空，直接使用新数据
    if (!oldCachePathInfos || oldCachePathInfos.length === 0) {
      console.log("Old cache path infos is empty, using new data");
      return newCachePathInfos;
    }

    // 创建一个 Map 用于去重和合并
    const mergedMap = new Map<string, SessionCachePathInfo>();

    // 先添加旧数据
    for (const oldItem of oldCachePathInfos) {
      const key = this.generateCachePathInfoKey(oldItem);
      mergedMap.set(key, oldItem);
    }

    // 再添加新数据，新数据会覆盖旧数据（优先级更高）
    for (const newItem of newCachePathInfos) {
      const key = this.generateCachePathInfoKey(newItem);
      mergedMap.set(key, newItem);
    }

    // 转换回数组并按优先级排序
    const mergedArray = Array.from(mergedMap.values());
    mergedArray.sort((a, b) => (b.order || 0) - (a.order || 0));

    console.log(`Merged cache path infos: ${oldCachePathInfos.length} old + ${newCachePathInfos.length} new = ${mergedArray.length} total`);

    return mergedArray;
  }

  /**
   * 生成缓存路径信息的唯一键
   * @param pathInfo 缓存路径信息
   * @returns 唯一键
   */
  private generateCachePathInfoKey(pathInfo: SessionCachePathInfo): string {
    // 使用文件路径、起始行、结束行作为唯一标识
    return `${pathInfo.filepath || "unknown"}:${pathInfo.startLine || 0}:${pathInfo.endLine || 0}:${pathInfo.cacheKey || ""}`;
  }

  async setComposerSessionData(sessionId: string, data: PersistedComposerSessionData) {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    this.kwaipilotKV.set(storageKey, data);
  }

  private localMessagePersistedStorageKey(sessionKey: string) {
    return `composerData:${sessionKey}`;
  }

  async deleteSession(sessionId: string) {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    await this.kwaipilotKV.delete(storageKey);
  }
}
