import { SerializedDiagnostic } from "shared/lib/misc/diagnostic";
import { Position, Range, Diagnostic } from "vscode";

export function fromSerializedDiagnostic(serializedDiagnostic: SerializedDiagnostic): Diagnostic {
  const diagnostic = new Diagnostic(
    new Range(
      new Position(
        serializedDiagnostic.range.start.line,
        serializedDiagnostic.range.start.character,
      ),
      new Position(
        serializedDiagnostic.range.end.line,
        serializedDiagnostic.range.end.character,
      ),
    ),
    serializedDiagnostic.message,
    serializedDiagnostic.severity,
  );
  diagnostic.source = serializedDiagnostic.source;
  return diagnostic;
}
