import * as vscode from "vscode";
import { ContextManager } from "../../base/context-manager";
import EventEmitter from "events";

function isVscodeVersionGreaterThanOrEqualTo(version: string): boolean {
  const [major, minor, bug] = vscode.version.split(".").map(Number);
  const [targetMajor, targetMinor, targetBug] = version.split(".").map(Number);
  if (major > targetMajor) return true;
  if (major < targetMajor) return false;
  if (minor > targetMinor) return true;
  if (minor < targetMinor) return false;
  return bug >= targetBug;
}

export class DocumentPasteService implements vscode.DocumentPasteEditProvider {
  emitter = new EventEmitter<{
    copy: [{
      document: vscode.TextDocument;
      ranges: readonly vscode.Range[];
      dataTransfer: vscode.DataTransfer;
    }];
  }>();

  private latestCopiedContent: {
    document: vscode.TextDocument;
    ranges: readonly vscode.Range[];
    dataTransfer: vscode.DataTransfer;
  } | undefined;

  /**
   * Invoked on copy. This allows us to modify the `dataTransfer` that is later passed to {@link provideDocumentPasteEdits}.
   */
  prepareDocumentPaste(
    document: vscode.TextDocument,
    ranges: readonly vscode.Range[],
    dataTransfer: vscode.DataTransfer,
    _token: vscode.CancellationToken,
  ) {
    this.latestCopiedContent = {
      document,
      ranges,
      dataTransfer,
    };
    this.emitter.emit("copy", {
      document,
      ranges,
      dataTransfer,
    });
  }

  constructor(ext: ContextManager) {
    // Register our provider
    const selector: vscode.DocumentSelector = { language: "*" };
    // https://github.com/microsoft/vscode/commit/4daea74ba9cf57292cbc3ef04cfe3a50c234de8a
    if (vscode.languages.registerDocumentPasteEditProvider && isVscodeVersionGreaterThanOrEqualTo("1.97.0")) {
      ext.context.subscriptions.push(vscode.languages.registerDocumentPasteEditProvider(selector, this, {
        // List out all kinds of edits that our provider may return
        providedPasteEditKinds: [],

        // List out all mime types that our provider may add on copy
        copyMimeTypes: [],

        // List out all mime types that our provider should be invoked for on paste
        pasteMimeTypes: ["text/plain"],
      }));
    }
  }

  getLatestCopiedContent(): {
    document: vscode.TextDocument;
    ranges: readonly vscode.Range[];
    dataTransfer: vscode.DataTransfer;
  } | undefined {
    return this.latestCopiedContent;
  }
}
