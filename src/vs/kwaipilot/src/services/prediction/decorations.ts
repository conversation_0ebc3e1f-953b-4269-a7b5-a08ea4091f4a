import * as vscode from "vscode";

export const deleteContentDecorationType = vscode.window.createTextEditorDecorationType({
  backgroundColor: { id: "diffEditor.removedLineBackground" },
  color: "#808080",
  rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

export const newContentDecorationType = vscode.window.createTextEditorDecorationType(
  {
    backgroundColor: { id: "diffEditor.insertedLineBackground" },
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
  },
);

export const selectContentDecorationType = vscode.window.createTextEditorDecorationType({
  backgroundColor: "rgba(255, 255, 255, 0.2)",
  rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

export const newContentAfterDecorationType = vscode.window.createTextEditorDecorationType({
  rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

export const tabTipDecorationType = vscode.window.createTextEditorDecorationType({
  after: {
    color: "rgba(153,153,153,0.6)",
    border: "1px solid rgb(203, 167, 37)",
    margin: "0 0 0 3rem",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
});
