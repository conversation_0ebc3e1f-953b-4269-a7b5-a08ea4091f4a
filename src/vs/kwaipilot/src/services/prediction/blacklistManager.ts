import { Prediction } from "../../base/http-client/interface";

interface BlacklistItem {
  key: string;
  timestamp: number; // 存储秒级时间戳
}

export class BlacklistManager {
  private blacklist: BlacklistItem[] = [];
  private maxSize: number = 1000;
  private expirationTime: number = 60; // 60 seconds
  private _filename: string = "";

  constructor() {}

  public updateConfig(maxSize: number, expirationTimeInSeconds: number) {
    this.maxSize = maxSize;
    this.expirationTime = expirationTimeInSeconds;
  }

  private generateKey(prediction: Prediction): string {
    return `${prediction.sourceBlockContent}`;
  }

  private getCurrentTimestampInSeconds(): number {
    return Math.floor(Date.now() / 1000);
  }

  public add(prediction: Prediction) {
    const key = this.generateKey(prediction);
    const now = this.getCurrentTimestampInSeconds();

    const existingIndex = this.blacklist.findIndex(item => item.key === key);
    if (existingIndex !== -1) {
      return;
    }

    // Add new item
    this.blacklist.push({
      key,
      timestamp: now,
    });

    // Remove oldest if exceeding max size
    if (this.blacklist.length > this.maxSize) {
      this.blacklist.shift();
    }
  }

  public isBlacklisted(prediction: Prediction): boolean {
    const key = this.generateKey(prediction);
    const item = this.blacklist.find(item => item.key === key);

    if (!item) return false;

    const now = this.getCurrentTimestampInSeconds();
    // Check if expired
    if (now - item.timestamp > this.expirationTime) {
      // Update timestamp if expired
      item.timestamp = now;
      return false;
    }

    return true;
  }

  public clear() {
    this.blacklist = [];
  }

  get filename() {
    return this._filename;
  }

  set filename(value: string) {
    this._filename = value;
  }
}
